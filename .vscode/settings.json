{"workbench.colorCustomizations": {"activityBar.activeBackground": "#001b80", "activityBar.background": "#001b80", "activityBar.foreground": "#e7e7e7", "activityBar.inactiveForeground": "#e7e7e799", "activityBarBadge.background": "#bf0028", "activityBarBadge.foreground": "#e7e7e7", "commandCenter.border": "#e7e7e799", "sash.hoverBorder": "#001b80", "statusBar.background": "#00104d", "statusBar.foreground": "#e7e7e7", "statusBarItem.hoverBackground": "#001b80", "statusBarItem.remoteBackground": "#00104d", "statusBarItem.remoteForeground": "#e7e7e7", "titleBar.activeBackground": "#00104d", "titleBar.activeForeground": "#e7e7e7", "titleBar.inactiveBackground": "#00104d99", "titleBar.inactiveForeground": "#e7e7e799"}, "peacock.color": "#00104d", "cSpell.words": ["actionsheet", "ahooks", "aini", "aiservices", "appconfservice", "appn", "appt", "appv", "arithmeticpk", "arrowright", "<PERSON><PERSON><PERSON><PERSON>", "Bookln", "booklnboot", "bule", "buyu", "chen<PERSON>", "ellipsize", "exercisereport", "gulu", "iflytek", "imds", "imserver", "itechserver", "judgecountdown", "judgeright", "kousuanlianxi", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "leftcrown", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "listtypeandgrade", "matchinghead", "mathexam", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mmkv", "modalbox", "netinfo", "oralarithmetic", "<PERSON><PERSON>", "pkdefeat", "pkdraw", "PKDTO", "pkplay", "pksucceed", "Prepub", "Pressable", "qrcode", "querybykey", "Reactotron", "readygo", "rightanswers", "rightcrown", "socketservertcp", "Srts", "tamagui", "tarojs", "templateid", "<PERSON><PERSON>", "unsynthesized", "vsyellow", "<PERSON><PERSON><PERSON>", "wechat", "weixin", "wxmp", "xing<PERSON>g", "xuebihua", "xuepianpang", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yunti", "<PERSON><PERSON><PERSON>"], "editor.defaultFormatter": "biomejs.biome", "notebook.defaultFormatter": "biomejs.biome", "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "biome.lspBin": "", "editor.formatOnSave": true, "tailwindCSS.classAttributes": ["className", "jglClassName"]}