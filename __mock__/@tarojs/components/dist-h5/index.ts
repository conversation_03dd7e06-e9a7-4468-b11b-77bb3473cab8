import reactifyWc from './utils/reactify-wc'
import ReactInput from './components/input'
import {callOnMount} from "../../../onMount";

export var View = reactifyWc('taro-view-core')
export var Icon = reactifyWc('taro-icon-core')
export var Progress = reactifyWc('taro-progress-core')
export var RichText = reactifyWc('taro-rich-text-core')
export var Text = reactifyWc('taro-text-core')
export var Button = reactifyWc('taro-button-core')
export var Checkbox = reactifyWc('taro-checkbox-core')
export var CheckboxGroup = reactifyWc('taro-checkbox-group-core')
export var Editor = reactifyWc('taro-editor-core')
export var Form = reactifyWc('taro-form-core')
export var Input = ReactInput
export var Label = reactifyWc('taro-label-core')
export var Picker = reactifyWc('taro-picker-core', (props) => {
  callOnMount('Picker', props)
})
export var PickerView = reactifyWc('taro-picker-view-core')
export var PickerViewColumn = reactifyWc('taro-picker-view-column-core')
export var Radio = reactifyWc('taro-radio-core')
export var RadioGroup = reactifyWc('taro-radio-group-core')
export var Slider = reactifyWc('taro-slider-core')
export var Switch = reactifyWc('taro-switch-core')
export var CoverImage = reactifyWc('taro-cover-image-core')
export var Textarea = reactifyWc('taro-textarea-core')
export var CoverView = reactifyWc('taro-cover-view-core')
export var MovableArea = reactifyWc('taro-movable-area-core')
export var MovableView = reactifyWc('taro-movable-view-core')
export var ScrollView = reactifyWc('taro-scroll-view-core')
export var Swiper = reactifyWc('taro-swiper-core')
export var SwiperItem = reactifyWc('taro-swiper-item-core')
export var FunctionalPageNavigator = reactifyWc('taro-functional-page-navigator-core')
export var Navigator = reactifyWc('taro-navigator-core')
export var Audio = reactifyWc('taro-audio-core')
export var Camera = reactifyWc('taro-camera-core')
export var Image = reactifyWc('taro-image-core', (props) => {
  callOnMount('Image', props)
})
export var LivePlayer = reactifyWc('taro-live-player-core')
export var Video = reactifyWc('taro-video-core')
export var Map = reactifyWc('taro-map-core')
export var Canvas = reactifyWc('taro-canvas-core')
export var Ad = reactifyWc('taro-ad-core')
export var OfficialAccount = reactifyWc('taro-official-account-core')
export var OpenData = reactifyWc('taro-open-data-core')
export var WebView = reactifyWc('taro-web-view-core')
export var NavigationBar = reactifyWc('taro-navigation-bar-core')
export var Block = reactifyWc('taro-block-core')
export var CustomWrapper = reactifyWc('taro-custom-wrapper-core')
