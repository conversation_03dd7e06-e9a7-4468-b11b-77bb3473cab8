import type { SceneCode } from '@jgl/biz-func';

export type ArrayElement<ArrType> = ArrType extends readonly (infer ElementType)[]
  ? ElementType
  : never;

export type DownloadStatus = {
  status?: number;
  progress?: number;
  total?: number;
  localUri?: string;
};

export const crCodeTypes = {
  // 书籍封面
  virtualBook: 18,
  // 资源码
  resourceType: 28,
  // 目录码
  catalogType: 23,
  // 学习卡
  studyCard: 58,
  // 会员卡
  vipCard: 59,
  // 充值卡
  rechargeCard: 63,
  // 单词书
  wordBook: 64,
  // 生字本总码
  chinese: 69,
  // 生字本文字目录章二维码
  chineseCatalog: 70,
  // 生字本文字目录节二维码
  chineseSection: 71,
  // 口语测评
  spoken: 75,
  // 口语测评，章
  spokenCatalog: 76,
  // 口语测评，节
  spokenSection: 77,
  // 古诗文，全文
  AncientPoemsTypeAll: 79,
  // 古诗文，章
  AncientPoemsTypeCatalog: 80,
  /**
   * 汉语听写总码
   */
  CHINESE_WORDS_QR: 81,
  /**
   *汉字听写目录章
   */
  CHINESE_WORDS_CHAPTER_QR: 82,
  /**
   *汉字听写目录节
   */
  CHINESE_WORDS_SECTION_QR: 83,

  /**
   * 错题本总码
   */
  MISTAKE_BOOK: 94,
  /**
   * 章码
   */
  MISTAKE_BOOK_CHAPTER: 95,
  /**
   * 错题本节
   */
  MISTAKE_BOOK_SECTION: 96,
  /**
   *听说模拟总码
   */
  LISTEN_SPOKEN_EXAM_QR: 101,
  /**
   *听说模拟章码
   */
  LISTEN_SPOKEN_EXAM_CHAPTER_QR: 102,
  /**
   *听说模拟节码
   */
  LISTEN_SPOKEN_EXAM_SECTION_QR: 103,
  /**
   * 人机对话总码
   */
  MAN_MACHINE_DIALOGUE_QR: 109,
  /**
   * 人机对话总码
   */
  MAN_MACHINE_DIALOGUE_CATALOG_QR: 110,
  /**
   * 人机对话卷子总码
   */
  MAN_MACHINE_EXAM_QR: 111,
};

export type HandleNavActionParams = {
  sceneCode: SceneCode;
  search?: Record<string, unknown>;
};

export enum UserGender {
  Male = 1,
  Female = 2,
}

/**
 * 扫码类型
 */
export enum ScanType {
  // 扫码看解析
  ScanParse = 'scanParse',
  // 扫码搜书
  ScanBook = 'scanBook',
}

/* 点击场景的参数 */
export type SceneClickParams = {
  sceneCode?: string;
  itemCode?: string;
  pkgCode?: string;
  learnItemSceneCode?: string;
  lock?: boolean;
};
/* 详情类型 */
export enum StyleType {
  /* 阅读类 */
  ReadingDetail = 1,
  /* 专项类 */
  SpecialDetail = 2,
  /* 闯关类 */
  PassDetail = 3,
  /* 通用类 */
  CommonDetail = 3,
}

export enum BannerType {
  /* 工具 */
  Tool = 1,
  /* 玩中学 */
  LearningByPlay = 2,

  /* 双图 */
  DoubleImage = 3,

  Index = 4,
}

export enum ShowHead {
  /* 展示 */
  Show = '1',
  /* 不展示 */
  Hide = '2',
  /* 不使用该字段的值 */
  NotUse = '3',
}

/* 选择年级的步骤 */
export enum StepType {
  Grade = 'grade',
  Version = 'version',
  Book = 'book',
}
/* 选择年级的步骤 */
export const stepTypeText: Record<StepType, string> = {
  grade: '年级',
  version: '版本',
  book: '书籍',
};

export type ExpoUpdatesInfoOnLaunch = {
  updateId: string | null;
  runtimeVersion: string | null;
  checkAutomatically: string | null;
  isEmergencyLaunch: boolean | null;
  isEmbeddedLaunch: boolean | null;
};

/**
 * 首页标签
 */
export enum HomeTab {
  Bookln = 'bookln',
  Mine = 'mine',
}
