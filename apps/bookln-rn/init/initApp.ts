import { initContainer } from '@jgl/container';
import { envVars, isAndroid } from '@jgl/utils';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SplashScreen } from 'expo-router';
import { unlockAsync } from 'expo-screen-orientation';
import { getUniqueId, isTablet } from 'react-native-device-info';
import { ChannelHelper } from '../utils/ChannelHelper';
import { getDefaultStore } from 'jotai';
import { atomMap } from '../atom';
import { store } from '@jgl/biz-func';

/**
 * 初始化网络容器
 */
const initNetContainer = async () => {
  const channel = await ChannelHelper.shared().getChannel();

  const deviceId = await getUniqueId();
  const latestEnv = await AsyncStorage.getItem('current-env-prefix');

  const jotaiStore = getDefaultStore();

  initContainer({
    appId: envVars.appId(),
    channel,
    vid: Number(envVars.appVersionId()),
    getSessionId: () => {
      const sessionId = store.getState().userInfo.sessionId;
      return sessionId;
    },
    appDeviceId: deviceId,
    latestEnv,
    onNetContainerInitialized: () => {
      jotaiStore.set(atomMap.isNetContainerInitializedAtom, true);
    },
  });
};

/**
 * 初始化屏幕方向
 */
const initAppOrientation = () => {
  if (isAndroid() && isTablet()) {
    unlockAsync();
  }
};

/**
 * 防止开屏页自动隐藏
 */
const preventSplashScreenAutoHide = () => {
  SplashScreen.preventAutoHideAsync();
};

/**
 * render任何UI之前执行
 */
export const initBeforeLaunch = async () => {
  // 防止资源加载还未加载完成开屏页自动隐藏
  preventSplashScreenAutoHide();

  // 初始化网络容器
  await initNetContainer();
  // 初始化屏幕方向
  initAppOrientation();
};
