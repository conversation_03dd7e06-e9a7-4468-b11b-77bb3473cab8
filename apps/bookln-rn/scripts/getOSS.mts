import path, { basename, dirname } from 'path';
import { fileURLToPath } from 'url';
import {
  ParseAppUploadInfo,
  UploadOSSType,
  getOSSConfig,
} from '@yunti-private/cli';
import fs from 'fs';
import { ossConfigFileName } from './common.mts';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

getOSSConfig(UploadOSSType.App).then(
  (result: ParseAppUploadInfo | undefined) => {
    if (result) {
      // console.log('result', result);
      // console.log('typeof result', typeof result);
      const resultStr = JSON.stringify(result);

      const ossConfigFilePath = path.join(__dirname, '..', ossConfigFileName);

      // 将环境变量的值写入文件
      fs.writeFileSync(ossConfigFilePath, resultStr);
    }
  },
);
