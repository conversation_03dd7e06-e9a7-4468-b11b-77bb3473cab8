#! /usr/bin/env zx
import OSS from 'ali-oss';
import { existsSync, readdirSync, statSync } from 'fs';
import { readFile } from 'fs/promises';
import path, { basename, dirname } from 'path';
import { exit } from 'process';
import { fileURLToPath } from 'url';
import { $, echo } from 'zx';
import {
  UpdateChannel,
  UpdatePlatform,
  ossConfigFileName,
  ossUploadTimeout,
} from './common.mts';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

type OSSConfig = {
  accessKeyId: string;
  accessKeySecret: string;
  bucket: string;
  cdnHost: string;
  endpoint: string;
  region: string;
  token: string;
  uploadDir: string;
};

const checkWaitUploadFilePath = (jsBundlePath: string) => {
  if (!existsSync(jsBundlePath)) {
    echo(`${jsBundlePath} 路径不存在`);
    exit(1);
  }
};

const uploadWithOSS = async (
  jsonData: OSSConfig,
  waitUploadFiles: string[],
  uploadFilePrefix: string,
) => {
  const ossClient = new OSS({
    region: jsonData.region,
    accessKeyId: jsonData.accessKeyId,
    accessKeySecret: jsonData.accessKeySecret,
    stsToken: jsonData.token,
    bucket: jsonData.bucket,
  });

  const uploadDirPath = `${jsonData.uploadDir}${uploadFilePrefix}/`;

  const uploadResultUrlList: string[] = [];
  for await (const filePath of waitUploadFiles) {
    const fileName = basename(filePath);
    const uploadPath = `${uploadDirPath}${fileName}`;
    echo(`${filePath} 文件上传路径 https://${jsonData.cdnHost}/${uploadPath}`);

    const fileData = await readFile(filePath);
    const uploadResult = await ossClient.put(uploadPath, fileData, {
      timeout: ossUploadTimeout,
    });
    if (uploadResult.res.status === 200) {
      let uploadResultUrl = uploadResult.url.replace(
        `${jsonData.bucket}.${jsonData.endpoint}`,
        jsonData.cdnHost,
      );
      uploadResultUrl = uploadResultUrl.replace('http://', 'https://');
      echo(`${filePath} 文件上传成功，CDN 地址为 ${uploadResultUrl}`);
      uploadResultUrlList.push(uploadResultUrl);
    } else {
      echo(`${filePath} 文件上传失败`, uploadResult.res.toString());
    }
  }
  return uploadResultUrlList;
};

const scanDirectory = (directory: string): string[] => {
  let filesList: string[] = [];

  function scan(directory: string): void {
    readdirSync(directory).forEach((file) => {
      const filePath = path.join(directory, file);
      const stats = statSync(filePath);

      if (stats.isDirectory()) {
        scan(filePath); // 递归扫描子目录
      } else {
        filesList.push(filePath); // 存储文件路径
      }
    });
  }

  scan(directory);
  return filesList;
};

const parseOSSConfig = async (param: {
  ossConfigFilePath: string;
  jsBundlePath: string;
  uploadFilePrefix: string;
}) => {
  const { ossConfigFilePath, jsBundlePath, uploadFilePrefix } = param;
  const data = await readFile(ossConfigFilePath, 'utf-8');

  try {
    const jsonData = JSON.parse(data);
    echo('OSS 配置文件内容', data);

    const waitUploadFiles: string[] = scanDirectory(jsBundlePath);
    console.log('🚀 ~ parseOSSConfig ~ waitUploadFiles:', waitUploadFiles);

    if (waitUploadFiles.length > 0) {
      return uploadWithOSS(jsonData, waitUploadFiles, uploadFilePrefix);
    }
  } catch (error) {
    echo('解析 OSS 配置文件失败', error.toString());
  }
};

export const uploadJSBundleToAliYunOSS = async (param: {
  appId: string;
  channel: UpdateChannel;
  platform: UpdatePlatform;
  latestRuntimeVersion: string;
  jsBundlePath: string;
}) => {
  const { appId, channel, platform, latestRuntimeVersion, jsBundlePath } =
    param;
  checkWaitUploadFilePath(jsBundlePath);

  const getOSSFilePath = path.join(__dirname, 'getOSS.mts');
  echo('getOSSFilePath', getOSSFilePath);
  if (existsSync(getOSSFilePath)) {
    await $`npx tsx ${getOSSFilePath}`;
  }

  // 1.定位到 oss_config.json
  const ossConfigFilePath = path.join(__dirname, '..', ossConfigFileName);

  echo('ossConfigFilePath', ossConfigFilePath);
  echo('__filename', __filename);
  echo('__dirname', __dirname);

  const uploadFilePrefix = `updates/${appId}/${channel}/${platform}/${latestRuntimeVersion}`;

  // 2.检查 ossConfigFilePath 是否存在
  if (existsSync(ossConfigFilePath)) {
    // 如果存在，则解析 oss 配置文件
    return parseOSSConfig({
      ossConfigFilePath,
      jsBundlePath,
      uploadFilePrefix,
    });
  } else {
    echo(`${ossConfigFileName} 文件未找到，请检查 getOSS.mts 脚本是否执行成功`);
  }
};
