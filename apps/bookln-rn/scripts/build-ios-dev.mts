#! /usr/bin/env zx
/* eslint-disable @typescript-eslint/ban-ts-comment */
import { $, cd, echo } from 'zx';
import {
  checkNodeEnv,
  getOSS,
  prepareEnvironmentVariables,
} from './build-common.mts';

const run = async () => {
  echo('书链 App iOS 开发包 - 开始打包');

  // 检查 Node
  await checkNodeEnv();

  // 准备环境变量
  await prepareEnvironmentVariables(true);

  await getOSS();

  cd('ios');

  await bundleIOS();
};

const bundleIOS = async () => {
  echo('Bundle Install 开始');
  await $`bundle config set path 'vendor/bundle'`;
  await $`bundle install`;
  await $`bundle update fastlane`;
  await $`bundle config set path 'vendor/bundle'`;
  echo('Bundle Install 结束');
  echo('Pod Install 开始');
  await $`bundle exec pod install --verbose`;
  echo('Pod Install 结束');
  await $`bundle exec fastlane app app_id:2 dev_build:true`;
};

run();
