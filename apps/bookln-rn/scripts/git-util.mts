#! /usr/bin/env zx
import { $ } from 'zx';

// https://github.com/google/zx/issues/126#issuecomment-850621670
// @ts-ignore
$.noquote = async (...args) => {
  const q = $.quote;
  $.quote = (v) => v;
  // @ts-ignore
  const p = $(...args);
  await p;
  $.quote = q;
  return p;
};

/**
 * 获取 git 分支
 * @returns git 分支信息
 */
export const getGitBranch = async () => {
  const gitBranch = await $`git symbolic-ref --short -q HEAD`;
  return gitBranch.stdout;
};

/**
 * 获取 git 最后一次提交的 hash
 * @returns git 最后一次提交的 hash
 */
export const getGitCommit = async () => {
  const gitCommit = await $`git rev-parse --short HEAD`;
  return gitCommit.stdout;
};

/**
 * 获取 git 当前的用户
 */
export const getCurrentAuthor = async () => {
  const currentAuthor = await $`git config --get user.name`;
  return currentAuthor.stdout;
};

/**
 * 获取本次 git commit 和 GIT_PREVIOUS_SUCCESSFUL_COMMIT 之间的 commit 信息
 */
export const getCommitInfo = (): Promise<string | null> => {
  return new Promise(async (resolve, reject) => {
    const env = process.env;

    if (!('GIT_PREVIOUS_SUCCESSFUL_COMMIT' in env)) {
      return resolve(null);
    }

    const previousCommit = env['GIT_PREVIOUS_SUCCESSFUL_COMMIT'];
    // const previousCommit = '528066f8caf0c03cbac43bf143ad7fcaf135fc76';
    const currentCommit = env['GIT_COMMIT'];
    // const currentCommit = '7df83a77c36490a2b2d44c9bcfabd71a014e42c0';

    if (currentCommit === previousCommit) {
      return resolve(null);
    }

    const command = `git log ${previousCommit}..${currentCommit} --pretty=format:"%H-%s[%cn](%h)"`;

    try {
      // @ts-ignore
      const { stdout } = await $.noquote`${command}`;
      const content = stdout.trim().split('\n');

      const infos = content
        .map((line, index) => {
          return `${index + 1}. ${line.substring(41)}`;
        })
        .join('\n');
      // console.log('🚀 ~ file: test.mts:71 ~ returnnewPromise ~ infos:', infos);
      // echo(`result = ${infos}`);
      resolve(infos);
    } catch (err) {
      reject(err);
    }
  });
};

export const getGitDiffBetweenPreviousSuccessfulCommitAndCurrentGitCommit =
  (): Promise<string | null> => {
    return new Promise(async (resolve, reject) => {
      const env = process.env;

      if (!('GIT_PREVIOUS_SUCCESSFUL_COMMIT' in env)) {
        return resolve(null);
      }

      const previousCommit = env['GIT_PREVIOUS_SUCCESSFUL_COMMIT'];
      // const previousCommit = 'bc3f49b254fbd5702f3b8e30700072e652140f9f';
      const currentCommit = env['GIT_COMMIT'];
      // const currentCommit = 'f663e69273970b4c75b809af47a0e28e91d6d102';

      if (currentCommit === previousCommit) {
        return resolve(null);
      }

      const command = `git diff ${previousCommit}..${currentCommit}`;

      try {
        // @ts-ignore
        const { stdout } = await $.noquote`${command}`;
        resolve(stdout);
      } catch (err) {
        reject(err);
      }
    });
  };
