#! /usr/bin/env zx
import path, { basename, dirname } from 'path';
import { fileURLToPath } from 'url';
import { $, cd, echo } from 'zx';
import { existsSync } from 'fs';
import { readFile } from 'fs/promises';
import OSS from 'ali-oss';
import { exit } from 'process';
import { ossConfigFileName, ossUploadTimeout } from './common.mts';
import { getOSS } from './build-common.mts';
const [
  nodePath,
  scriptPath,
  ipa_path_for_ad_hoc,
  ipa_manifest_plist_path,
  qr_code_path_for_ad_hoc,
  ...restArgs
] = process.argv;

echo('process.argv', process.argv);

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

type OSSConfig = {
  accessKeyId: string;
  accessKeySecret: string;
  bucket: string;
  cdnHost: string;
  endpoint: string;
  region: string;
  token: string;
  uploadDir: string;
};

const checkWaitUploadFilePath = () => {
  if (!existsSync(ipa_path_for_ad_hoc)) {
    echo(`${ipa_path_for_ad_hoc} 路径不存在，请检查 build.rb 脚本`);
    exit(1);
  }
  if (!existsSync(ipa_manifest_plist_path)) {
    echo(`${ipa_manifest_plist_path} 路径不存在，请检查 build.rb 脚本`);
    exit(1);
  }
  if (!existsSync(qr_code_path_for_ad_hoc)) {
    echo(`${qr_code_path_for_ad_hoc} 路径不存在，请检查 build.rb 脚本`);
    exit(1);
  }
};

const uploadWithOSS = async (jsonData: OSSConfig) => {
  const ossClient = new OSS({
    region: jsonData.region,
    accessKeyId: jsonData.accessKeyId,
    accessKeySecret: jsonData.accessKeySecret,
    stsToken: jsonData.token,
    bucket: jsonData.bucket,
  });

  const uploadDirPath = `${jsonData.uploadDir}ios/`;

  const filePaths = [
    ipa_path_for_ad_hoc,
    ipa_manifest_plist_path,
    qr_code_path_for_ad_hoc,
  ];

  for await (const filePath of filePaths) {
    const fileName = basename(filePath);
    const uploadPath = `${uploadDirPath}${fileName}`;
    echo(`${filePath} 文件上传路径 ${uploadPath}`);
    const fileData = await readFile(filePath);
    const uploadResult = await ossClient.put(uploadPath, fileData, {
      timeout: ossUploadTimeout,
    });
    if (uploadResult.res.status === 200) {
      echo(`${filePath} 文件上传成功，CDN 地址为 ${uploadResult.url}`);
    } else {
      echo(`${filePath} 文件上传失败`, uploadResult.res.toString());
    }
  }
};

const parseOSSConfig = async (ossConfigFilePath: string) => {
  const data = await readFile(ossConfigFilePath, 'utf-8');

  try {
    const jsonData = JSON.parse(data);
    echo('OSS 配置文件内容', data);

    checkWaitUploadFilePath();

    uploadWithOSS(jsonData);
  } catch (error) {
    echo('解析 OSS 配置文件失败', error.toString());
  }
};

const run = async () => {
  const getOSSFilePath = path.join(__dirname, 'getOSS.mts');
  echo('getOSSFilePath', getOSSFilePath);
  if (existsSync(getOSSFilePath)) {
    await $`npx tsx ${getOSSFilePath}`;
  }

  // 1.定位到 oss_config.json
  const ossConfigFilePath = path.join(__dirname, '..', ossConfigFileName);

  echo('ossConfigFilePath', ossConfigFilePath);
  echo('__filename', __filename);
  echo('__dirname', __dirname);

  // 2.检查 ossConfigFilePath 是否存在
  if (existsSync(ossConfigFilePath)) {
    // 如果存在，则解析 oss 配置文件
    parseOSSConfig(ossConfigFilePath);
  } else {
    echo(`${ossConfigFileName} 文件未找到，请检查 getOSS.mts 脚本是否执行成功`);
  }
};

run();
