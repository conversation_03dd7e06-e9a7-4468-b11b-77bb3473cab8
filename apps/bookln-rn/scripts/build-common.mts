#! /usr/bin/env zx
/* eslint-disable @typescript-eslint/ban-ts-comment */
import dayjs from 'dayjs';
import { $, echo } from 'zx';

export const checkNodeEnv = async () => {
  const result = await $`which node`;
  echo(`node 环境检查结果 ${result.toString()}`);
};

export const prepareEnvironmentVariables = async (
  disableAutoUpload = false,
) => {
  // 打包时间
  // 精确到分钟 202208232031
  const buildDate = new Date();
  const readableTime = dayjs(buildDate).format('YYYYMMDDHHmmss');
  process.env.EXPO_PUBLIC_YT_BUILD_TIME = `${readableTime}`;

  // 打包分支
  const branch = (await $`git rev-parse --abbrev-ref HEAD`).toString().trim();
  process.env.EXPO_PUBLIC_YT_BRANCH = branch;

  // 打包commit
  const commit = (await $`git rev-parse --short HEAD`).toString().trim();
  process.env.EXPO_PUBLIC_YT_COMMIT = commit;

  //准备sentry的环境变量
  process.env.SENTRY_DISABLE_AUTO_UPLOAD = String(disableAutoUpload);

  echo('所有环境变量：');
  echo(JSON.stringify($.env));
};

export const prepareEnvironmentVariablesForNativeBuild = async () => {
  // 打包时间
  // 精确到分钟 202208232031
  const buildDate = new Date();
  const readableTime = dayjs(buildDate).format('YYYYMMDDHHmmss');
  process.env.EXPO_PUBLIC_APP_NATIVE_BUILD_TIME = `${readableTime}`;

  // 打包分支
  const branch = (await $`git rev-parse --abbrev-ref HEAD`).toString().trim();
  process.env.EXPO_PUBLIC_APP_NATIVE_BUILD_BRANCH = branch;

  // 打包commit
  const commit = (await $`git rev-parse --short HEAD`).toString().trim();
  process.env.EXPO_PUBLIC_APP_NATIVE_BUILD_COMMIT = commit;

  echo('App Native Build: 环境变量：');
  echo('原生包打包时间：', process.env.EXPO_PUBLIC_APP_NATIVE_BUILD_TIME);
  echo('原生包打包分支：', process.env.EXPO_PUBLIC_APP_NATIVE_BUILD_BRANCH);
  echo('原生包打包commit：', process.env.EXPO_PUBLIC_APP_NATIVE_BUILD_COMMIT);
};

export const preparePnpm = async () => {
  const result = await $`which pnpm`;
  if (result) {
    await $`pnpm config set auto-install-peers false`;
    await $`pnpm install --no-frozen-lockfile`;
  }
};

export const getOSS = async () => {
  await $`npx tsx scripts/getOSS.mts`;
};

const padZero = (num: number): string => {
  return num < 10 ? `0${num}` : `${num}`;
};

/**
 * 返回当前时间，格式为年月日时分秒，每个部分均占两位，不足的话会在前面补0
 * @example 2023年12月6日14时30分00秒 => 20231206143000
 */
export const formatCurrentDateTime = (): string => {
  const now = new Date();

  const year = now.getFullYear();
  const month = padZero(now.getMonth() + 1); // JavaScript months are 0-indexed
  const day = padZero(now.getDate());
  const hours = padZero(now.getHours());
  const minutes = padZero(now.getMinutes());
  const seconds = padZero(now.getSeconds());

  return `${year}${month}${day}${hours}${minutes}${seconds}`;
};
