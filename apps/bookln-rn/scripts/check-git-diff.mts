#!/usr/bin/env zx
import { $, echo } from 'zx';
import { readFileSync } from 'node:fs';
import { fileURLToPath } from 'node:url';
import path, { dirname } from 'node:path';
import { getGitDiffBetweenPreviousSuccessfulCommitAndCurrentGitCommit } from './git-util.mts';

// https://github.com/google/zx/issues/126#issuecomment-850621670
// @ts-ignore
$.noquote = async (...args) => {
  const q = $.quote;
  $.quote = (v) => v;
  // @ts-ignore
  const p = $(...args);
  await p;
  $.quote = q;
  return p;
};

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 读取 package.json 中的 dependencies 字段
const packageJsonName = 'package.json';

/**
 * 判断包名是否为 React Native 相关的库
 * @param packageName 包名
 * @returns 是否为 React Native 相关库
 */
const isReactNativeRelatedPackage = (packageName: string): boolean => {
  // React Native 相关库的匹配规则
  const reactNativePatterns = [
    // 包含 react-native 的库
    /react-native/i,
    // 以 @react-native 开头的库
    /^@react-native/,
    // React Native 生态常用库
    /^react-navigation/,
    /expo/,
    // 特定的第三方 React Native 组件库
    /^@shopify\/flash-list/,
    // 其他 React Native 原生桥接库
    /^@?rn-/,
    /^react-native-/,
    /^native-wechat/,
  ];

  return reactNativePatterns.some((pattern) => pattern.test(packageName));
};

/**
 * 解析 git diff 输出，检查 package.json 文件中的增删行是否涉及 RN 相关依赖。
 * @returns 如果检测到 RN 相关依赖的实际变更（添加、删除、版本修改）。
 * 纯粹的位置移动（版本号不变）不视为变更。
 * @param diffContent git diff 的原始字符串输出。
 * @param _appDependencies (不再使用，保留签名)
 * @param _rootDependencies (不再使用，保留签名)
 * @returns 如果检测到 RN 相关依赖的实际变更则返回 true，否则返回 false。
 */
const parsePackageJsonDiffForRNChanges = (
  diffContent: string | null | undefined,
  _appDependencies: Record<string, string>,
  _rootDependencies: Record<string, string>,
): boolean => {
  if (!diffContent) {
    return false;
  }

  // Regex to capture package name and version from +/- lines
  // Example: +   "react-native": "0.72.0",
  // Example: -   "expo": "49.0.0",
  // Captures: 1=packageName, 2=version (lenient on surrounding quotes/commas for version)
  // Allowing for optional comma and space after version quote
  // Allowing for leading/trailing whitespace and optional trailing comma
  const depChangeRegex = /^\s*[+-]\s*"([^"]+)":\s*"([^"]+)"\s*,?/;

  const lines = diffContent.split('\n');
  const addedPackages = new Map<string, string>();
  const removedPackages = new Map<string, string>();
  let currentFileIsPackageJson = false;

  for (const line of lines) {
    const trimmedLine = line.trim();

    if (trimmedLine.startsWith('diff --git')) {
      currentFileIsPackageJson = line.includes(' b/') && line.endsWith('/package.json');
    } else if (currentFileIsPackageJson) {
      const match = line.match(depChangeRegex);
      if (match && match[1] && match[2]) {
        const packageName = match[1];
        const version = match[2]; // Version might include stuff like ^, ~, etc.

        // Only consider RN-related packages for detailed tracking
        if (isReactNativeRelatedPackage(packageName)) {
          if (trimmedLine.startsWith('+')) {
            addedPackages.set(packageName, version);
          } else if (trimmedLine.startsWith('-')) {
            removedPackages.set(packageName, version);
          }
        }
      }
    }
  }

  let hasActualRnChange = false;

  // Check for removals or version changes
  for (const [pkgName, removedVersion] of removedPackages.entries()) {
    if (addedPackages.has(pkgName)) {
      // Package exists in both added and removed - check version
      if (addedPackages.get(pkgName) !== removedVersion) {
        // Version changed!
        hasActualRnChange = true;
        break;
      }
      // Versions match - it's a move, remove from addedPackages so it's not counted as an addition later
      addedPackages.delete(pkgName);
    } else {
      // Package was only removed
      hasActualRnChange = true;
      break;
    }
  }

  // If no change detected yet, check if any RN packages were purely added
  if (!hasActualRnChange) {
    for (const pkgName of addedPackages.keys()) {
      // We already removed moved packages, any remaining RN package is a pure addition
      if (isReactNativeRelatedPackage(pkgName)) {
        // Double check, though only RN should be in the map
        hasActualRnChange = true;
        break;
      }
    }
  }

  return hasActualRnChange;
};

/**
 * 检测是否有原生代码变动
 */
export const checkGitDiffHasNativeChange = async () => {
  const packageJsonPath = path.join(__dirname, '..', packageJsonName);
  const rootPackageJsonPath = path.join(__dirname, '../../..', packageJsonName);
  const packageJsonContent = readFileSync(packageJsonPath, 'utf-8');
  const rootPackageJsonContent = readFileSync(rootPackageJsonPath, 'utf-8');
  // 解析依赖，如果 dependencies 不存在则使用空对象，避免后续 Object.keys 报错
  const appDeps = JSON.parse(packageJsonContent).dependencies ?? {};
  const rootDeps = JSON.parse(rootPackageJsonContent).dependencies ?? {};

  // 获取上一次构建成功的提交hash
  const previousSuccessfulCommitHash = await $`git rev-parse HEAD^`;

  // 执行 git diff 命令获取变更内容,使用相同的比较范围
  const nameOnlyGitDiffOutput =
    await $`git diff --name-only ${previousSuccessfulCommitHash.stdout.trim()}..HEAD`;

  // 检查是否有 iOS 目录或 Android 目录的变化
  const hasIOSChanges = nameOnlyGitDiffOutput.stdout.includes('ios/');
  const hasAndroidChanges = nameOnlyGitDiffOutput.stdout.includes('android/');

  // 获取 git diff 内容
  const contentGitDiffOutput = await getGitDiffBetweenPreviousSuccessfulCommitAndCurrentGitCommit();

  // 调用新的纯函数来判断 package.json 的 RN 相关变动
  const hasPackageJsonChanges = parsePackageJsonDiffForRNChanges(
    contentGitDiffOutput,
    appDeps,
    rootDeps,
  );

  console.log(
    '🚀 ~ checkGitDiffHasNativeChange ~ hasPackageJsonChanges:', // 更新了 context
    hasPackageJsonChanges,
  );

  // TODO: cenfeng - 是否需要执行判断 pnpm-lock.yaml 的变动呢？
  // git diff --name-only ${previousCommit}..${currentCommit} | grep pnpm-lock.yaml
  // 如果有原生代码变动，输出警告并阻止提交
  if (hasIOSChanges || hasAndroidChanges || hasPackageJsonChanges) {
    echo('有原生代码变动');
    return true;
    // iOS => IPA
    // Android => APK
    // 更新 runtime version

    // console.error(
    //   '本次提交包含原生代码的改动或者 React Native 相关依赖的变化。请确保进行了相应的测试和审查。',
    // );
    // process.exit(1); // 提交失败
  } else {
    // iOS => JS Bundle
    // Android => JS Bundle
    // 上传 JS Bundle
    echo('没有原生代码变动');
    return false;
  }
};

// 如果没有原生代码变动，提交继续
// process.exit(0);

// simple test
// const result = await checkGitDiffHasNativeChange();
// console.log('🚀 ~ file: check-git-diff.mts:167 ~ result:', result);

// --- 本地测试代码 ---
const runLocalTests = () => {
  console.log('\n--- 开始本地 Diff 解析器测试 ---');

  // 模拟当前项目中的依赖情况
  const sampleAppDeps = {
    'react-native': '0.71.0',
    lodash: '4.17.21',
  };
  const sampleRootDeps = {
    expo: '49.0.0',
    typescript: '5.0.4',
    '@config-plugins/react-native-pdf': '6.0.0',
  };

  // 测试用例 1: 模拟在 app package.json 中添加 RN 包
  const diffAddRN = `
diff --git a/apps/bookln-rn/package.json b/apps/bookln-rn/package.json
index abc..def 100644
--- a/apps/bookln-rn/package.json
+++ b/apps/bookln-rn/package.json
@@ -5,6 +5,7 @@
    "react": "18.2.0",
    "react-dom": "18.2.0",
    "react-native": "0.71.13",
+   "react-native-gesture-handler": "^2.12.0", // RN 相关包
    "react-native-web": "~0.18.10"
  },
  "devDependencies": {
`;
  const result1 = parsePackageJsonDiffForRNChanges(diffAddRN, sampleAppDeps, sampleRootDeps);
  console.log(`测试 1 (添加 RN 包): 期望 true, 结果: ${result1} ${result1 === true ? '✅' : '❌'}`);

  // 测试用例 2: 模拟在 app package.json 中添加非 RN 包
  const diffAddNonRN = `
diff --git a/apps/bookln-rn/package.json b/apps/bookln-rn/package.json
index abc..def 100644
--- a/apps/bookln-rn/package.json
+++ b/apps/bookln-rn/package.json
@@ -5,6 +5,7 @@
    "react": "18.2.0",
    "react-dom": "18.2.0",
    "react-native": "0.71.13",
+    "@bookln/bookln-biz": "workspace:*", // 非 RN 相关包
    "react-native-web": "~0.18.10"
  },
  "devDependencies": {
`;
  const result2 = parsePackageJsonDiffForRNChanges(diffAddNonRN, sampleAppDeps, sampleRootDeps);
  console.log(
    `测试 2 (添加非 RN 包): 期望 false, 结果: ${result2} ${result2 === false ? '✅' : '❌'}`,
  );

  // 测试用例 3: 模拟在 root package.json 中移除 RN 包 (expo)
  const diffRemoveRNRoot = `
diff --git a/package.json b/package.json
index ghi..jkl 100644
--- a/package.json
+++ b/package.json
@@ -10,7 +10,6 @@
    "eslint": "^8.57.0",
    "jest": "^29.7.0",
    "prettier": "^3.2.5",
-   "expo": "49.0.0", // RN 相关包
    "turbo": "latest",
    "typescript": "^5.3.3"
  },
`;
  const result3 = parsePackageJsonDiffForRNChanges(diffRemoveRNRoot, sampleAppDeps, sampleRootDeps);
  console.log(
    `测试 3 (移除 root RN 包): 期望 true, 结果: ${result3} ${result3 === true ? '✅' : '❌'}`,
  );

  // 测试用例 4: 模拟在 app package.json 中修改非 RN 包版本
  const diffChangeVersionNonRN = `
diff --git a/apps/bookln-rn/package.json b/apps/bookln-rn/package.json
index abc..def 100644
--- a/apps/bookln-rn/package.json
+++ b/apps/bookln-rn/package.json
@@ -4,7 +4,7 @@
  "dependencies": {
    "@react-navigation/native": "^6.1.17",
    "jotai": "^2.8.0",
-   "lodash": "4.17.21", // 非 RN 相关包
+   "lodash": "4.17.22", // 非 RN 相关包
    "react": "18.2.0",
    "react-dom": "18.2.0",
    "react-native": "0.71.13",
`;
  const result4 = parsePackageJsonDiffForRNChanges(
    diffChangeVersionNonRN,
    sampleAppDeps,
    sampleRootDeps,
  );
  console.log(
    `测试 4 (修改非 RN 包版本): 期望 false, 结果: ${result4} ${result4 === false ? '✅' : '❌'}`,
  );

  // 测试用例 5: 模拟 diff 上下文包含 RN 包名，但实际变动是非 RN 包 (用户反馈的场景)
  const diffNonRNWithRNContext = `
diff --git a/apps/bookln-rn/package.json b/apps/bookln-rn/package.json
index 0fd13c9..d26e107 100644
--- a/apps/bookln-rn/package.json
+++ b/apps/bookln-rn/package.json
@@ -36,6 +36,7 @@
      "@config-plugins/react-native-blob-util": "6.0.0", // 上下文中的 RN 相关包
      "@config-plugins/react-native-pdf": "6.0.0",       // 上下文中的 RN 相关包
      "@expo/react-native-action-sheet": "4.0.1",        // 上下文中的 RN 相关包
+     "@bookln/bookln-biz": "workspace:*", // 实际添加的非 RN 包
`;
  // 注意：这个测试用例在新逻辑下可能需要调整，因为新逻辑只关注匹配 Regex 的行
  // 但这里的添加行 "@bookln/bookln-biz": "workspace:*" 不匹配新的 Regex (缺少版本号引号)
  // 我们先保持原样，看结果。如果预期行为是识别这种添加，Regex需要调整。
  // 当前更严格的 Regex 可能导致此例也返回 false (这是正确的，因为它不是 RN 变更)。
  const result5 = parsePackageJsonDiffForRNChanges(
    diffNonRNWithRNContext,
    sampleAppDeps,
    sampleRootDeps,
  );
  console.log(
    `测试 5 (非 RN 变动 + RN 上下文): 期望 false, 结果: ${result5} ${
      result5 === false ? '✅' : '❌'
    }`,
  );

  // 测试用例 6: 空 diff 内容
  const result6 = parsePackageJsonDiffForRNChanges(null, sampleAppDeps, sampleRootDeps);
  console.log(`测试 6 (空 Diff): 期望 false, 结果: ${result6} ${result6 === false ? '✅' : '❌'}`);

  // 测试用例 7: 仅修改 RN 包版本号
  const diffChangeVersionRN = `
 diff --git a/apps/bookln-rn/package.json b/apps/bookln-rn/package.json
 index abc..def 100644
 --- a/apps/bookln-rn/package.json
 +++ b/apps/bookln-rn/package.json
 @@ -7,7 +7,7 @@
    "react-dom": "18.2.0",
 -   "react-native": "0.71.13", // RN 相关包
 +   "react-native": "0.72.0", // RN 相关包
    "react-native-web": "~0.18.10"
  },
  "devDependencies": {
`;
  const result7 = parsePackageJsonDiffForRNChanges(
    diffChangeVersionRN,
    sampleAppDeps,
    sampleRootDeps,
  );
  console.log(
    `测试 7 (修改 RN 包版本): 期望 true, 结果: ${result7} ${result7 === true ? '✅' : '❌'}`,
  );

  // 测试用例 8: 移动 RN 相关包位置，版本号不变
  const diffMoveRN = `
diff --git a/apps/bookln-rn/package.json b/apps/bookln-rn/package.json
index mno..pqr 100644
--- a/apps/bookln-rn/package.json
+++ b/apps/bookln-rn/package.json
@@ -4,9 +4,9 @@
  "dependencies": {
    "@react-navigation/native": "^6.1.17",
    "jotai": "^2.8.0",
-   "react-native": "0.71.13", // RN 相关包 (被移动)
    "lodash": "4.17.21",
    "react": "18.2.0",
    "react-dom": "18.2.0",
+   "react-native": "0.71.13", // RN 相关包 (移动到新位置, 版本不变)
    "react-native-web": "~0.18.10"
  },
  "devDependencies": {
`;
  const result8 = parsePackageJsonDiffForRNChanges(diffMoveRN, sampleAppDeps, sampleRootDeps);
  // 根据新逻辑，纯粹移动（版本不变）不应视为变更
  console.log(
    `测试 8 (移动 RN 包位置): 期望 false, 结果: ${result8} ${
      // <--- 期望结果已改为 false
      result8 === false ? '✅' : '❌'
    }`,
  );

  console.log('--- 本地测试结束 ---');
};

// 如果直接运行此脚本，则执行本地测试
// 注意：这会覆盖脚本原本作为 husky hook 使用时的行为
// 在实际部署为 hook 时应注释或移除此调用
// runLocalTests();

// 保留原始的简单测试调用（如果需要）
// const result = await checkGitDiffHasNativeChange();
// console.log('🚀 ~ file: check-git-diff.mts:167 ~ result:', result);
