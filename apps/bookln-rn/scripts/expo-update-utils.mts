import Crypto from 'crypto';

export const createHash = (param: {
  file: Buffer;
  hashingAlgorithm: string;
  encoding: Crypto.BinaryToTextEncoding;
}) => {
  const { file, hashingAlgorithm, encoding } = param;
  return Crypto.createHash(hashingAlgorithm).update(file).digest(encoding);
};

export const convertSHA256HashToUUID = (value: string) => {
  return `${value.slice(0, 8)}-${value.slice(8, 12)}-${value.slice(
    12,
    16,
  )}-${value.slice(16, 20)}-${value.slice(20, 32)}`;
};

export const getBase64URLEncoding = (base64EncodedString: string): string => {
  return base64EncodedString
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '');
};
