#! /usr/bin/env zx
/* eslint-disable @typescript-eslint/ban-ts-comment */
import 'dotenv/config';
import { dirname } from 'path';
import { exit } from 'process';
import { fileURLToPath } from 'url';
import yargs from 'yargs';
import { $, cd, echo } from 'zx';
import {
  checkNodeEnv,
  getOSS,
  prepareEnvironmentVariables,
} from './build-common.mts';

const argv = yargs(process.argv.slice(2))
  .option('xcodePath', { type: 'string' })
  .option('fastlanePath', { type: 'string' })
  .option('podRepoUpdate', { type: 'boolean', default: false })
  .parse();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 获取应用信息
const appId = process.env.EXPO_PUBLIC_APP_ID?.toString();
console.log('🐳 - build-ios-upload.mts - checkAppId - appId', appId);
const iOSProjectName = process.env.EXPO_PUBLIC_APP_IOS_PROJECT_NAME;
console.log(
  '🐳 - build-ios-upload.mts - checkiOSProjectName - iOSProjectName',
  iOSProjectName,
);
const taskName = '书链 App iOS 上传 iOS 测试包到 TestFlight';

// 配置验证函数
const validateConfig = () => {
  // 验证环境变量
  const requiredEnvVars = [
    'EXPO_PUBLIC_APP_ID',
    'EXPO_PUBLIC_APP_IOS_PROJECT_NAME',
  ];
  const missingVars = requiredEnvVars.filter(
    (varName) => !process.env[varName],
  );

  if (missingVars.length > 0) {
    console.error(
      'validateConfig',
      `缺少必要的环境变量: ${missingVars.join(', ')}`,
    );
    return false;
  }

  return true;
};

// 使用异步 IIFE 初始化程序
(async () => {
  // 验证配置
  if (!validateConfig()) {
    exit(1);
  }

  // 在验证通过后进行类型断言，确保 TypeScript 知道这些变量不会是 undefined
  const appIdValue = appId as string;
  const iOSProjectNameValue = iOSProjectName as string;

  const xcodePath = argv.xcodePath;
  const fastlanePath = argv.fastlanePath;
  const podRepoUpdate = argv.podRepoUpdate;
  console.log(
    '🐳 - build-ios-upload.mts - checkXcodePath - xcodePath',
    xcodePath,
  );
  console.log(
    '🐳 - build-ios-upload.mts - checkFastlanePath - fastlanePath',
    fastlanePath,
  );
  console.log(
    '🐳 - build-ios-upload.mts - podRepoUpdate - podRepoUpdate',
    podRepoUpdate,
  );

  const execWithTimeout = async (cmd, timeoutMs = 600000) => {
    return Promise.race([
      cmd,
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('命令执行超时')), timeoutMs),
      ),
    ]);
  };

  const timeStart = (label) => {
    console.time(`⏱️ ${label}`);
    return () => console.timeEnd(`⏱️ ${label}`);
  };

  /**
   * 构建 iOS 原生包
   */
  const bundleIOS = async () => {
    console.log('🐳 - build-ios-upload.mts - bundleIOS - Bundle Install 开始');
    await $`bundle config set path 'vendor/bundle'`;
    await $`bundle install`;
    await $`bundle update fastlane`;
    console.log('🐳 - build-ios-upload.mts - bundleIOS - Bundle Install 结束');

    console.log('🐳 - build-ios-upload.mts - bundleIOS - Pod Install 开始');
    const endPodInstall = timeStart('Pod Install');
    try {
      if (podRepoUpdate) {
        await execWithTimeout(
          $`bundle exec pod install --repo-update --verbose`,
          10 * 60 * 1000,
        ); // 10分钟超时
      } else {
        await execWithTimeout(
          $`bundle exec pod install --verbose`,
          10 * 60 * 1000,
        ); // 10分钟超时
      }
      console.log('🐳 - build-ios-upload.mts - bundleIOS - Pod Install 结束');
    } catch (error) {
      console.error(
        `🐳 - build-ios-upload.mts - bundleIOS - Pod Install 失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      throw error;
    } finally {
      endPodInstall();
    }

    try {
      console.log('🐳 - build-ios-upload.mts - bundleIOS - Fastlane 上传开始');
      const endFastlane = timeStart('Fastlane 上传');
      await execWithTimeout(
        $`bundle exec fastlane auto_detect_latest_archive_then_upload_to_testflight app_id:${appIdValue} xcode_path:${xcodePath} fastlane_path:${fastlanePath}`,
        60 * 60 * 1000, // 60分钟超时
      );
      console.log('🐳 - build-ios-upload.mts - bundleIOS - Fastlane 上传结束');
      endFastlane();
      exit(0);
    } catch (error) {
      console.error(
        `🐳 - build-ios-upload.mts - bundleIOS - Fastlane 上传失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      exit(1);
    }
  };

  const run = async () => {
    try {
      console.log(
        `🐳 build-ios-upload.mts - run - 🔥🔥🔥🔥🔥🔥 ${taskName} - 开始上传`,
      );

      // 检查 Node
      await checkNodeEnv();

      // 准备环境变量
      await prepareEnvironmentVariables();

      await getOSS();

      cd('ios');

      await bundleIOS();
    } catch (error) {
      console.error(
        `build-ios-upload.mts 上传过程中发生错误: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      exit(1);
    }
  };

  // 启动上传流程
  await run();
})();
