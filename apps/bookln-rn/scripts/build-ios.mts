#! /usr/bin/env zx
/* eslint-disable @typescript-eslint/ban-ts-comment */
import 'dotenv/config';
import path, { dirname } from 'path';
import { exit } from 'process';
import { fileURLToPath } from 'url';
import yargs from 'yargs';
import { $, cd, fetch } from 'zx';
import {
  checkNodeEnv,
  prepareEnvironmentVariables,
  prepareEnvironmentVariablesForNativeBuild,
} from './build-common.mts';
import { checkGitDiffHasNativeChange } from './check-git-diff.mts';
import { UpdateChannel, UpdatePlatform } from './common.mts';
import {
  ExpoUpdatesCheckOnLaunchType,
  addNewExpoUpdatesConfig,
  exportJSBundle,
  fetchExpoUpdatesConfig,
  getLatestRuntimeVersion,
  hotFixJSONFileName,
  hotFixManifestUrl,
  sendJSBundleUpdatedMsg,
  updateRuntimeVersion,
  uploadJSBundleToCDN,
  writeExpoUpdatesConfig,
} from './expo-updates.mts';

const argv = yargs(process.argv.slice(2))
  .option('forceBuildNative', { type: 'boolean', default: false })
  .option('updateReleaseChannel', { type: 'string', default: 'staging' })
  .option('forceSkipBuildNative', { type: 'boolean', default: false })
  .option('targetRuntimeVersion', { type: 'string' })
  .option('xcodePath', { type: 'string' })
  .option('disabledAutoUploadSentry', { type: 'boolean', default: false })
  .option('podRepoUpdate', { type: 'boolean', default: false })
  .option('addNewTestDevice', { type: 'boolean', default: false })
  .parse();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

type AppVersionResponse = {
  success: boolean;
  data: {
    miniAPPVersion: string;
  };
};

/**
 * 从 API 获取应用的版本号
 * @param appId 应用ID
 * @returns Promise<string | undefined> 应用版本号或 undefined（获取失败时）
 */
const fetchAppVersion = async (appId: string): Promise<string | undefined> => {
  try {
    console.log(
      '🐳 - build-ios.mts - fetchAppVersion - 开始从 API 获取应用版本',
    );
    const response = await fetch(
      `https://app-prepub.bookln.cn/appservice/miniappinfo.do?appid=${appId}&type=1`,
    );

    if (!response.ok) {
      throw new Error(
        `API 请求失败: ${response.status} ${response.statusText}`,
      );
    }

    const data: AppVersionResponse =
      (await response.json()) as AppVersionResponse;

    if (!data.success) {
      throw new Error(`API 返回错误: ${JSON.stringify(data)}`);
    }

    const version = data.data?.miniAPPVersion;

    if (!version) {
      throw new Error('API 返回数据中未找到 miniAPPVersion 字段');
    }

    // 处理版本号格式，去掉前缀 'v'
    const formattedVersion = version.startsWith('v')
      ? version.substring(1)
      : version;

    console.log(
      `🐳 - build-ios.mts - fetchAppVersion - 成功获取版本号: ${version} -> ${formattedVersion}`,
    );
    return formattedVersion;
  } catch (error) {
    console.error(
      `🐳 - build-ios.mts - fetchAppVersion - 获取版本号失败: ${
        error instanceof Error ? error.message : String(error)
      }`,
    );
    return undefined;
  }
};

// 获取应用信息
let appVersion: string | undefined;
const appId = process.env.EXPO_PUBLIC_APP_ID?.toString();
console.log('🐳 - build-ios.mts - checkAppId - appId', appId);
const iOSProjectName = process.env.EXPO_PUBLIC_APP_IOS_PROJECT_NAME;
console.log(
  '🐳 - build-ios.mts - checkiOSProjectName - iOSProjectName',
  iOSProjectName,
);
const appName = process.env.EXPO_PUBLIC_APP_NAME_FOR_USER;
console.log('🐳 - build-ios.mts - checkAppName - appName', appName);
const updateReleaseChannel =
  (argv.updateReleaseChannel as UpdateChannel) || 'staging';
console.log(
  '🐳 - build-ios.mts - checkUpdateReleaseChannel - updateReleaseChannel',
  updateReleaseChannel,
);
const channel: UpdateChannel = updateReleaseChannel;
console.log('🐳 - build-ios.mts - checkChannel - channel', channel);
const taskName = '书链 App iOS 测试包';

// 配置验证函数
const validateConfig = async () => {
  // 验证环境变量
  const requiredEnvVars = [
    'EXPO_PUBLIC_APP_ID',
    'EXPO_PUBLIC_APP_IOS_PROJECT_NAME',
    'EXPO_PUBLIC_APP_NAME_FOR_USER',
  ];
  const missingVars = requiredEnvVars.filter(
    (varName) => !process.env[varName],
  );

  if (missingVars.length > 0) {
    console.error(
      'validateConfig',
      `缺少必要的环境变量: ${missingVars.join(', ')}`,
    );
    return false;
  }

  // 验证热更新配置
  if (!hotFixJSONFileName || !hotFixManifestUrl) {
    console.error(
      'validateConfig',
      '热更新配置不完整，请检查 expo-updates.mts 配置',
    );
    return false;
  }

  // 到这里，已经确认 appId 存在，可以安全调用 fetchAppVersion
  // 从 API 获取版本号
  appVersion = await fetchAppVersion(appId as string);
  console.log('🐳 - build-ios.mts - validateConfig - appVersion', appVersion);

  if (!appVersion) {
    console.error('validateConfig', '无法从 API 获取应用版本号');
    return false;
  }

  return true;
};

// 使用异步 IIFE 初始化程序
(async () => {
  // 验证配置
  if (!(await validateConfig())) {
    exit(1);
  }

  // 在验证通过后进行类型断言，确保 TypeScript 知道这些变量不会是 undefined
  const appIdValue = appId as string;
  const appVersionValue = appVersion as string;
  const appNameValue = appName as string;
  const iOSProjectNameValue = iOSProjectName as string;

  const platform: UpdatePlatform = 'ios';
  const forceBuildNative = argv.forceBuildNative;
  const forceSkipBuildNative = argv.forceSkipBuildNative;
  const targetRuntimeVersion = argv.targetRuntimeVersion;
  const xcodePath = argv.xcodePath;
  const disabledAutoUploadSentry = argv.disabledAutoUploadSentry;
  const podRepoUpdate = argv.podRepoUpdate;
  const addNewTestDevice = argv.addNewTestDevice;
  console.log(
    '🐳 - build-ios.mts - checkForceBuildNative - forceBuildNative',
    forceBuildNative,
  );
  console.log(
    '🐳 - build-ios.mts - checkForceSkipBuildNative - forceSkipBuildNative',
    forceSkipBuildNative,
  );
  console.log(
    '🐳 - build-ios.mts - checkTargetRuntimeVersion - targetRuntimeVersion',
    targetRuntimeVersion,
  );
  console.log('🐳 - build-ios.mts - checkXcodePath - xcodePath', xcodePath);
  console.log(
    '🐳 - build-ios.mts - checkDisabledAutoUploadSentry - disabledAutoUploadSentry',
    disabledAutoUploadSentry,
  );
  console.log(
    '🐳 - build-ios.mts - podRepoUpdate - podRepoUpdate',
    podRepoUpdate,
  );
  console.log(
    '🐳 - build-ios.mts - addNewTestDevice - addNewTestDevice',
    addNewTestDevice,
  );

  /**
   * 获取运行时版本
   * 根据不同情况获取或更新 runtime version
   * @param forceUpdate 是否强制更新 runtime version
   * @returns 运行时版本或 undefined (失败时)
   */
  const getRuntimeVersion = async (
    forceUpdate = false,
  ): Promise<string | undefined> => {
    console.log(
      `🐳 - build-ios.mts - getRuntimeVersion - 开始获取运行时版本 (强制更新: ${forceUpdate})`,
    );

    try {
      // 如果需要强制更新 runtime version
      if (forceUpdate) {
        const latestVersion = await updateRuntimeVersion({
          appId: appIdValue,
          appVersion: appVersionValue,
          platform,
        });
        console.log(
          `🐳 - build-ios.mts - getRuntimeVersion - 已更新运行时版本: ${latestVersion}`,
        );
        return latestVersion;
      }

      // 否则，优先使用传入的目标版本
      if (targetRuntimeVersion && targetRuntimeVersion.length > 0) {
        console.log(
          `🐳 - build-ios.mts - getRuntimeVersion - 使用指定的运行时版本: ${targetRuntimeVersion}`,
        );
        return targetRuntimeVersion;
      }

      // 如果没有指定版本，获取最新版本
      const latestVersion = await getLatestRuntimeVersion({
        appId: appIdValue,
        platform,
      });
      console.log(
        `🐳 - build-ios.mts - getRuntimeVersion - 获取最新运行时版本: ${latestVersion}`,
      );
      return latestVersion;
    } catch (error) {
      console.error(
        `🐳 - build-ios.mts - getRuntimeVersion - 获取运行时版本失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      return undefined;
    }
  };

  /**
   * 获取或创建 Expo 更新配置
   * @param runtimeVersion 运行时版本
   * @returns 配置对象或 undefined (失败时)
   */
  const getOrCreateExpoConfig = async (runtimeVersion: string) => {
    console.log(
      `🐳 - build-ios.mts - getOrCreateExpoConfig - 开始获取 Expo 配置 (运行时版本: ${runtimeVersion})`,
    );

    try {
      // 尝试获取已有配置
      const config = await fetchExpoUpdatesConfig({
        appId: appIdValue,
        platform,
        channel,
        latestRuntimeVersion: runtimeVersion,
      });

      // 如果配置存在，直接返回
      if (config) {
        console.log(
          '🐳 - build-ios.mts - getOrCreateExpoConfig - 成功获取已有配置',
        );
        return config;
      }

      // 否则创建新配置
      console.log(
        '🐳 - build-ios.mts - getOrCreateExpoConfig - 未找到配置，创建新配置',
      );
      await addNewExpoUpdatesConfig({
        appId: appIdValue,
        channel,
        platform,
        latestRuntimeVersion: runtimeVersion,
        checkOnLaunch: ExpoUpdatesCheckOnLaunchType.Never,
      });

      // 获取并返回新创建的配置
      const newConfig = await fetchExpoUpdatesConfig({
        appId: appIdValue,
        platform,
        channel,
        latestRuntimeVersion: runtimeVersion,
      });

      if (!newConfig) {
        throw new Error('创建配置后仍然无法获取配置');
      }

      console.log(
        '🐳 - build-ios.mts - getOrCreateExpoConfig - 成功创建并获取新配置',
      );
      return newConfig;
    } catch (error) {
      console.error(
        `🐳 - build-ios.mts - getOrCreateExpoConfig - 获取配置失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      return undefined;
    }
  };

  const run = async () => {
    try {
      console.log(
        `🐳 build-ios.mts - run - 🔥🔥🔥🔥🔥🔥 ${taskName} - 开始打包`,
      );

      // 检查 Node
      await checkNodeEnv();

      // 准备环境变量
      await prepareEnvironmentVariables(disabledAutoUploadSentry);

      let hasNativeChange = await checkGitDiffHasNativeChange();
      // const hasNativeChange = true;
      console.log(
        '🚀 ~ run ~ hasNativeChange:',
        hasNativeChange,
        forceBuildNative,
        forceSkipBuildNative,
      );

      const shouldBuildNative =
        (hasNativeChange || forceBuildNative) && !forceSkipBuildNative;

      if (shouldBuildNative) {
        console.log('🐳 - build-ios.mts - run - 执行原生代码构建流程');

        await prepareEnvironmentVariablesForNativeBuild();

        // 获取更新的运行时版本
        let updateRuntimeVersionIfNeeded = true;

        // 如果是添加新测试设备的场景:
        // - 有原生代码变更时,需要更新运行时版本
        // - 无原生代码变更时,不需要更新运行时版本
        if (addNewTestDevice) {
          if (hasNativeChange) {
            updateRuntimeVersionIfNeeded = true;
          } else {
            updateRuntimeVersionIfNeeded = false;
          }
        }

        // 获取最新的运行时版本,根据 updateRuntimeVersionIfNeeded 决定是否强制更新
        const latestRuntimeVersion = await getRuntimeVersion(
          updateRuntimeVersionIfNeeded,
        );
        if (!latestRuntimeVersion) {
          console.error(
            '🐳 - build-ios.mts - run - 无法获取运行时版本，中止构建',
          );
          exit(1);
        }

        // 导出 JS bundle
        const exportResult = await exportJSBundle({ platform });
        if (!exportResult) {
          console.error(
            '🐳 - build-ios.mts - run - 导出 JS bundle 失败，中止构建',
          );
          exit(1);
        }

        // 获取或创建配置
        const config = await getOrCreateExpoConfig(latestRuntimeVersion);
        if (!config) {
          console.error(
            '🐳 - build-ios.mts - run - 无法获取 Expo 配置，中止构建',
          );
          exit(1);
        }

        // 上传 JS bundle 到 CDN
        const uploadResult = await uploadJSBundleToCDN({
          appId: appIdValue,
          channel,
          platform,
          latestRuntimeVersion,
        });

        if (!uploadResult) {
          console.error(
            '🐳 - build-ios.mts - run - 上传 JS bundle 到 CDN 失败，中止构建',
          );
          exit(1);
        }

        // 写入配置文件
        const expoUpdatesConfigFilePath = path.join(
          __dirname,
          '..',
          `${platform}/${iOSProjectNameValue}/${hotFixJSONFileName}`,
        );
        await writeExpoUpdatesConfig({
          config,
          expoUpdatesConfigFilePath,
        });

        cd('ios');

        await clearDerivedData();

        console.log('🐳 - build-ios.mts - run - 序列化 expoUpdatesConfig 开始');
        const configStr = JSON.stringify(config);
        console.log(
          '🐳 - build-ios.mts - run - 序列化 expoUpdatesConfig 完成 - ',
          configStr,
        );

        await bundleIOS(latestRuntimeVersion, configStr);
      } else {
        console.log('🐳 - build-ios.mts - run - 执行 js bundle 构建流程');

        // 获取运行时版本 (不强制更新)
        const latestRuntimeVersion = await getRuntimeVersion(false);
        if (!latestRuntimeVersion) {
          console.error(
            '🐳 - build-ios.mts - run - 无法获取运行时版本，中止构建',
          );
          exit(1);
        }

        // 导出 JS bundle
        const exportResult = await exportJSBundle({ platform });
        if (!exportResult) {
          console.error(
            '🐳 - build-ios.mts - run - 导出 JS bundle 失败，中止构建',
          );
          exit(1);
        }

        // 获取或创建配置 (隐式创建，不需要单独处理返回值)
        await getOrCreateExpoConfig(latestRuntimeVersion);

        // 上传 JS bundle 到 CDN
        const uploadResult = await uploadJSBundleToCDN({
          appId: appIdValue,
          channel,
          platform,
          latestRuntimeVersion,
        });

        if (uploadResult) {
          await sendJSBundleUpdatedMsg({
            appId: appIdValue,
            channel,
            appName: appNameValue,
            latestRuntimeVersion,
            platform,
          });
          exit(0);
        } else {
          console.error(
            '🐳 - build-ios.mts - run - 上传 JS bundle 到 CDN 失败，中止构建',
          );
          exit(1);
        }
      }
    } catch (error) {
      console.error(
        `build-ios.mts 构建过程中发生错误: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      exit(1);
    }
  };

  /**
   * 清理 DerivedData，防止出现 main.jsbundle 无法打包成功的问题
   */
  const clearDerivedData = async () => {
    try {
      // 获取构建设置信息
      const { stdout: buildSettingsOutput } =
        await $`xcodebuild -workspace ${iOSProjectNameValue}.xcworkspace -scheme ${iOSProjectNameValue} -showBuildSettings`;

      // 使用正则表达式精确匹配 BUILD_DIR
      const buildDirMatch = buildSettingsOutput.match(
        /\s*BUILD_DIR\s*=\s*(.+)$/m,
      );
      let derivedDataPath = buildDirMatch?.[1]?.trim();

      if (!derivedDataPath) {
        console.warn(
          '🐳 - build-ios.mts - clearDerivedData - 无法通过 BUILD_DIR 找到 derivedDataPath，尝试其他方法...',
        );
        // 尝试备用方法或使用默认路径
        derivedDataPath = path.join(
          process.env.HOME || '',
          'Library/Developer/Xcode/DerivedData',
        );
      } else {
        derivedDataPath = derivedDataPath.replace('/Build/Products', '');
      }

      console.log(
        `🐳 - build-ios.mts - clearDerivedData - 使用 derivedDataPath: ${derivedDataPath}`,
      );

      // 清理 DerivedData 缓存
      await $`xcodebuild clean -workspace ${iOSProjectNameValue}.xcworkspace -scheme ${iOSProjectNameValue} -derivedDataPath ${derivedDataPath}`;
      console.log(
        '🐳 - build-ios.mts - clearDerivedData - DerivedData 缓存清理成功',
      );
    } catch (error) {
      console.error(
        '🐳 - build-ios.mts - clearDerivedData - 清理 DerivedData 时发生错误:',
        error instanceof Error ? error.message : String(error),
      );
      console.warn(
        '🐳 - build-ios.mts - clearDerivedData - 继续执行后续步骤，但可能会影响构建结果',
      );
    }
  };

  const execWithTimeout = async (cmd, timeoutMs = 600000) => {
    return Promise.race([
      cmd,
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('命令执行超时')), timeoutMs),
      ),
    ]);
  };

  const timeStart = (label) => {
    console.time(`⏱️ ${label}`);
    return () => console.timeEnd(`⏱️ ${label}`);
  };

  /**
   * 构建 iOS 原生包
   */
  const bundleIOS = async (latestRuntimeVersion: string, configStr: string) => {
    console.log('🐳 - build-ios.mts - bundleIOS - Bundle Install 开始');
    await $`bundle config set path 'vendor/bundle'`;
    await $`bundle install`;
    await $`bundle update fastlane`;
    // 删除重复的配置设置
    console.log('🐳 - build-ios.mts - bundleIOS - Bundle Install 结束');

    console.log('🐳 - build-ios.mts - bundleIOS - Pod Install 开始');
    const endPodInstall = timeStart('Pod Install');
    try {
      if (podRepoUpdate) {
        await execWithTimeout(
          $`bundle exec pod install --repo-update --verbose`,
          10 * 60 * 1000,
        ); // 10分钟超时
      } else {
        await execWithTimeout(
          $`bundle exec pod install --verbose`,
          10 * 60 * 1000,
        ); // 10分钟超时
      }
      console.log('🐳 - build-ios.mts - bundleIOS - Pod Install 结束');
    } catch (error) {
      console.error(
        `🐳 - build-ios.mts - bundleIOS - Pod Install 失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      throw error;
    } finally {
      endPodInstall();
    }

    try {
      console.log('🐳 - build-ios.mts - bundleIOS - Fastlane 构建开始');
      const endFastlane = timeStart('Fastlane 构建');
      await execWithTimeout(
        $`bundle exec fastlane app app_id:${appIdValue} runtime_version:${latestRuntimeVersion} updates_url:${hotFixManifestUrl} expo_update_config:${configStr} xcode_path:${xcodePath}`,
        60 * 60 * 1000, // 60分钟超时
      );
      console.log('🐳 - build-ios.mts - bundleIOS - Fastlane 构建结束');
      endFastlane();
      exit(0);
    } catch (error) {
      console.error(
        `🐳 - build-ios.mts - bundleIOS - Fastlane 构建失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      exit(1);
    }
  };

  // 启动构建流程
  await run();
})();
