#! /usr/bin/env zx
import { existsSync } from 'fs';
import { readFile } from 'fs/promises';
import https from 'https';
import { exit } from 'process';
import { fileURLToPath } from 'url';
import { echo, path } from 'zx';
import { getCommitInfo, getCurrentAuthor, getGitCommit } from './git-util.mts';

const [
  nodePath,
  scriptPath,
  appIdParam,
  platformParam,
  envParam,
  runtimeVersionParam,
  messageTitleParam,
  ...restArgs
] = process.argv;

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const robotKeyConfigFileName = 'fei_shu_robot_key.json';
const larkRobotAPIBaseUrl = 'https://open.feishu.cn/open-apis/bot/v2/hook/';

type RobotConfig = {
  [key: string]: {
    ios: {
      normalBuild: string;
      devBuild: string;
    };
    android: {
      normalBuild: string;
      devBuild: string;
    };
  };
};

const checkArguments = () => {
  if (!appIdParam.includes('=')) {
    echo(`${__filename} 执行失败，请检查 appId 参数`);
    exit(1);
  }
  if (!platformParam.includes('=')) {
    echo(`${__filename} 执行失败，请检查 platform 参数`);
    exit(1);
  }
  if (!envParam.includes('=')) {
    echo(`${__filename} 执行失败，请检查 env 参数`);
    exit(1);
  }
  if (!runtimeVersionParam.includes('=')) {
    echo(`${__filename} 执行失败，请检查 runtimeVersion 参数`);
    exit(1);
  }
  if (!messageTitleParam.includes('=')) {
    echo(`${__filename} 执行失败，请检查 messageTitle 参数`);
    exit(1);
  }
};

const parseArguments = () => {
  const appId = appIdParam?.slice('appId='.length);
  const platform = platformParam?.slice('platform='.length);
  const env = envParam?.slice('env='.length);
  const runtimeVersion = runtimeVersionParam?.slice('runtimeVersion='.length);
  const messageTitle = messageTitleParam?.slice('messageTitle='.length);
  return {
    appId,
    platform,
    env,
    runtimeVersion,
    messageTitle,
  };
};

/**
 * 解析机器人配置文件
 * @param robotKeyConfigFileNamePath 机器人配置文件路径
 * @returns 机器人 token
 */
const parseRobotKeyConfig = async (param: {
  robotKeyConfigFileNamePath: string;
  appId: string;
  platform: string;
  buildType: string;
}): Promise<string> => {
  const { robotKeyConfigFileNamePath, appId, platform, buildType } = param;
  const data = await readFile(robotKeyConfigFileNamePath, 'utf-8');
  return new Promise((resolve, reject) => {
    try {
      const jsonData: RobotConfig = JSON.parse(data);
      echo('机器人配置文件内容', data);

      if (jsonData[appId]) {
        const config = jsonData[appId];
        if (config[platform]) {
          const platformConfig = config[platform];
          if (platformConfig[buildType]) {
            const token = platformConfig[buildType];
            echo(`${appId}-${platform}-${buildType} 对应的 Token 为 ${token}`);
            resolve(token);
          } else {
            echo(
              `buildType-${buildType} 在机器人配置文件中未匹配到，请检查参数`,
            );
            reject(
              `buildType-${buildType} 在机器人配置文件中未匹配到，请检查参数`,
            );
          }
        } else {
          echo(`platform-${platform} 在机器人配置文件中未匹配到，请检查参数`);
          reject(`platform-${platform} 在机器人配置文件中未匹配到，请检查参数`);
        }
      } else {
        echo(`appId-${appId} 在机器人配置文件中未匹配到，请检查参数`);
        reject(`appId-${appId} 在机器人配置文件中未匹配到，请检查参数`);
      }
    } catch (error) {
      echo('解析机器人配置文件失败', error.toString());
      reject(error.toString());
    }
  });
};

/**
 * 发送消息到飞书机器人
 * @param param 参数
 * @returns
 */
const sendMsgToRobot = async (param: {
  larkRobotRequestUri: string;
  msgBody: string;
}): Promise<boolean> => {
  const { larkRobotRequestUri, msgBody } = param;
  const uri = new URL(larkRobotRequestUri);
  const options: https.RequestOptions = {
    hostname: uri.hostname,
    port: 443,
    path: uri.pathname,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
    },
  };

  const body = msgBody;

  return new Promise<boolean>((resolve) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.setEncoding('utf8');

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        const response = JSON.parse(data);
        if (response.StatusCode === 0) {
          resolve(true);
        } else {
          echo(`发送打包消息到机器人失败，失败原因：${response.msg}`);
          // throw new Error(response.msg);
          resolve(false);
        }
      });
    });

    req.write(body);
    req.end();
  });
};

/**
 * 构建消息体
 * @param imageKey 图片 key
 * @returns 消息体
 */
const constructMsgBody = async (param: {
  messageTitle: string;
  env: string;
  runtimeVersion: string;
}): Promise<string> => {
  const { messageTitle, env, runtimeVersion } = param;
  const commitInfo = await getCommitInfo();
  echo(`commitInfo - ${commitInfo}`);
  const gitCommit = await getGitCommit();
  const msgBody = {
    msg_type: 'post',
    content: {
      post: {
        zh_cn: {
          title: messageTitle,
          content: [
            [
              {
                tag: 'text',
                text: `环境：${env}\n\n`,
              },
              {
                tag: 'text',
                text: `版本(原生代码有变动后会更新): ${runtimeVersion}\n\n`,
              },
              {
                tag: 'text',
                text: `代码：${gitCommit}\n`,
              },
              {
                tag: 'text',
                text: `操作：${
                  process.env.BUILD_USER ?? (await getCurrentAuthor())
                }\n`,
              },
            ],
          ],
        },
      },
    },
  };
  if (commitInfo) {
    msgBody.content.post.zh_cn.content[0].push({
      tag: 'text',
      text: `${commitInfo}\n`,
    });
  }
  return JSON.stringify(msgBody);
};

const run = async () => {
  checkArguments();
  const { appId, platform, env, runtimeVersion, messageTitle } =
    parseArguments();

  const robotKeyConfigFileNamePath = path.resolve(
    __dirname,
    robotKeyConfigFileName,
  );
  if (!existsSync(robotKeyConfigFileNamePath)) {
    echo(`${robotKeyConfigFileNamePath} 不存在`);
    return false;
  } else {
    // 1.获取机器人 token
    const token = await parseRobotKeyConfig({
      robotKeyConfigFileNamePath,
      appId,
      buildType: 'normalBuild',
      platform,
    });

    const msgBody = await constructMsgBody({
      messageTitle,
      env,
      runtimeVersion,
    });

    if (msgBody.length > 0) {
      // 6.调用飞书机器人 API 进行发送
      const larkRobotRequestUri = `${larkRobotAPIBaseUrl}${token}`;
      return await sendMsgToRobot({ larkRobotRequestUri, msgBody });
    } else {
      return false;
    }
  }
};

run();
