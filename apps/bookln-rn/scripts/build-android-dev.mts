#! /usr/bin/env zx
/* eslint-disable @typescript-eslint/ban-ts-comment */
import { $, cd, echo } from 'zx';
import {
  checkNodeEnv,
  getOSS,
  prepareEnvironmentVariables,
} from './build-common.mts';

const run = async () => {
  echo('书链 App Android 开发包 - 开始打包');

  // 检查 Node
  await checkNodeEnv();

  // 准备环境变量
  await prepareEnvironmentVariables();

  await getOSS();

  cd('android');

  await bundleAndroid();
};

const bundleAndroid = async () => {
  echo('uv 开始');
  await $`uv venv --python 3.10.18`;
  await $`uv sync --upgrade`;
  echo('uv 结束');
  await $`if [ ! -f "local.properties" ]; then
    echo "sdk.dir=/Users/<USER>/Library/Android/sdk" >> local.properties
  fi`;
  await $`commitHash='git rev-parse --short HEAD'`;
  await $`chmod +x gradlew`;
  await $`./gradlew clean assembleDebug -Pappid=2`;
  cd('../scripts');
  await $`tsx ./upload-android-apk.mts 2 daily true`;
};

run();
