// #! /usr/bin/env zx
// /* eslint-disable @typescript-eslint/ban-ts-comment */
// import 'dotenv/config';

// import { $, cd, echo } from 'zx';
// import {
//   checkNodeEnv,
//   getOSS,
//   prepareEnvironmentVariables,
// } from './build-common.mts';
// import { fileURLToPath } from 'url';
// import path, { dirname } from 'path';
// import { UpdateChannel, UpdatePlatform } from './common.mts';
// import { exit } from 'process';
// import { Builder, parseString } from 'xml2js';
// import { exists } from 'fs-extra';
// import { readFile, writeFile } from 'fs/promises';
// import { checkGitDiffHasNativeChange } from './check-git-diff.mts';
// import {
//   updateRuntimeVersion,
//   addNewExpoUpdatesConfig,
//   ExpoUpdatesCheckOnLaunchType,
//   fetchExpoUpdatesConfig,
//   hotFixJSONFileName,
//   writeExpoUpdatesConfig,
//   getLatestRuntimeVersion,
//   exportJSBundle,
//   uploadJSBundleToCDN,
//   sendJSBundleUpdatedMsg,
// } from './expo-updates.mts';

// const [
//   nodePath,
//   scriptPath,
//   // skipBuild,
//   // forceBuildNative,
//   // updateReleaseChannel,
//   // forceSkipBuildNative,
//   // targetRuntimeVersion,
//   ...restArgs
// ] = process.argv;

// const __filename = fileURLToPath(import.meta.url);
// const __dirname = dirname(__filename);

// const appVersion = process.env.EXPO_PUBLIC_APP_VERSION;
// echo(`appVersion - ${appVersion}`);
// const appId = process.env.EXPO_PUBLIC_APP_ID?.toString();
// echo(`appId - ${appId}`);
// const appName = process.env.EXPO_PUBLIC_APP_NAME_FOR_USER;
// echo(`appName - ${appName}`);
// const updateReleaseChannel = restArgs
//   .find((arg) => arg.startsWith('--updateReleaseChannel='))
//   ?.split('=')[1];
// const channel: UpdateChannel =
//   updateReleaseChannel == null
//     ? 'staging'
//     : (updateReleaseChannel as UpdateChannel);
// const taskName = '书链 App Android 渠道包';

// if (!appName) {
//   echo('请检查 .env 文件中 EXPO_PUBLIC_APP_NAME_FOR_USER 是否已配置');
//   exit(1);
// }
// if (!appId) {
//   echo('请检查 .env 文件中 EXPO_PUBLIC_APP_ID 是否已配置');
//   exit(1);
// }
// if (!appVersion) {
//   echo('请检查 .env 文件中 EXPO_PUBLIC_APP_VERSION 是否已配置');
//   exit(1);
// }
// const platform: UpdatePlatform = 'android';
// const skipBuildAndJustUploadReleaseAPK = restArgs
//   .find((arg) => arg.startsWith('--skipBuildAndJustUploadReleaseAPK='))
//   ?.split('=')[1];
// const forceBuildNative = restArgs
//   .find((arg) => arg.startsWith('--forceBuildNative='))
//   ?.split('=')[1];
// const forceSkipBuildNative = restArgs
//   .find((arg) => arg.startsWith('--forceSkipBuildNative='))
//   ?.split('=')[1];
// const targetRuntimeVersion = restArgs
//   .find((arg) => arg.startsWith('--targetRuntimeVersion='))
//   ?.split('=')[1];

// console.log(
//   'skipBuildAndJustUploadReleaseAPK:',
//   skipBuildAndJustUploadReleaseAPK,
// );
// console.log('forceBuildNative:', forceBuildNative);
// console.log('updateReleaseChannel:', updateReleaseChannel);
// console.log('forceSkipBuildNative:', forceSkipBuildNative);
// console.log('targetRuntimeVersion:', targetRuntimeVersion);

// // 替换xmL中 expo_runtime_version 的值
// function replaceExpoRuntimeVersionAndChannel(
//   xmlContent: string,
//   newVersion: string,
//   newChannel: string,
// ): Promise<string> {
//   return new Promise((resolve, reject) => {
//     parseString(xmlContent, (err, result) => {
//       if (err) {
//         return reject(err);
//       }

//       // 查找并替换 expo_runtime_version
//       const strings = result.resources.string;
//       const expoRuntimeVersionItem = strings.find(
//         (item: any) => item.$.name === 'expo_runtime_version',
//       );
//       const expoReleaseChannelItem = strings.find(
//         (item: any) => item.$.name === 'expo_release_channel',
//       );
//       if (expoRuntimeVersionItem) {
//         expoRuntimeVersionItem._ = newVersion;
//       }
//       if (expoReleaseChannelItem) {
//         expoReleaseChannelItem._ = newChannel;
//       }

//       // 将JavaScript对象转换回XML字符串
//       const builder = new Builder({ headless: true });
//       const updatedXmlContent = builder.buildObject(result);

//       resolve(updatedXmlContent);
//     });
//   });
// }

// const updateExpoUpdatesConfigInAndroidNativeEnd = async (
//   latestRuntimeVersion: string,
//   channel: UpdateChannel,
// ) => {
//   // android/app/src/main/res/values/strings.xml
//   const androidStringXMLFileRelativePath =
//     'android/app/src/main/res/values/strings.xml';
//   // 1.确保路径对应的 xml 文件是否存在
//   // 1.1 切换到 android 目录
//   const stringsXMLPath = path.join(
//     __dirname,
//     '..',
//     androidStringXMLFileRelativePath,
//   );
//   try {
//     const stringsXMLPathExists = await exists(stringsXMLPath);
//     if (!stringsXMLPathExists) {
//       console.log(
//         `androidStringXMLFileRelativePath - ${stringsXMLPath} 不存在`,
//       );
//       exit(1);
//     }

//     // 读取XML文件内容
//     const xmlContent = await readFile(stringsXMLPath, 'utf-8');

//     // 替换 expo_runtime_version
//     const updatedXmlContent = await replaceExpoRuntimeVersionAndChannel(
//       xmlContent,
//       latestRuntimeVersion,
//       channel,
//     );

//     // 将更新后的XML内容写入文件
//     await writeFile(stringsXMLPath, updatedXmlContent);
//   } catch (error) {
//     console.log('更新 Android runtimeVersion 失败');
//   }
// };

// const run = async () => {
//   echo('书链 App Android 渠道包 - 开始打包');

//   // 检查 Node
//   await checkNodeEnv();

//   // 准备环境变量
//   await prepareEnvironmentVariables();

//   let hasNativeChange = await checkGitDiffHasNativeChange();
//   if (
//     (hasNativeChange || forceBuildNative === 'true') &&
//     forceSkipBuildNative === 'false'
//   ) {
//     console.log('AAAAAAAAAAAAA');
//     const latestRuntimeVersion = await updateRuntimeVersion({
//       appId: appId,
//       appVersion: appVersion,
//       platform,
//     });
//     if (!latestRuntimeVersion) {
//       return;
//     }
//     await addNewExpoUpdatesConfig({
//       appId: appId,
//       channel,
//       platform,
//       latestRuntimeVersion,
//       checkOnLaunch: ExpoUpdatesCheckOnLaunchType.Never,
//     });
//     const config = await fetchExpoUpdatesConfig({
//       appId: appId,
//       platform,
//       channel,
//       latestRuntimeVersion,
//     });
//     if (!config) {
//       return;
//     }
//     const expoUpdatesConfigFilePath = path.join(
//       __dirname,
//       '..',
//       `${platform}/app/src/main/assets/${hotFixJSONFileName}`,
//     );
//     await writeExpoUpdatesConfig({
//       config,
//       expoUpdatesConfigFilePath,
//     });

//     updateExpoUpdatesConfigInAndroidNativeEnd(latestRuntimeVersion, channel);

//     cd('android');
//     await bundleAndroid(latestRuntimeVersion);
//   } else {
//     // 优先取 targetRuntimeVersion
//     let latestRuntimeVersion: string | undefined = targetRuntimeVersion;
//     // 如果 targetRuntimeVersion 不存在或长度为 0，则调接口获取最新的 runtimeVersion
//     if (!latestRuntimeVersion || latestRuntimeVersion.length === 0) {
//       latestRuntimeVersion = await getLatestRuntimeVersion({
//         appId,
//         platform,
//       });
//     }
//     if (!latestRuntimeVersion) {
//       return;
//     }
//     const exportResult = await exportJSBundle({
//       platform,
//     });
//     if (exportResult) {
//       const uploadResult = await uploadJSBundleToCDN({
//         appId,
//         channel,
//         platform,
//         latestRuntimeVersion,
//       });
//       if (uploadResult) {
//         await sendJSBundleUpdatedMsg({
//           appId,
//           channel,
//           appName,
//           latestRuntimeVersion,
//           platform,
//         });
//       }
//     }
//   }
// };

// const bundleAndroid = async (latestRuntimeVersion: string) => {
//   if (skipBuildAndJustUploadReleaseAPK === 'true') {
//     await getOSS();
//     cd('../scripts');
//     await $`tsx ./upload-android-apk.mts ${appId} release false`;
//   } else {
//     echo('pipenv 开始');
//     await $`PIPENV_YES=1 pipenv --python 3.10.18`;
//     await $`pipenv update`;
//     await $`pipenv sync`;
//     echo('pipenv 结束');
//     await $`if [ ! -f "local.properties" ]; then
//       echo "sdk.dir=/Users/<USER>/Library/Android/sdk" >> local.properties
//     fi`;
//     await $`pipenv run python3 generate-channel-txt.py`;
//     await $`commitHash='git rev-parse --short HEAD'`;
//     await $`chmod +x gradlew`;
//     await $`./gradlew clean assembleRelease`;
//     // 加固脚本中也会用到 oss，所以需要 getOSS 配置
//     cd('..');
//     await getOSS();
//     cd('android');
//     await $`pipenv run python3 jiagu-channel.py ${appId}`;
//     await $`./gradlew reBuildChannel`;
//     cd('..');
//     // 为了确保上传到 OSS 成功，需要再次 getOSS
//     await getOSS();
//     cd('scripts');
//     await $`tsx ./upload-android-apk.mts ${appId} release false`;
//   }
// };

// run();
