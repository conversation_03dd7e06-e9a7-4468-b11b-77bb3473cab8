#! /usr/bin/env zx
/* eslint-disable @typescript-eslint/ban-ts-comment */
import 'dotenv/config';
import { exists } from 'fs-extra';
import { readFile, writeFile } from 'node:fs/promises';
import path, { dirname } from 'node:path';
import { exit } from 'node:process';
import { fileURLToPath } from 'node:url';
import { Builder, parseString } from 'xml2js';
import yargs from 'yargs';
import { $, cd } from 'zx';
import {
  checkNodeEnv,
  getOSS,
  prepareEnvironmentVariables,
  prepareEnvironmentVariablesForNativeBuild,
} from './build-common.mts';
import { checkGitDiffHasNativeChange } from './check-git-diff.mts';
import type { UpdateChannel, UpdatePlatform } from './common.mts';
import {
  ExpoUpdatesCheckOnLaunchType,
  addNewExpoUpdatesConfig,
  exportJSBundle,
  fetchExpoUpdatesConfig,
  getLatestRuntimeVersion,
  hotFixJSONFileName,
  sendJSBundleUpdatedMsg,
  updateRuntimeVersion,
  uploadJSBundleToCDN,
  writeExpoUpdatesConfig,
} from './expo-updates.mts';

// 使用 yargs 解析命令行参数
const argv = yargs(process.argv.slice(2))
  .option('forceBuildNative', { type: 'boolean', default: false })
  .option('updateReleaseChannel', { type: 'string', default: 'staging' })
  .option('forceSkipBuildNative', { type: 'boolean', default: false })
  .option('targetRuntimeVersion', { type: 'string' })
  .option('disabledAutoUploadSentry', { type: 'boolean', default: false })
  .option('isAndroidChannelRelease', { type: 'boolean', default: false })
  .option('skipBuildAndJustUploadReleaseAPK', {
    type: 'boolean',
    default: false,
  })
  .parse();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 替换 XML 中 expo_runtime_version 的值
function replaceExpoRuntimeVersionAndChannel(
  xmlContent: string,
  newVersion: string,
  newChannel: string,
): Promise<string> {
  return new Promise((resolve, reject) => {
    parseString(xmlContent, (err, result) => {
      if (err) {
        return reject(err);
      }

      // 查找并替换 expo_runtime_version
      const strings = result.resources.string;
      const expoRuntimeVersionItem = strings.find(
        (item: any) => item.$.name === 'expo_runtime_version',
      );
      const expoReleaseChannelItem = strings.find(
        (item: any) => item.$.name === 'expo_release_channel',
      );
      if (expoRuntimeVersionItem) {
        expoRuntimeVersionItem._ = newVersion;
      }
      if (expoReleaseChannelItem) {
        expoReleaseChannelItem._ = newChannel;
      }

      // 将JavaScript对象转换回XML字符串
      const builder = new Builder({ headless: true });
      const updatedXmlContent = builder.buildObject(result);

      resolve(updatedXmlContent);
    });
  });
}

type AppVersionResponse = {
  success: boolean;
  data: {
    miniAPPVersion: string;
  };
};

/**
 * 从 API 获取应用的版本号
 * @param appId 应用ID
 * @returns Promise<string | undefined> 应用版本号或 undefined（获取失败时）
 */
const fetchAppVersion = async (appId: string): Promise<string | undefined> => {
  try {
    console.log('🐳 - build-android.mts - fetchAppVersion - 开始从 API 获取应用版本');
    const response = await fetch(
      `https://app-prepub.bookln.cn/appservice/miniappinfo.do?appid=${appId}&type=2`,
    );

    if (!response.ok) {
      throw new Error(`API 请求失败: ${response.status} ${response.statusText}`);
    }

    const data: AppVersionResponse = (await response.json()) as AppVersionResponse;

    if (!data.success) {
      throw new Error(`API 返回错误: ${JSON.stringify(data)}`);
    }

    const version = data.data?.miniAPPVersion;

    if (!version) {
      throw new Error('API 返回数据中未找到 miniAPPVersion 字段');
    }

    // 处理版本号格式，去掉前缀 'v'
    const formattedVersion = version.startsWith('v') ? version.substring(1) : version;

    console.log(
      `🐳 - build-android.mts - fetchAppVersion - 成功获取版本号: ${version} -> ${formattedVersion}`,
    );
    return formattedVersion;
  } catch (error) {
    console.error(
      `🐳 - build-android.mts - fetchAppVersion - 获取版本号失败: ${
        error instanceof Error ? error.message : String(error)
      }`,
    );
    return undefined;
  }
};

// 获取应用信息
let appVersion: string | undefined;

// 使用异步 IIFE 初始化程序
(async () => {
  // 获取应用信息
  const appId = process.env.EXPO_PUBLIC_APP_ID?.toString();
  const appName = process.env.EXPO_PUBLIC_APP_NAME_FOR_USER;
  const updateReleaseChannel = (argv.updateReleaseChannel as UpdateChannel) || 'staging';
  const channel: UpdateChannel = updateReleaseChannel;
  const platform: UpdatePlatform = 'android';
  const forceBuildNative = argv.forceBuildNative as boolean;
  const forceSkipBuildNative = argv.forceSkipBuildNative as boolean;
  const targetRuntimeVersion = argv.targetRuntimeVersion as string;
  const disabledAutoUploadSentry = argv.disabledAutoUploadSentry as boolean;
  const isAndroidChannelRelease = argv.isAndroidChannelRelease as boolean;
  const skipBuildAndJustUploadReleaseAPK = argv.skipBuildAndJustUploadReleaseAPK as boolean;

  const taskName = `书链 App Android ${isAndroidChannelRelease ? '渠道包' : '测试包'}`;

  // 记录时间并返回结束时间函数
  const timeStart = (label) => {
    console.time(`⏱️ ${label}`);
    return () => console.timeEnd(`⏱️ ${label}`);
  };

  // 带超时的命令执行
  const execWithTimeout = async (cmd, timeoutMs = 600000) => {
    return Promise.race([
      cmd,
      new Promise((_, reject) => setTimeout(() => reject(new Error('命令执行超时')), timeoutMs)),
    ]);
  };

  // 配置验证函数
  const validateConfig = async () => {
    console.log('🐳 - build-android.mts - validateConfig - 开始验证配置');

    // 验证环境变量
    const requiredEnvVars = [
      'EXPO_PUBLIC_APP_VERSION',
      'EXPO_PUBLIC_APP_ID',
      'EXPO_PUBLIC_APP_NAME_FOR_USER',
    ];

    const missingVars = requiredEnvVars.filter((varName) => !process.env[varName]);

    if (missingVars.length > 0) {
      console.error(
        '🐳 - build-android.mts - validateConfig - 缺少必要的环境变量:',
        missingVars.join(', '),
      );
      return false;
    }

    // 到这里，已经确认 appId 存在，可以安全调用 fetchAppVersion
    // 从 API 获取版本号
    appVersion = await fetchAppVersion(appId as string);
    console.log('🐳 - build-android.mts - validateConfig - appVersion', appVersion);

    if (!appVersion) {
      console.error('validateConfig', '无法从 API 获取应用版本号');
      return false;
    }

    console.log('🐳 - build-android.mts - validateConfig - 配置验证通过');
    return true;
  };

  /**
   * 更新 Android 原生端的 Expo 配置
   */
  const updateExpoUpdatesConfigInAndroidNativeEnd = async (
    latestRuntimeVersion: string,
    channel: UpdateChannel,
  ) => {
    console.log(
      '🐳 - build-android.mts - updateExpoUpdatesConfigInAndroidNativeEnd - 开始更新原生端配置',
    );

    // android/app/src/main/res/values/strings.xml
    const androidStringXMLFileRelativePath = 'android/app/src/main/res/values/strings.xml';
    const stringsXMLPath = path.join(__dirname, '..', androidStringXMLFileRelativePath);

    try {
      // 检查文件是否存在
      const stringsXMLPathExists = await exists(stringsXMLPath);
      if (!stringsXMLPathExists) {
        console.error(
          `🐳 - build-android.mts - updateExpoUpdatesConfigInAndroidNativeEnd - 配置文件不存在: ${stringsXMLPath}`,
        );
        throw new Error(`配置文件不存在: ${stringsXMLPath}`);
      }

      // 读取 XML 文件内容
      const xmlContent = await readFile(stringsXMLPath, 'utf-8');

      // 替换配置值
      const updatedXmlContent = await replaceExpoRuntimeVersionAndChannel(
        xmlContent,
        latestRuntimeVersion,
        channel,
      );

      // 将更新后的 XML 内容写入文件
      await writeFile(stringsXMLPath, updatedXmlContent);
      console.log(
        '🐳 - build-android.mts - updateExpoUpdatesConfigInAndroidNativeEnd - 成功更新原生端配置',
      );

      return true;
    } catch (error) {
      console.error(
        `🐳 - build-android.mts - updateExpoUpdatesConfigInAndroidNativeEnd - 更新失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      return false;
    }
  };

  /**
   * 获取运行时版本
   */
  const getRuntimeVersion = async (forceUpdate = false): Promise<string | undefined> => {
    console.log(
      `🐳 - build-android.mts - getRuntimeVersion - 开始获取运行时版本 (强制更新: ${forceUpdate})`,
    );

    try {
      // 如果需要强制更新 runtime version
      if (forceUpdate) {
        if (!appVersion) {
          throw new Error('应用版本号未找到，无法更新运行时版本');
        }

        const latestVersion = await updateRuntimeVersion({
          appId: appId as string,
          appVersion: appVersion,
          platform,
        });

        console.log(
          `🐳 - build-android.mts - getRuntimeVersion - 已更新运行时版本: ${latestVersion}`,
        );
        return latestVersion;
      }

      // 否则，优先使用传入的目标版本
      if (targetRuntimeVersion && targetRuntimeVersion.length > 0) {
        console.log(
          `🐳 - build-android.mts - getRuntimeVersion - 使用指定的运行时版本: ${targetRuntimeVersion}`,
        );
        return targetRuntimeVersion;
      }

      // 如果没有指定版本，获取最新版本
      const latestVersion = await getLatestRuntimeVersion({
        appId: appId as string,
        platform,
      });

      console.log(
        `🐳 - build-android.mts - getRuntimeVersion - 获取最新运行时版本: ${latestVersion}`,
      );
      return latestVersion;
    } catch (error) {
      console.error(
        `🐳 - build-android.mts - getRuntimeVersion - 获取运行时版本失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      return undefined;
    }
  };

  /**
   * 获取或创建 Expo 更新配置
   */
  const getOrCreateExpoConfig = async (runtimeVersion: string) => {
    console.log(
      `🐳 - build-android.mts - getOrCreateExpoConfig - 开始获取 Expo 配置 (运行时版本: ${runtimeVersion})`,
    );

    try {
      // 尝试获取已有配置
      const config = await fetchExpoUpdatesConfig({
        appId: appId as string,
        platform,
        channel,
        latestRuntimeVersion: runtimeVersion,
      });

      // 如果配置存在，直接返回
      if (config) {
        console.log('🐳 - build-android.mts - getOrCreateExpoConfig - 成功获取已有配置');
        return config;
      }

      // 否则创建新配置
      console.log('🐳 - build-android.mts - getOrCreateExpoConfig - 未找到配置，创建新配置');
      await addNewExpoUpdatesConfig({
        appId: appId as string,
        channel,
        platform,
        latestRuntimeVersion: runtimeVersion,
        checkOnLaunch: ExpoUpdatesCheckOnLaunchType.Never,
      });

      // 获取并返回新创建的配置
      const newConfig = await fetchExpoUpdatesConfig({
        appId: appId as string,
        platform,
        channel,
        latestRuntimeVersion: runtimeVersion,
      });

      if (!newConfig) {
        throw new Error('创建配置后仍然无法获取配置');
      }

      console.log('🐳 - build-android.mts - getOrCreateExpoConfig - 成功创建并获取新配置');
      return newConfig;
    } catch (error) {
      console.error(
        `🐳 - build-android.mts - getOrCreateExpoConfig - 获取配置失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      return undefined;
    }
  };

  /**
   * 准备 uv 环境
   */
  const uvEnvPreparation = async () => {
    console.log('🐳 - build-android.mts - uvEnvPreparation - 开始准备 uv 环境');

    try {
      const endUvPrep = timeStart('UV 准备');
      await execWithTimeout(
        $`uv venv --python 3.10.18`,
        3 * 60 * 1000, // 3分钟超时
      );
      await execWithTimeout($`uv sync --upgrade`, 3 * 60 * 1000);
      console.log('🐳 - build-android.mts - uvEnvPreparation - uv 环境准备完成');
      endUvPrep();
      return true;
    } catch (error) {
      console.error(
        `🐳 - build-android.mts - uvEnvPreparation - 准备 uv 环境失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      return false;
    }
  };

  /**
   * 导出 Android SDK 路径
   */
  const exportAndroidSDK = async () => {
    console.log('🐳 - build-android.mts - exportAndroidSDK - 设置 Android SDK 路径');

    try {
      await $`if [ ! -f "local.properties" ]; then
        echo "sdk.dir=${process.env.HOME || '/Users/<USER>'}/Library/Android/sdk" >> local.properties
      fi`;
      console.log('🐳 - build-android.mts - exportAndroidSDK - Android SDK 路径设置完成');
      return true;
    } catch (error) {
      console.error(
        `🐳 - build-android.mts - exportAndroidSDK - 设置 Android SDK 路径失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      return false;
    }
  };

  /**
   * 构建 Android 原生包
   */
  const bundleAndroid = async () => {
    console.log('🐳 - build-android.mts - bundleAndroid - 开始构建 Android 包');

    try {
      if (skipBuildAndJustUploadReleaseAPK && isAndroidChannelRelease) {
        // 仅上传逻辑
        console.log('🐳 - build-android.mts - bundleAndroid - 仅上传 APK，跳过构建');

        cd('..');
        await getOSS();

        const endUpload = timeStart('上传 APK');
        await execWithTimeout(
          $`npx tsx scripts/upload-android-apk.mts ${appId} release false`,
          10 * 60 * 1000,
        );
        endUpload();

        console.log('🐳 - build-android.mts - bundleAndroid - APK 上传完成');
      } else {
        // 标准构建逻辑
        console.log('🐳 - build-android.mts - bundleAndroid - 执行标准构建流程');

        // 准备环境
        await uvEnvPreparation();
        await exportAndroidSDK();

        // 处理渠道配置
        if (isAndroidChannelRelease) {
          await execWithTimeout($`uv run python3 generate-channel-txt.py`, 5 * 60 * 1000);
        }

        // Gradle 构建
        await $`commitHash='git rev-parse --short HEAD'`;
        await $`chmod +x gradlew`;

        const endGradleBuild = timeStart('Gradle 构建');
        await execWithTimeout(
          $`./gradlew clean assembleRelease -Pappid=${appId}`,
          30 * 60 * 1000, // 30分钟超时
        );
        endGradleBuild();

        cd('..');

        // 渠道包特殊处理
        if (isAndroidChannelRelease) {
          await getOSS();
          cd('android');

          const endJiaguChannel = timeStart('加固渠道包');
          await execWithTimeout($`uv run python3 jiagu-channel.py ${appId}`, 20 * 60 * 1000);
          endJiaguChannel();

          const endRebuildChannel = timeStart('重建渠道包');
          await execWithTimeout($`./gradlew reBuildChannel`, 15 * 60 * 1000);
          endRebuildChannel();

          cd('..');
        }

        // 上传 APK
        await getOSS();
        cd('./scripts');

        const releaseType = isAndroidChannelRelease ? 'release' : 'daily';
        const endUpload = timeStart(`上传 ${releaseType} APK`);
        await execWithTimeout(
          $`tsx ./upload-android-apk.mts ${appId} ${releaseType} false`,
          10 * 60 * 1000,
        );
        endUpload();

        console.log('🐳 - build-android.mts - bundleAndroid - 构建和上传完成');
      }

      exit(0);
    } catch (error) {
      console.error(
        `🐳 - build-android.mts - bundleAndroid - 构建失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      exit(1);
    }
  };

  /**
   * 主运行逻辑
   */
  const run = async () => {
    try {
      console.log(`🐳 build-android.mts - run - 🔥🔥🔥🔥🔥🔥 ${taskName} - 开始打包`);

      // 验证配置
      if (!(await validateConfig())) {
        console.error('🐳 build-android.mts - run - 配置验证失败，中止打包');
        exit(1);
      }

      // 类型断言，确保不是 undefined
      const appIdValue = appId as string;
      const appNameValue = appName as string;

      // 检查 Node 环境
      await checkNodeEnv();

      // 准备环境变量
      await prepareEnvironmentVariables(disabledAutoUploadSentry);

      // 检查是否有原生代码变更
      let hasNativeChange = await checkGitDiffHasNativeChange();
      console.log(
        '🐳 build-android.mts - run - hasNativeChange:',
        hasNativeChange,
        forceBuildNative,
        forceSkipBuildNative,
      );

      // 如果是渠道包构建，则需要创建新的 expo 配置然后再进行构建
      const shouldBuildNative =
        ((hasNativeChange || forceBuildNative) && !forceSkipBuildNative) || isAndroidChannelRelease;

      if (shouldBuildNative) {
        console.log('🐳 build-android.mts - run - 执行原生代码构建流程');

        await prepareEnvironmentVariablesForNativeBuild();

        // 获取更新的运行时版本
        const latestRuntimeVersion = await getRuntimeVersion(true);
        if (!latestRuntimeVersion) {
          console.error('🐳 build-android.mts - run - 无法获取运行时版本，中止构建');
          exit(1);
        }

        // 导出 JS bundle
        const exportResult = await exportJSBundle({ platform });
        if (!exportResult) {
          console.error('🐳 build-android.mts - run - 导出 JS bundle 失败，中止构建');
          exit(1);
        }

        // 获取或创建配置
        const config = await getOrCreateExpoConfig(latestRuntimeVersion);
        if (!config) {
          console.error('🐳 build-android.mts - run - 无法获取 Expo 配置，中止构建');
          exit(1);
        }

        // 上传 JS bundle 到 CDN
        const uploadResult = await uploadJSBundleToCDN({
          appId: appIdValue,
          channel,
          platform,
          latestRuntimeVersion,
        });

        if (!uploadResult) {
          console.error('🐳 - build-android.mts - run - 上传 JS bundle 到 CDN 失败，中止构建');
          exit(1);
        }

        // 写入配置文件
        const expoUpdatesConfigFilePath = path.join(
          __dirname,
          '..',
          `${platform}/app/src/main/assets/${hotFixJSONFileName}`,
        );
        await writeExpoUpdatesConfig({
          config,
          expoUpdatesConfigFilePath,
        });

        // 更新 Android 原生端配置
        await updateExpoUpdatesConfigInAndroidNativeEnd(latestRuntimeVersion, channel);

        // 执行构建
        cd('android');
        await bundleAndroid();
      } else {
        console.log('🐳 build-android.mts - run - 执行 js bundle 构建流程');

        // 获取运行时版本 (不强制更新)
        const latestRuntimeVersion = await getRuntimeVersion(false);
        if (!latestRuntimeVersion) {
          console.error('🐳 build-android.mts - run - 无法获取运行时版本，中止构建');
          exit(1);
        }

        // 导出 JS bundle
        const exportResult = await exportJSBundle({ platform });
        if (!exportResult) {
          console.error('🐳 build-android.mts - run - 导出 JS bundle 失败，中止构建');
          exit(1);
        }

        // 获取或创建配置 (隐式创建，不需要单独处理返回值)
        await getOrCreateExpoConfig(latestRuntimeVersion);

        // 上传 JS bundle 到 CDN
        const uploadResult = await uploadJSBundleToCDN({
          appId: appIdValue,
          channel,
          platform,
          latestRuntimeVersion,
        });

        if (uploadResult) {
          await sendJSBundleUpdatedMsg({
            appId: appIdValue,
            channel,
            appName: appNameValue,
            latestRuntimeVersion,
            platform,
          });
          exit(0);
        } else {
          console.error('🐳 - build-android.mts - run - 上传 JS bundle 到 CDN 失败，中止构建');
          exit(1);
        }
      }
    } catch (error) {
      console.error(
        `🐳 build-android.mts - run - 构建过程中发生错误: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      exit(1);
    }
  };

  // 启动构建流程
  await run();
})();
