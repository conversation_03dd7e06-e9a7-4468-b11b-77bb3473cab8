#! /usr/bin/env zx
/* eslint-disable @typescript-eslint/ban-ts-comment */
import ExpoConfig from '@expo/config';
import archiver from 'archiver';
import axios from 'axios';
import FormData from 'form-data';
import { fileURLToPath } from 'url';
import {
  createReadStream,
  createWriteStream,
  existsSync,
  readFileSync,
  statSync,
  writeFileSync,
} from 'fs';
import { unlink, writeFile } from 'fs/promises';
import path, { dirname } from 'path';
import { exit } from 'process';
import { $, echo, cd } from 'zx';
import { formatCurrentDateTime } from './build-common.mts';
import { platform } from 'os';
import mime from 'mime';
import { uploadJSBundleToAliYunOSS } from './upload-js-bundle.mts';
import { UpdatePlatform, UpdateChannel } from './common.mts';
import {
  convertSHA256HashToUUID,
  createHash,
  getBase64URLEncoding,
} from './expo-update-utils.mts';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const hotFixServerBaseUrl = 'https://apphotfix.zhizhuma.com';
export const hotFixManifestUrl = `${hotFixServerBaseUrl}/api/manifest`;

export enum ExpoUpdatesCheckOnLaunchType {
  Always = '1',
  WifiOnly = '2',
  ErrorRecoveryOnly = '3',
  Never = '4',
}

export type ExpoUpdatesConfig = {
  enabled: boolean;
  updateUrl: string;
  runtimeVersion: string;
  releaseChannel: string;
  checkOnLaunch: 'ALWAYS' | 'NEVER' | 'WIFI_ONLY' | 'ERROR_RECOVERY_ONLY';
  launchWaitM: number;
  extra: {
    appId: number | string;
    platform: 'ios' | 'android';
  };
};

type AssetDTO = {
  hash: string;
  key: string;
  url: string;
  fileExtension: string;
  contentType: string;
};

type ManifestDTO = {
  id: string;
  createdAt: string;
  runtimeVersion: string;
  assets: AssetDTO[];
  launchAssets: AssetDTO;
  expoConfig: string;
};

export const hotFixJSONFileName = 'expoUpdateConfig.json';

/**
 * 更新 runtimeVersion
 */
export const updateRuntimeVersion = async (param: {
  appId: string;
  appVersion: string;
  platform: UpdatePlatform;
}): Promise<string | undefined> => {
  const { appId, appVersion, platform } = param;
  const apiUrl = `${hotFixServerBaseUrl}/api/appInfo/add`;
  console.log('🚀 ~ updateRuntimeVersion apiUrl:', apiUrl);
  try {
    const runtimeVersion = `${appVersion}.${formatCurrentDateTime()}`;
    const response = await axios.post(apiUrl, {
      appId,
      platform,
      runtimeVersion,
    });
    if (response.data.success) {
      echo(`updateRuntimeVersion ${runtimeVersion} 成功`);
      return runtimeVersion;
    } else {
      echo('updateRuntimeVersion 失败', runtimeVersion, response.data.msg);
    }
  } catch (error) {
    echo('updateRuntimeVersion 失败', error.toString());
    exit(1);
  }
};

/**
 * 新增热更新配置
 */
export const addNewExpoUpdatesConfig = async (param: {
  appId: string;
  channel: UpdateChannel;
  latestRuntimeVersion: string;
  checkOnLaunch: ExpoUpdatesCheckOnLaunchType;
  platform: UpdatePlatform;
}) => {
  const { appId, channel, latestRuntimeVersion, checkOnLaunch, platform } =
    param;
  const apiUrl = `${hotFixServerBaseUrl}/api/updates/add`;
  try {
    const response = await axios.post(apiUrl, {
      appId,
      platform,
      channel,
      runtimeVersion: latestRuntimeVersion,
      updateEnabled: 'true',
      updateUrl: hotFixManifestUrl,
      checkOnLaunch,
      waitSecondsOnLaunch: '0',
    });
    if (response.data.success) {
      echo('addNewExpoUpdatesConfig 成功');
    } else {
      echo('addNewExpoUpdatesConfig 失败', response.data.msg);
    }
  } catch (error) {
    echo('addNewExpoUpdatesConfig 失败', error.toString());
    exit(1);
  }
};

/**
 * 获取 Expo Updates 热更新配置
 */
export const fetchExpoUpdatesConfig = async (param: {
  appId: string;
  channel: UpdateChannel;
  latestRuntimeVersion: string;
  platform: UpdatePlatform;
}): Promise<ExpoUpdatesConfig | undefined> => {
  const { appId, channel, latestRuntimeVersion, platform } = param;
  const apiUrl = `${hotFixServerBaseUrl}/api/expoConfig`;
  try {
    const response = await axios.post(apiUrl, {
      appId,
      platform,
      channel,
      runtimeVersion: latestRuntimeVersion,
    });
    if (response.data.success && response.data.data) {
      echo('fetchExpoUpdatesConfig 成功', JSON.stringify(response.data.data));
      return response.data.data;
    } else {
      echo('fetchExpoUpdatesConfig 失败', response.data.msg);
    }
  } catch (error) {
    echo('fetchExpoUpdatesConfig 失败', error.toString());
    exit(1);
  }
};

/**
 * 写入热更新配置到 expoUpdatesConfig.json 文件中
 */
export const writeExpoUpdatesConfig = async (param: {
  config: ExpoUpdatesConfig;
  expoUpdatesConfigFilePath: string;
}) => {
  const { config, expoUpdatesConfigFilePath } = param;
  echo('writeExpoUpdatesConfig 开始执行');
  let modifiedConfig: ExpoUpdatesConfig = {
    ...config,
    extra: {
      ...config.extra,
      appId: config.extra.appId.toString(),
    },
  };
  try {
    const jsonString = JSON.stringify(modifiedConfig, null, 2);
    echo('writeExpoUpdatesConfig -  jsonString', jsonString);
    echo(
      'writeExpoUpdatesConfig -  expoUpdatesConfigFilePath',
      expoUpdatesConfigFilePath,
    );
    if (existsSync(expoUpdatesConfigFilePath)) {
      // 如果存在，则先删除
      await unlink(expoUpdatesConfigFilePath);
    }
    await writeFile(expoUpdatesConfigFilePath, jsonString, 'utf-8');
  } catch (error) {
    echo('writeExpoUpdatesConfig 执行失败', error.toString());
    exit(1);
  }
};

/**
 * 获取最新的 runtimeVersion
 */
export const getLatestRuntimeVersion = async (param: {
  appId: string;
  platform: UpdatePlatform;
}): Promise<string | undefined> => {
  const { appId, platform } = param;
  const apiUrl = `${hotFixServerBaseUrl}/api/appInfo/getLatest`;
  console.log('🚀 getLatestRuntimeVersion ~ apiUrl:', apiUrl, param);
  try {
    const response = await axios.post(apiUrl, {
      appId,
      platform,
    });
    if (response.data.success && response.data.data) {
      echo('getLatestRuntimeVersion 成功', response.data.data.runtimeVersion);
      return response.data.data.runtimeVersion;
    } else {
      echo('getLatestRuntimeVersion 失败', response.data.msg);
    }
  } catch (error) {
    echo('getLatestRuntimeVersion 失败', error.toString());
    exit(1);
  }
};

/**
 * 输出 JS Bundle
 */
export const exportJSBundle = async (param: { platform: UpdatePlatform }) => {
  const { platform } = param;
  try {
    await $`npx expo export --platform ${platform} --output-dir ${platform}/jsbundle`;
    echo('exportJSBundle 成功');
    return true;
  } catch (error) {
    echo('exportJSBundle 失败', error.toString());
    return false;
  }
};

/**
 * 上传 JSBundle
 */
export const uploadJSBundle = async (param: {
  appId: string;
  channel: UpdateChannel;
  platform: UpdatePlatform;
  latestRuntimeVersion: string;
}) => {
  const { appId, channel, platform, latestRuntimeVersion } = param;
  // 1.确保 jsbundle 目录存在
  const jsBundleDirPath = path.join(__dirname, '..', `${platform}/jsbundle`);
  if (!existsSync(jsBundleDirPath)) {
    echo(`uploadJSBundle 执行失败 - ${jsBundleDirPath} 路径不存在`);
    exit(1);
  }
  const outputJSBundleZipPath = path.join(
    __dirname,
    '..',
    `${platform}/jsbundle.zip`,
  );
  if (existsSync(outputJSBundleZipPath)) {
    // 如果压缩包存在，则先删除一下
    await unlink(outputJSBundleZipPath);
  }

  // 读取 app.json 的内容然后写入到 jsbundle/expoConfig.json 文件中
  const appJsonPath = path.join(__dirname, '..', 'app.json');
  if (!existsSync(appJsonPath)) {
    echo(`uploadJSBundle 执行失败 - ${appJsonPath} 路径不存在`);
    exit(1);
  }
  const projectDir = path.join(__dirname, '..');
  const { exp } = ExpoConfig.getConfig(projectDir, {
    skipSDKVersionRequirement: true,
    isPublicConfig: true,
  });

  const expoConfigFilePath = path.join(jsBundleDirPath, 'expoConfig.json');
  if (existsSync(expoConfigFilePath)) {
    console.log(`expoConfigFilePath - ${expoConfigFilePath} 存在`);
    // 如果存在，则先删除一下
    await unlink(expoConfigFilePath);
  } else {
    console.log(`expoConfigFilePath - ${expoConfigFilePath} 不存在`);
  }
  writeFileSync(expoConfigFilePath, JSON.stringify(exp), 'utf-8');
  console.log(`expoConfigFilePath - ${expoConfigFilePath} 写入成功`);

  await archiveJSBundleToZip({
    zipPath: outputJSBundleZipPath,
    dirPath: jsBundleDirPath,
  });

  // 3.上传 zip 包
  // 创建 FormData 对象
  const formData = new FormData();

  // 添加 zip 文件到 FormData
  formData.append('file', createReadStream(outputJSBundleZipPath));

  // 添加其他参数到 FormData
  formData.append('appId', appId);
  formData.append('channel', channel);
  formData.append('platform', platform);
  formData.append('runtimeVersion', latestRuntimeVersion);

  const apiUrl = `${hotFixServerBaseUrl}/api/uploadBundle`;
  echo('上传 JSBundle apiUrl - ', apiUrl);
  try {
    const response = await axios.post(apiUrl, formData, {
      headers: {
        ...formData.getHeaders(),
      },
    });
    console.log('🚀 ~ uploadJSBundle ~ response:', response);
    if (response.data.success) {
      echo('上传 JSBundle 成功');

      // 4.如果上传成功则删除 zip 包
      unlink(outputJSBundleZipPath);
    } else {
      echo('上传 JSBundle 失败 - 323', response.data.msg);
    }
  } catch (error) {
    echo('上传 JSBundle 失败 - 326', error);
    exit(1);
  }
};

export const uploadJSBundleToCDN = async (param: {
  appId: string;
  channel: UpdateChannel;
  platform: UpdatePlatform;
  latestRuntimeVersion: string;
}) => {
  const { appId, channel, platform, latestRuntimeVersion } = param;
  // 1.确保 jsbundle 目录存在
  const jsBundleDirPath = path.join(__dirname, '..', `${platform}/jsbundle`);
  if (!existsSync(jsBundleDirPath)) {
    echo(`uploadJSBundleToCDN 执行失败 - ${jsBundleDirPath} 路径不存在`);
    exit(1);
  }

  // 读取 app.json 的内容然后写入到 jsbundle/expoConfig.json 文件中
  const appJsonPath = path.join(__dirname, '..', 'app.json');
  if (!existsSync(appJsonPath)) {
    echo(`uploadJSBundle 执行失败 - ${appJsonPath} 路径不存在`);
    exit(1);
  }
  const projectDir = path.join(__dirname, '..');
  const { exp } = ExpoConfig.getConfig(projectDir, {
    skipSDKVersionRequirement: true,
    isPublicConfig: true,
  });
  const expoConfig = JSON.stringify(exp);
  const expoConfigFilePath = path.join(jsBundleDirPath, 'expoConfig.json');
  if (existsSync(expoConfigFilePath)) {
    console.log(`expoConfigFilePath - ${expoConfigFilePath} 存在`);
    // 如果存在，则先删除一下
    await unlink(expoConfigFilePath);
  } else {
    console.log(`expoConfigFilePath - ${expoConfigFilePath} 不存在`);
  }
  writeFileSync(expoConfigFilePath, expoConfig, 'utf-8');
  console.log(`expoConfigFilePath - ${expoConfigFilePath} 写入成功`);

  // 2.开始上传
  const uploadedUrls: string[] | undefined = await uploadJSBundleToAliYunOSS({
    ...param,
    jsBundlePath: jsBundleDirPath,
  });
  console.log('🚀 ~ uploadedUrls:', uploadedUrls);

  if (!uploadedUrls) {
    echo('上传 JSBundle 到 CDN 失败');
    return;
  }

  // 3.构造 manifest request
  // 3.1 读取 medata.json 文件内容并得到其 hash
  const metadataPath = path.join(jsBundleDirPath, 'metadata.json');
  const updateMetadataBuffer = readFileSync(metadataPath, null);
  const metadataJson = JSON.parse(updateMetadataBuffer.toString('utf-8'));
  const metadataStat = statSync(metadataPath);
  const id = createHash({
    file: updateMetadataBuffer,
    hashingAlgorithm: 'sha256',
    encoding: 'hex',
  });
  const manifestId = convertSHA256HashToUUID(id);
  const createdAt = new Date(metadataStat.birthtime).toISOString();
  const platformSpecificMetadata = metadataJson.fileMetadata[platform];
  const assets: AssetDTO[] = platformSpecificMetadata.assets.map((asset) => {
    // 从 uploadedUrls url 列表中去匹配 asset.path 对应的 url
    const url = uploadedUrls.find((u) =>
      u.includes(asset.path.replace('assets/', '')),
    );
    const filePath = path.join(jsBundleDirPath, asset.path);
    return getAssetMetadataSync({
      filePath,
      ext: asset.ext,
      isLaunchAsset: false,
      url: url || '',
    });
  });
  const launchAssetUrl = uploadedUrls.find((u) =>
    u.includes(platformSpecificMetadata.bundle.replace('bundles/', '')),
  );
  const launchAsset = getAssetMetadataSync({
    filePath: path.join(jsBundleDirPath, platformSpecificMetadata.bundle),
    isLaunchAsset: true,
    url: launchAssetUrl || '',
  });
  if (!launchAsset) {
    echo('上传 JSBundle 到 CDN 失败 - 构造 launchAsset 失败');
    return;
  }
  const manifest: ManifestDTO = {
    id: manifestId,
    createdAt,
    runtimeVersion: latestRuntimeVersion,
    assets,
    launchAssets: launchAsset,
    expoConfig,
  };
  console.log('🚀 ~ manifest:', manifest);

  const apiUrl = `${hotFixServerBaseUrl}/api/uploadManifest`;
  try {
    const response = await axios.post(apiUrl, {
      appId,
      platform,
      channel,
      runtimeVersion: latestRuntimeVersion,
      manifestId,
      manifestCreatedAt: createdAt,
      manifestAssets: JSON.stringify(assets),
      manifestLaunchAssets: JSON.stringify(launchAsset),
      expoConfig: expoConfig,
    });
    if (response.data.success) {
      echo('uploadJSBundleToCDN 成功');
      return true;
    } else {
      echo('uploadJSBundleToCDN 失败', response.data.msg);
      return false;
    }
  } catch (error) {
    echo('uploadJSBundleToCDN 失败', error.toString());
    exit(1);
  }
};

/**
 * 发送 js 热更新包更新消息
 * @param latestRuntimeVersion 最新的 runtimeVersion
 */
export const sendJSBundleUpdatedMsg = async (param: {
  appId: string;
  channel: UpdateChannel;
  appName: string;
  latestRuntimeVersion: string;
  platform: UpdatePlatform;
}) => {
  const { appId, channel, appName, latestRuntimeVersion, platform } = param;
  echo($`pwd`);
  cd('scripts');
  await $`tsx ./notify-js-bundle-msg.mts appId=${appId} platform=${platform} env=${channel} runtimeVersion=${latestRuntimeVersion} messageTitle="${appName}-${platform} 热更新"`;
};

/**
 * 压缩 js bundle 为一个 zip
 */
const archiveJSBundleToZip = async (param: {
  zipPath: string;
  dirPath: string;
}) => {
  const { zipPath, dirPath } = param;
  return new Promise<void>((resolve, reject) => {
    // 2.压缩 jsbundle 目录为 zip
    const zipWriteStream = createWriteStream(zipPath);

    // 创建 archiver 实例
    const archive = archiver('zip', {
      zlib: { level: 9 }, // 设置压缩级别，可选参数
    });

    // 将输出流与 archiver 关联
    archive.pipe(zipWriteStream);

    // 添加目录到压缩包
    archive.directory(dirPath, false);

    // 完成压缩并关闭输出流
    archive.finalize();

    // 处理完成事件
    zipWriteStream.on('close', () => {
      console.log('压缩完成。');
      resolve();
    });

    // 处理错误事件
    archive.on('error', (err) => {
      echo('压缩 js bundle 失败');
      reject();
      exit(1);
    });
  });
};

const getAssetMetadataSync = (param: {
  filePath: string;
  ext?: string;
  isLaunchAsset: boolean;
  url: string;
}): AssetDTO | undefined => {
  const { filePath, ext, isLaunchAsset, url } = param;
  const asset = readFileSync(filePath, null);
  const assetHash = getBase64URLEncoding(
    createHash({ file: asset, hashingAlgorithm: 'sha256', encoding: 'base64' }),
  );
  const keyHash = createHash({
    file: asset,
    hashingAlgorithm: 'md5',
    encoding: 'hex',
  });
  if (isLaunchAsset) {
    return {
      hash: assetHash,
      key: `${keyHash}`,
      url,
      fileExtension: '.bundle',
      contentType: 'application/javascript',
    };
  } else if (ext) {
    const contentType = mime.getType(ext);
    return {
      hash: assetHash,
      key: `${keyHash}`,
      contentType: contentType || '',
      url,
      fileExtension: `.${ext}`,
    };
  }
  return undefined;
};
