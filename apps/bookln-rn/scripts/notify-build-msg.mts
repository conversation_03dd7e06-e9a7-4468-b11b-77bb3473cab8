#! /usr/bin/env zx
import * as lark from '@larksuiteoapi/node-sdk';
import fs, { createReadStream, readFileSync } from 'fs';
import { readFile } from 'fs/promises';
import https from 'https';
import path from 'path';
import { exit } from 'process';
import { fileURLToPath } from 'url';
import { $, echo } from 'zx';
import { getCommitInfo, getCurrentAuthor } from './git-util.mts';

const [
  nodePath,
  scriptPath,
  appIdParam,
  platformParam,
  buildTypeParam,
  qrCodeImageUrlParam,
  messageTitleParam,
  downloadAddressParam,
  taskNameParam,
  buildGitBranchParam,
  buildGitHashParam,
  androidChannelDownloadAddressParam,
  ...restArgs
] = process.argv;

const checkArguments = () => {
  if (!appIdParam.includes('=')) {
    echo(`${__filename} 执行失败，请检查 appId 参数`);
    exit(1);
  }
  if (!platformParam.includes('=')) {
    echo(`${__filename} 执行失败，请检查 platform 参数`);
    exit(1);
  }
  if (!buildTypeParam.includes('=')) {
    echo(`${__filename} 执行失败，请检查 buildType 参数`);
    exit(1);
  }
  if (!messageTitleParam.includes('=')) {
    echo(`${__filename} 执行失败，请检查 messageTitle 参数`);
    exit(1);
  }
  if (!downloadAddressParam.includes('=')) {
    echo(`${__filename} 执行失败，请检查 downloadAddress 参数`);
    exit(1);
  }
  if (!taskNameParam.includes('=')) {
    echo(`${__filename} 执行失败，请检查 taskName 参数`);
    exit(1);
  }
  if (!buildGitHashParam.includes('=')) {
    echo(`${__filename} 执行失败，请检查 buildGitHash 参数`);
    exit(1);
  }
};

type RobotConfig = {
  [key: string]: {
    ios: {
      normalBuild: string;
      devBuild: string;
    };
    android: {
      normalBuild: string;
      devBuild: string;
    };
  };
};

interface AccessTokenResponseData {
  code: number;
  tenant_access_token?: string;
  msg?: string;
}

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const robotKeyConfigFileName = 'fei_shu_robot_key.json';
const larkAppId = '********************';
const larkAppSecret = 'dCUzoTfmcwj1YlW9FWR76gApbC5zG4xE';
const larkRobotAPIBaseUrl = 'https://open.feishu.cn/open-apis/bot/v2/hook/';

// 下载二维码到本地
const downloadQrCodeToLocal = async (param: {
  localPath: string;
  qrCodeImageUrl: string;
}): Promise<void> => {
  const { localPath, qrCodeImageUrl } = param;
  if (!qrCodeImageUrl) {
    echo(`qrCodeImageUrl - ${qrCodeImageUrl} 为空，无法继续执行任务`);
    exit(1);
  }

  return new Promise<void>((resolve, reject) => {
    fs.open(localPath, 'w', (err, fd) => {
      if (err) throw err;
      const file = fs.createWriteStream(localPath, { fd });
      https
        .get(qrCodeImageUrl, (res) => {
          res.pipe(file);
          res.on('end', async () => {
            await $`chmod +r ${localPath}`;
            resolve();
            file.close();
          });
        })
        .once('error', (error: Error) => {
          echo('====', JSON.stringify(error));
        });
    });
  });
};

/**
 * 解析机器人配置文件
 * @param robotKeyConfigFileNamePath 机器人配置文件路径
 * @returns 机器人 token
 */
const parseRobotKeyConfig = async (param: {
  robotKeyConfigFileNamePath: string;
  appId: string;
  platform: string;
  buildType: string;
}): Promise<string> => {
  const { robotKeyConfigFileNamePath, appId, platform, buildType } = param;
  const data = await readFile(robotKeyConfigFileNamePath, 'utf-8');
  return new Promise((resolve, reject) => {
    try {
      const jsonData: RobotConfig = JSON.parse(data);
      echo('机器人配置文件内容', data);

      if (jsonData[appId]) {
        const config = jsonData[appId];
        if (config[platform]) {
          const platformConfig = config[platform];
          if (platformConfig[buildType]) {
            const token = platformConfig[buildType];
            echo(`${appId}-${platform}-${buildType} 对应的 Token 为 ${token}`);
            resolve(token);
          } else {
            echo(
              `buildType-${buildType} 在机器人配置文件中未匹配到，请检查参数`,
            );
            reject(
              `buildType-${buildType} 在机器人配置文件中未匹配到，请检查参数`,
            );
          }
        } else {
          echo(`platform-${platform} 在机器人配置文件中未匹配到，请检查参数`);
          reject(`platform-${platform} 在机器人配置文件中未匹配到，请检查参数`);
        }
      } else {
        echo(`appId-${appId} 在机器人配置文件中未匹配到，请检查参数`);
        reject(`appId-${appId} 在机器人配置文件中未匹配到，请检查参数`);
      }
    } catch (error) {
      echo('解析机器人配置文件失败', error.toString());
      reject(error.toString());
    }
  });
};

/**
 * 获取 access_token
 * @param app_id 飞书应用 ID
 * @param app_secret 飞书应用 Secret
 * @returns
 */
const getTenantAccessToken = (
  app_id: string,
  app_secret: string,
): Promise<string> => {
  const uri = new URL(
    'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal',
  );

  const options: https.RequestOptions = {
    hostname: uri.hostname,
    port: 443,
    path: uri.pathname,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
    },
  };

  const body = JSON.stringify({
    app_id,
    app_secret,
  });

  return new Promise<string>((resolve) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.setEncoding('utf8');

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        const response = JSON.parse(data) as AccessTokenResponseData;
        if (response.code === 0 && response.tenant_access_token) {
          resolve(response.tenant_access_token);
        } else {
          throw new Error(response.msg);
        }
      });
    });

    req.write(body);
    req.end();
  });
};

/**
 * 上传 ipa/apk 下载二维码图片到飞书，得到 imageKey
 * @param accessToken 飞书应用上传图片 access_token
 * @param localPath 图片本地路径
 * @returns imageKey
 */
const uploadQrImageToLark = async (
  accessToken: string,
  localPath: string,
): Promise<string | undefined> => {
  const client = new lark.Client({
    appId: larkAppId,
    appSecret: larkAppSecret,
    // disableTokenCache为true时，SDK不会主动拉取并缓存token，这时需要在发起请求时，调用lark.withTenantToken("token")手动传递
    // disableTokenCache为false时，SDK会自动管理租户token的获取与刷新，无需使用lark.withTenantToken("token")手动传递token
    disableTokenCache: true,
  });

  // 这里在 nodejs 18+ 上会抛出 TypeError: source.on is not a function，所以需要改用 stream 替代 buffer
  // https://github.com/larksuite/node-sdk/issues/39#issuecomment-1578177054
  // const imageBuffer = readFileSync(localPath);
  const imageStream = createReadStream(localPath);
  const result = await client.im.image.create(
    {
      data: {
        image_type: 'message',
        image: imageStream,
      },
    },
    lark.withTenantToken(accessToken),
  );
  return result?.image_key;

  // TODO: cenfeng - 原生 nodejs 貌似上传后飞书会返回参数错误
  // console.log(
  //   '🚀 ~ file: notify-build-msg.mts:191 ~ imageBuffer:',
  //   localPath,
  //   imageBuffer.length,
  // );

  // const uri = new URL('https://open.feishu.cn/open-apis/im/v1/images');
  // const boundary = '----WebKitFormBoundary7MA4YWxkTrZu0gW';
  // let body: string = `${boundary}\r\n`;
  // body += 'Content-Disposition: form-data; name="image_type"\r\n\r\n';
  // body += 'message\r\n';
  // body += `${boundary}\r\n`;
  // body +=
  //   'Content-Disposition: form-data; name="image"; filename="image.jpg"\r\n';
  // body += 'Content-Type: image/jpeg\r\n\r\n';
  // const bodyEnd = `\r\n${boundary}\r\n`;
  // const headers = {
  //   Authorization: `Bearer ${accessToken}`,
  //   'Content-Type': `multipart/form-data; boundary=${boundary}`,
  //   'Content-Length': Buffer.from(body + bodyEnd).length + imageBuffer.length,
  // };
  // console.log('🚀 ~ file: notify-build-msg.mts:209 ~ headers:', headers);
  // const options: https.RequestOptions = {
  //   hostname: uri.hostname,
  //   port: 443,
  //   path: uri.pathname,
  //   method: 'POST',
  //   headers,
  // };
  // return new Promise((resolve, reject) => {
  //   const req = https.request(options, (res) => {
  //     let data = '';

  //     res.on('data', (chunk) => {
  //       data += chunk;
  //     });

  //     res.on('end', () => {
  //       console.log('上传完成', data);
  //       try {
  //         const response = JSON.parse(data) as ImageUploadResponseData;
  //         if (response.code === 0) {
  //           resolve(response.data.image_key);
  //         } else {
  //           throw new Error(response.msg);
  //         }
  //       } catch (err) {
  //         reject(err);
  //       }
  //     });
  //   });

  //   // 将请求体和图片数据写入请求
  //   console.log('\n');
  //   console.log(
  //     '🚀 ~ file: notify-build-msg.mts:254 ~ returnnewPromise ~ body:',
  //   );
  //   console.log(body);
  //   console.log('\n');
  //   console.log(imageBuffer);
  //   console.log('\n');
  //   console.log(bodyEnd);
  //   console.log('\n');
  //   req.write(body);
  //   req.write(imageBuffer);
  //   req.write(bodyEnd);
  //   req.end();
  // });
};

/**
 * 构建消息体
 * @param imageKey 图片 key
 * @returns 消息体
 */
const constructMsgBody = async (param: {
  imageKey?: string;
  messageTitle: string;
  taskName: string;
  buildGitBranch: string;
  downloadAddress: string;
  buildGitHash: string;
  androidChannelDownloadAddress?: string;
}): Promise<string> => {
  const {
    imageKey,
    messageTitle,
    downloadAddress,
    taskName,
    buildGitBranch,
    buildGitHash,
    androidChannelDownloadAddress,
  } = param;
  const commitInfo = await getCommitInfo();
  echo(`commitInfo - ${commitInfo}`);
  const msgBody = {
    msg_type: 'post',
    content: {
      post: {
        zh_cn: {
          title: messageTitle,
          content: [
            [
              {
                tag: 'text',
                text: `${downloadAddress}\n\n`,
              },
              {
                tag: 'text',
                text: `${taskName}\n\n`,
              },
              {
                tag: 'text',
                text: `${buildGitBranch}\n\n`,
              },
              {
                tag: 'text',
                text: `${buildGitHash}\n\n`,
              },
              {
                tag: 'text',
                text: `操作：${
                  process.env.BUILD_USER ?? (await getCurrentAuthor())
                }\n\n`,
              },
            ],
          ],
        },
      },
    },
  };
  if (imageKey) {
    msgBody.content.post.zh_cn.content[0].push({
      tag: 'img',
      // @ts-ignore
      image_key: imageKey,
    });
  }
  if (commitInfo) {
    msgBody.content.post.zh_cn.content[0].push({
      tag: 'text',
      text: `${imageKey ? '\n' : ''}${commitInfo}\n\n`,
    });
  }
  if (androidChannelDownloadAddress) {
    msgBody.content.post.zh_cn.content[0].push({
      tag: 'text',
      text: `\n${androidChannelDownloadAddress}\n\n`,
    });
  }
  return JSON.stringify(msgBody);
};

/**
 * 发送消息到飞书机器人
 * @param param 参数
 * @returns
 */
const sendMsgToRobot = async (param: {
  larkRobotRequestUri: string;
  msgBody: string;
}): Promise<boolean> => {
  const { larkRobotRequestUri, msgBody } = param;
  const uri = new URL(larkRobotRequestUri);
  const options: https.RequestOptions = {
    hostname: uri.hostname,
    port: 443,
    path: uri.pathname,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
    },
  };

  const body = msgBody;

  return new Promise<boolean>((resolve) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.setEncoding('utf8');

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        const response = JSON.parse(data);
        if (response.StatusCode === 0) {
          resolve(true);
        } else {
          echo(`发送打包消息到机器人失败，失败原因：${response.msg}`);
          // throw new Error(response.msg);
          resolve(false);
        }
      });
    });

    req.write(body);
    req.end();
  });
};

const run = async (): Promise<boolean> => {
  checkArguments();
  const appId = appIdParam?.slice('appId='.length);
  const platform = platformParam?.slice('platform='.length);
  const buildType = buildTypeParam?.slice('buildType='.length);
  const qrCodeImageUrl: string | undefined = qrCodeImageUrlParam?.slice(
    'qrCodeImageUrl='.length,
  );
  console.log(
    '🚀 ~ file: notify-build-msg.mts:445 ~ run ~ qrCodeImageUrl:',
    qrCodeImageUrl,
  );
  const messageTitle = messageTitleParam.slice('messageTitle='.length);
  const downloadAddress = downloadAddressParam?.slice(
    'downloadAddress='.length,
  );
  const taskName = taskNameParam?.slice('taskName='.length);
  const buildGitBranch = buildGitBranchParam?.slice('buildGitBranch='.length);
  const buildGitHash = buildGitHashParam?.slice('buildGitHash='.length);
  const androidChannelDownloadAddress =
    androidChannelDownloadAddressParam?.slice(
      'androidChannelDownloadAddress='.length,
    );

  const robotKeyConfigFileNamePath = path.resolve(
    __dirname,
    robotKeyConfigFileName,
  );
  if (!fs.existsSync(robotKeyConfigFileNamePath)) {
    echo(`${robotKeyConfigFileNamePath} 不存在`);
    return false;
  } else {
    // 1.获取机器人 token
    const token = await parseRobotKeyConfig({
      robotKeyConfigFileNamePath,
      appId,
      buildType,
      platform,
    });

    let msgBody = '';

    if (qrCodeImageUrl && qrCodeImageUrl.length > 0) {
      const localQrCodePath = `./${platform}_qr_code_image_url.jpg`;
      // 2.下载二维码到本地
      await downloadQrCodeToLocal({
        localPath: localQrCodePath,
        qrCodeImageUrl,
      });

      // 3.获取图片上传所需 access_token
      const qrCodeImageUploadAccessToken = await getTenantAccessToken(
        larkAppId,
        larkAppSecret,
      );

      // 4.上传二维码图片到飞书
      const imageKey = await uploadQrImageToLark(
        qrCodeImageUploadAccessToken,
        localQrCodePath,
      );
      if (!imageKey) {
        echo('二维码图片上传失败');
        return false;
      } else {
        // 5.构造发送消息体
        msgBody = await constructMsgBody({
          imageKey,
          messageTitle,
          taskName,
          buildGitBranch,
          downloadAddress,
          buildGitHash,
        });
        // TODO: cenfeng - 输出一下消息体，定位一下消息发送失败的原因
        echo(`msgBody - ${msgBody}`);
      }
    } else {
      msgBody = await constructMsgBody({
        messageTitle,
        taskName,
        downloadAddress,
        buildGitHash,
        buildGitBranch,
        androidChannelDownloadAddress,
      });
    }

    if (msgBody.length > 0) {
      // 6.调用飞书机器人 API 进行发送
      const larkRobotRequestUri = `${larkRobotAPIBaseUrl}${token}`;
      return await sendMsgToRobot({ larkRobotRequestUri, msgBody });
    } else {
      return false;
    }
  }
};

run();
