import { YTRequest } from '@yunti-private/net';
// @ts-ignore
import FormData from 'react-native/Libraries/Network/FormData';

export class UploadApi {
  /**
   * 上传
   */
  static upload(param: {
    uri: string;
    name: string;
    type: string;
  }): YTRequest<string> {
    const { uri, type, name } = param;
    // 创建一个FormData对象
    const formData = new FormData();

    // 添加图片到FormData对象中
    const fileObject = {
      uri: uri,
      type,
      name,
    };
    formData.append('file', fileObject);
    return {
      url: '/uploadFile/upload.do',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };
  }
}
