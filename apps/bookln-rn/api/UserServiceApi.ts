import type { YTRequest } from '@yunti-private/net';
import type { UsersApiLoginDTO, UsersApiLoginParams } from '@yunti-private/api-xingdeng-boot';

/**
 * 登录
 */
export const login = (data: UsersApiLoginParams): YTRequest<UsersApiLoginDTO> => ({
  url: '/userservice/login.do',
  data,
});

/**
 * 退出登录
 */
export const logout = (): YTRequest<{ result: 'true' | 'false' }> => ({
  url: '/userservice/logout.do',
});

/**
 * 用户注销
 */
export const userLogout = (): YTRequest<boolean> => ({
  url: '/userservice/userLogout.do',
});

// 更新用户信息
export const updateUserInfo = (data: {
  nick: string;
}): YTRequest<{ result: 'true' | 'false' }> => ({
  url: '/userservice/update.do',
  data,
});

// 绑定手机号
export const bindPhone = (data: {
  mobile: string;
  validCode: string;
}): YTRequest<{ result: 'true' | 'false' }> => ({
  url: '/userservice/bindmobileonlycode.do',
  data,
});

/**
 * 身份认证
 */
export const identityVerification = (data: {
  realName: string;
  idCard: string;
}): YTRequest<boolean> => ({
  url: '/userservice/identityVerification.do',
  data,
});
