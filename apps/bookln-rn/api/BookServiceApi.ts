import type { YTRequest } from '@yunti-private/net';
import type { BookDTO } from './dto';
import type { BookHomeDetailDTO } from '../dtos/BookHomeDetailDTO';

type QueryBookListParams =
  | { catId: number }
  | { name: string; pageNo: number; pageSize: number; orderType: string }
  | { isbn: string };

/**
 * 查询图书列表
 * @param data
 * @returns
 */
export const queryBookList = (data: QueryBookListParams): YTRequest<Array<BookDTO>> => ({
  url: '/bookservice/querylist.do',
  data,
});

/**
 * 查询图书详情
 * @param data
 * @returns
 */
export const bookhomedetail = (data: { bookId: number }): YTRequest<BookHomeDetailDTO> => ({
  url: '/bookservice/bookhomedetail.do',
  data,
});
