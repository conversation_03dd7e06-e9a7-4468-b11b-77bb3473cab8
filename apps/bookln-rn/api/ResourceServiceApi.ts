import type { YTRequest } from '@yunti-private/net';
import type { CRCodeResult } from '../dtos/CRCodeResultDTO';

/**
 * 根据id获取对应的url
 *
 * 请求地址：/resourceservice/mpresourceurl.do
 * 参数：id（资源ID 或者 二维码ID），idSign（对应ID的签名），idType（id类型,1=二维码id｛通过扫码的时候｝,2=资源ID｛通过点击资源列表的时候｝）, crId: section 的 id
 * 响应： url（不含host的mp页面链接）
 */
export const mpResourceUrl = (data: {
  id: number;
  idSign: string;
  idType: number;
  crId?: number;
}): YTRequest<{ url?: string }> => ({
  url: '/resourceservice/mpresourceurl.do',
  data,
});
