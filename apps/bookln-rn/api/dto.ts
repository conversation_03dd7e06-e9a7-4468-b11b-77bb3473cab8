export type RemoteUpdateConfDTO = {
  enabled: boolean;
  updateUrl: string;
  checkOnLaunch: 'ALWAYS' | 'NEVER' | 'WIFI_ONLY' | 'ERROR_RECOVERY_ONLY';
  launchWaitM: number;
  updateAlert: boolean;
};

export interface BookDTO {
  // 不需要
  id: number;
  // 书名
  name: string;
  // 条形码（目前用作"图书资源"文案修改）
  barCode: string;
  // 作者
  author: string;
  // 出版社
  publisher: string;
  // 描述
  description: string;
  type: number;
  status: number;
  isDelete: number;
  // idsign
  idSign: string;
  // 二维码
  crCode: string;
  // 浏览次数
  totalViews: number;
  flag: number;
  // 是否有点读服务
  clickRead: boolean;
  // 点读是否上线
  clickReadOnline: boolean;
  clickReadAuthVal: string;
  // 是否有资源
  resource: boolean;
  // 只有推荐模块能用
  buyed: boolean;
  // 创建人
  userId: number;
  // 创建人姓名
  userName: string;
  // 条形码
  isbn: string;
  // 版次
  version: string;
  thumbnails?: string;
  pubCompanyId: number;
  // ar文件路径
  arDataFilePath: string;
  // 0=noneed=无需认证;1=need=需要认证
  needAuth: number;
  // 如果需要认证;直接购买的价格
  bookAuthPrice: number;
  arXmlFilePath: string;
  arResMappingFilePath: string;
  authType: number;
  authVal: string;
  // 用户认证情况
  userAuthVal: number;
  // 0=offline=offline;1=online=online
  online: number;
  // 类目ID
  catId: number;
  // 用户自定义分组
  groupId: number;
  // 父账号ID
  puserid: number;
  // 二维码列表
  crIds: string;
  // 购买链接•
  buyUrl: string;
  // 用户自定义编码
  defineCode: string;
  url: string;
  // 0=unclickread=无点读;1=clickread=有点读未上线;2=clickread_online=点读上线
  crStatus: number;
  orders?: number;
}

export interface WordItem {
  id: string;
  content: string;
  name: string;
  phAm: string;
  phAmMp3: string;
  phEn: string;
  phEnMp3: string;
  meanSimpleAudio?: string;
}
