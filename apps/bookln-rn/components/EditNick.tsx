import { Jg<PERSON><PERSON><PERSON>on, JglText, JglYStack } from '@jgl/ui-v4';
import { TextInput } from 'react-native';
import { useEditNick } from '../hooks/useEditNick';

/**
 * 编辑昵称
 */
export const EditNick = () => {
  const { nick, setNick, onSubmitEditing, isLoading } = useEditNick();

  return (
    <JglYStack backgroundColor="#f2f5f7" pt={8}>
      <TextInput
        maxLength={15}
        className="py-[12px] pl-[16px] pr-[12px] mt-[10px] bg-white"
        autoCapitalize={'none'}
        autoCorrect={false}
        onSubmitEditing={onSubmitEditing}
        onChangeText={setNick}
        defaultValue={nick}
        returnKeyType={'done'}
        editable={!isLoading}
        clearButtonMode={'while-editing'}
        underlineColorAndroid={'transparent'}
        selectionColor={'#4E76FF'}
      />
      <JglButton
        onPress={onSubmitEditing}
        radius={'circle'}
        marginHorizontal={20}
        disabled={isLoading}
        loading={isLoading}
        marginTop={20}
        backgroundColor={'#4E76FF'}
      >
        <JglText color={'white'} fontSize={16}>
          保存
        </JglText>
      </JglButton>
    </JglYStack>
  );
};
