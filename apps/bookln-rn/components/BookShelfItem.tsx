import { JglImage, JglText, JglTouchable, JglYStack } from '@jgl/ui-v4';
import { router } from '@jgl/utils';
import { useCallback } from 'react';
import type { BookDTO } from '../api/dto';
import { getRealMpHost } from '../utils/mpUrlHostHelper';
import { routerMap } from '../utils/routerMap';

type Props = {
  book: BookDTO;
};

/**
 * 书架图书条目
 */
export const BookShelfItem = ({ book }: Props) => {
  const { name = '', thumbnails, id, idSign } = book;

  // 点击图书条目
  const handlePress = useCallback(() => {
    router.push(routerMap.WebView, {
      url: `${getRealMpHost()}/book.htm?id=${id}&sign=${idSign}`,
    });
  }, [id, idSign]);

  return (
    <JglTouchable onPress={handlePress}>
      <JglYStack jglClassName='w-full' space={8}>
        <JglImage
          source={thumbnails ?? ''}
          defaultImage={require('../assets/images/img_cover.png')}
          width={'100%'}
          borderRadius={8}
          borderColor='#E5E5E5'
          borderWidth={1}
          jglClassName='aspect-[92/130]'
        />
        <JglText fontSize={12} maxLines={2} color='#00000'>
          {name}
        </JglText>
      </JglYStack>
    </JglTouchable>
  );
};
