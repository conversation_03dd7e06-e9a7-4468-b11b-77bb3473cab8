import { withLogin } from '@bookln/bookln-biz';
import { useAgreementCheck } from '@jgl/biz-func';
import {
  JglGridView,
  JglText,
  JglTouchable,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { envVars } from '@jgl/utils';
import { useCallback, useMemo } from 'react';
import { Image } from 'tamagui';
import {
  navigateToWebMyCollection,
  navigateToWebMyPractice,
} from '../utils/WebViewHelper';

/**
 * 学习
 */
export const MineContentStudy = () => {
  const { withAgreementCheck } = useAgreementCheck();

  const onPressDownload = useCallback(() => {
    console.log('我的下载');
  }, []);

  const onPressWrongQuestion = useCallback(() => {
    console.log('错题本');
  }, []);

  /**
   * 学习功能列表
   */
  const studyFeatureList = useMemo(() => {
    return [
      // {
      //   onPress: onPressDownload,
      //   key: 'my_download',
      //   icon: require('../assets/images/ic_my_download.png'),
      //   title: '我的下载',
      // },
      {
        onPress:
          envVars.loginLogicVersion() === 'new'
            ? withAgreementCheck(navigateToWebMyCollection)
            : withLogin(navigateToWebMyCollection),
        key: 'my_collection',
        icon: require('../assets/images/ic_my_collection.png'),
        title: '我的收藏',
      },
      {
        onPress:
          envVars.loginLogicVersion() === 'new'
            ? withAgreementCheck(navigateToWebMyPractice)
            : withLogin(navigateToWebMyPractice),
        key: 'my_practice',
        icon: require('../assets/images/ic_my_practice.png'),
        title: '我的练习',
      },
      // {
      //   onPress: onPressWrongQuestion,
      //   key: 'my_wrong_question',
      //   icon: require('../assets/images/ic_my_wrong_question.png'),
      //   title: '错题本',
      // },
    ];
  }, [withAgreementCheck]);

  return (
    <JglYStack
      jglClassName='p-[12px] bg-white'
      marginHorizontal={16}
      borderRadius={8}
      space={12}
    >
      <JglText fontSize={16} fontWeight='bold'>
        学习
      </JglText>
      <JglXStack>
        <JglGridView
          minColumns={3}
          minItemWidth={114}
          horizontalSpace={56}
          gap={12}
        >
          {studyFeatureList.map((item) => (
            <JglTouchable key={item.title} w={'100%'} onPress={item.onPress}>
              <JglYStack space={4} jglClassName='flex-1 items-center'>
                <Image source={item.icon} width={40} height={40} />
                <JglText fontSize={12} color='#151B37'>
                  {item.title}
                </JglText>
              </JglYStack>
            </JglTouchable>
          ))}
        </JglGridView>
      </JglXStack>
    </JglYStack>
  );
};
