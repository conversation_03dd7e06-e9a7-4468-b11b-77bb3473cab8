import { JglText, JglTouchable, JglXStack, JglYStack } from '@jgl/ui-v4';
import { FlashList } from '@shopify/flash-list';
import { useCallback } from 'react';
import BizConfig from '../constants/BizConfig';
import type { LessonDTO } from '../dtos/LessonDTO';
import { useMineContentLessons } from '../hooks/useMineContentLessons';
import { LessonItem } from './LessonItem';

/**
 * 我的课程
 */
export const MineContentLessons = () => {
  const { lessons = [], onPressMore } = useMineContentLessons();

  const keyExtractor = useCallback((item: LessonDTO) => `${item.id}`, []);

  const renderItem = useCallback(({ item }: { item: LessonDTO }) => {
    return (
      <JglXStack w={142}>
        <LessonItem lesson={item} />
      </JglXStack>
    );
  }, []);

  if (lessons.length === 0) {
    return null;
  }

  return (
    <JglYStack jglClassName='py-[16px] w-full items-center' space={16}>
      <JglXStack
        jglClassName='w-full items-center px-[16px]'
        justifyContent='space-between'
        alignItems='center'
      >
        <JglText fontSize={18} fontWeight='700' color='#151B37'>
          我的课程
        </JglText>
        <JglTouchable
          onPress={onPressMore}
          display={
            lessons.length > BizConfig.MAX_DISPLAY_LESSONS ? 'flex' : 'none'
          }
        >
          <JglText fontSize={12} color='#9698A5'>
            查看更多
          </JglText>
        </JglTouchable>
      </JglXStack>
      <JglXStack h={80} w={'100%'}>
        <FlashList
          data={lessons.slice(0, BizConfig.MAX_DISPLAY_LESSONS)}
          renderItem={renderItem}
          centerContent={false}
          contentContainerStyle={{
            paddingHorizontal: 16,
          }}
          key={`${lessons.length}`}
          keyExtractor={keyExtractor}
          ItemSeparatorComponent={() => <JglXStack jglClassName='w-[12px]' />}
          showsHorizontalScrollIndicator={false}
          horizontal
          showsVerticalScrollIndicator={false}
          alwaysBounceVertical={false}
        />
      </JglXStack>
    </JglYStack>
  );
};
