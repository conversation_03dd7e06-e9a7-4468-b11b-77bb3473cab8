import type { FeedbackDTO } from '@jgl/biz-func';
import { ContentContainer } from '@jgl/components';
import {
  JglCenterModal,
  JglGameButton,
  JglStateView,
  JglText,
  JglTouchable,
  JglView,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { useCallback } from 'react';
import { FlatList, SafeAreaView } from 'react-native';
// import ViewShot, { captureRef } from 'react-native-view-shot';
import DropShadow from 'react-native-drop-shadow';
import { Image, ScrollView } from 'tamagui';
import { useCustomerServiceCenter } from '../hooks/useCustomerServiceCenter';
// eslint-disable-next-line import/no-named-as-default
import LinearGradient from 'react-native-linear-gradient';

/**
 * 客服中心页面内容
 */
export default function CustomerServiceCenter() {
  const {
    feedbackDataList,
    isLoading,
    error,
    retry,
    onPressItem,
    modalRef,
    service,
    copyWechat,
    // onPressSaveQrCode,
    onPressComplaint,
    onPressContact,
    // viewShotRef,
  } = useCustomerServiceCenter();

  const keyExtractor = (item: FeedbackDTO) => item.id.toString();

  /**
   * 渲染反馈列表
   */
  const renderItem = useCallback(
    (data: { item: FeedbackDTO }) => {
      const { item } = data;
      return (
        <JglTouchable onPress={() => onPressItem(item)}>
          <JglXStack alignItems="center" p={16}>
            <JglText flex={1} fontSize={16} color="#151B37">
              {item.title}
            </JglText>
            <Image source={require('../assets/images/img_service_arrow.png')} />
          </JglXStack>
        </JglTouchable>
      );
    },
    [onPressItem],
  );

  // 分割线
  const ItemSeparatorComponent = useCallback(() => {
    return <JglView height={1} backgroundColor="#FAFAFB" mx={16} />;
  }, []);

  const renderQrModal = useCallback(() => {
    return (
      <JglCenterModal showTop={false} ref={modalRef} containerStyle={{ minHeight: 0 }}>
        <JglYStack p={24} space={16}>
          <JglText w="100%" textAlign="center" fontSize={16} fontWeight="bold">
            添加客服微信
          </JglText>
          {service?.showType === 0 ? (
            <>
              <JglText
                color="#4E76FF"
                fontSize={16}
                fontWeight="bold"
                w="100%"
                mb={4}
                textAlign="center"
              >
                微信号：{service?.wechatId}
              </JglText>
              <JglGameButton
                onPress={copyWechat}
                secondaryBgColor="#2E58E9"
                backgroundColor="#4E76FF"
                size="large"
              >
                复制
              </JglGameButton>
            </>
          ) : null}
          {service?.showType === 1 ? (
            <JglYStack alignItems="center">
              <Image w={180} h={180} source={{ uri: service?.wechatQrCode }} />
            </JglYStack>
          ) : null}
          <JglText color="#151B37" fontSize={14} textAlign="center" w="100%">
            感谢您的真诚建议，请联系客服进行反馈
          </JglText>
        </JglYStack>
      </JglCenterModal>
    );
  }, [copyWechat, modalRef, service?.showType, service?.wechatId, service?.wechatQrCode]);

  /**
   * 渲染底部按钮
   */
  const renderBottom = useCallback(() => {
    return (
      <SafeAreaView>
        <JglXStack w="100%" justifyContent="space-between" space={16} p={16}>
          <JglYStack flex={1}>
            <JglGameButton
              radius={'circle'}
              color="white"
              borderWidth={1}
              backgroundColor="white"
              secondaryBgColor="#4E76FF"
              onPress={onPressComplaint}
            >
              <JglXStack space={12} alignItems="center" py={4}>
                <Image source={require('../assets/images/img_complaint.png')} w={30} h={30} />
                <JglYStack alignItems="center">
                  <JglText fontSize={14} color="#4E76FF" fontWeight="bold">
                    投诉专线
                  </JglText>
                  <JglText fontSize={12} color="#4E76FF">
                    08:00-22:00
                  </JglText>
                </JglYStack>
              </JglXStack>
            </JglGameButton>
          </JglYStack>
          <JglYStack flex={1}>
            <JglGameButton
              backgroundColor="#4E76FF"
              secondaryBgColor="#2E58E9"
              onPress={onPressContact}
            >
              <JglXStack space={12} alignItems="center" py={4}>
                <Image source={require('../assets/images/img_contact.png')} w={30} h={30} />
                <JglYStack alignItems="center">
                  <JglText fontSize={14} color="white" fontWeight="bold">
                    联系客服
                  </JglText>
                  <JglText fontSize={12} color="white">
                    08:00-22:00
                  </JglText>
                </JglYStack>
              </JglXStack>
            </JglGameButton>
          </JglYStack>
        </JglXStack>
      </SafeAreaView>
    );
  }, [onPressComplaint, onPressContact]);

  const renderHeader = useCallback(() => {
    return (
      <JglYStack p={16}>
        <Image source={require('../assets/images/img_guess.png')} />
      </JglYStack>
    );
  }, []);

  return (
    <>
      <JglStateView isLoading={isLoading} error={error} onRetry={retry}>
        <ContentContainer containerClassName="self-center flex-1 flex bg-white">
          <JglYStack flex={1}>
            <DropShadow
              className="mx-[12px]"
              style={{
                shadowColor: '#000',
                shadowOffset: {
                  width: 0,
                  height: 5,
                },
                shadowOpacity: 0.05,
                shadowRadius: 12,
              }}
            >
              <ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{
                  borderRadius: 12,
                  backgroundColor: 'white',
                  overflow: 'hidden',
                }}
              >
                <LinearGradient
                  colors={['#E6ECFF', '#FBFCFF', '#FFFFFF']}
                  className="absolute h-[120px] w-full"
                  style={{
                    borderTopLeftRadius: 12,
                    borderTopRightRadius: 12,
                  }}
                />
                <FlatList
                  data={feedbackDataList}
                  renderItem={renderItem}
                  scrollEnabled={false}
                  ListHeaderComponent={renderHeader}
                  showsVerticalScrollIndicator={false}
                  keyExtractor={keyExtractor}
                  ItemSeparatorComponent={ItemSeparatorComponent}
                />
              </ScrollView>
            </DropShadow>
          </JglYStack>
          {renderBottom()}
        </ContentContainer>
      </JglStateView>
      {renderQrModal()}
    </>
  );
}
