import { JglSafeArea, JglText, JglView, JglXStack, JglYStack } from '@jgl/ui-v4';
import { useAtom } from 'jotai';
import { ScrollView, Switch } from 'react-native';
import { Image } from 'tamagui';
import { personalizedSwitchAtom } from '../atom';

/**
 * 推荐管理
 * @returns
 */
export const RecommendManager = () => {
  const [personalizedSwitch, setPersonalizedSwitch] = useAtom(personalizedSwitchAtom);
  return (
    <JglSafeArea>
      <ScrollView className="h-full bg-white">
        <Image className="w-full" source={require('../assets/images/img_header.png')} />
        <JglYStack backgroundColor="white" btlr={24} btrr={24} px={16} py={20} mt={-50}>
          <JglText fontSize={12} color="#676B7D">
            为向您提供更便捷、更优质、个性化的商品及服务，提升您的体验，我们会基于您所在的年级向您推荐合适的产品和服务，如果您不想看我们推荐的产品，可以关闭个性化推荐服务。
          </JglText>
          <JglView bg="#F1F1F3" h={1} w="full" my={16} />
          <JglXStack jglClassName="flex-center" mb={8}>
            <JglText fontSize={16} color="#151B37" flex={1}>
              个性化推荐
            </JglText>
            <Switch value={personalizedSwitch} onValueChange={setPersonalizedSwitch} />
          </JglXStack>
          <JglText fontSize={12} color="#676B7D">
            开启后将展示个性化推荐，提升您的服务体验
          </JglText>
        </JglYStack>
      </ScrollView>
    </JglSafeArea>
  );
};
