import { showToast } from '@yunti-private/jgl-ui';
import {
  type BarCodeScanningResult,
  Camera,
  type CameraCapturedPicture,
  CameraType,
  type FlashMode,
} from 'expo-camera';
import type { PropsWithChildren } from 'react';
import React, {
  useCallback,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { Platform } from 'react-native';

export type CameraScannerRef = {
  /**
   * 暂停预览 only for android
   */
  pausePreview: () => void;
  /**
   * 恢复预览 only for android
   */
  resumePreview: () => void;

  capture: () => Promise<CameraCapturedPicture | undefined>;

  /**
   * 获取支持的预览比例 only for android
   */
  getSupportedRatiosAsync: () => Promise<string[]> | undefined;
};

type Props = {
  onScanResult: (result: BarCodeScanningResult) => void;
  scanned: boolean;
  flashMode: FlashMode;
} & PropsWithChildren;

/**
 * 扫码组件
 */
export const CameraScanner = React.forwardRef<CameraScannerRef, Props>(
  (props, ref) => {
    const { onScanResult, children, scanned, flashMode } = props;

    const [ratio, setRatio] = useState<string>('16:9');

    const cameraRef = useRef<Camera>(null);

    useImperativeHandle(ref, () => ({
      pausePreview: () => {
        cameraRef.current?.pausePreview();
      },
      resumePreview: () => {
        cameraRef.current?.resumePreview();
      },
      getSupportedRatiosAsync: () => {
        return cameraRef.current?.getSupportedRatiosAsync();
      },
      capture: async () => {
        const result = await cameraRef.current?.takePictureAsync();
        return result;
      },
    }));

    const onCameraReady = useCallback(async () => {
      if (Platform.OS === 'android') {
        // 默认比例
        const targetRatio = 16 / 9;
        const supportRatio = await cameraRef.current?.getSupportedRatiosAsync();
        if (!supportRatio) {
          showToast({ title: '不支持该高宽比' });
          return;
        }
        const result = supportRatio
          .map((item) => {
            const pair = item.split(':');
            const radioValue =
              Number.parseInt(pair[0]) / Number.parseInt(pair[1]);
            return {
              key: item,
              difference: Math.abs(radioValue - targetRatio),
            };
          })
          .sort((a, b) => {
            return a.difference - b.difference;
          });
        setRatio(result[0].key);
      }
    }, []);

    return (
      <Camera
        ref={cameraRef}
        className='w-full flex-1'
        type={CameraType.back}
        onCameraReady={onCameraReady}
        ratio={ratio}
        flashMode={flashMode}
        onBarCodeScanned={scanned ? undefined : onScanResult}
        barCodeScannerSettings={
          Platform.OS === 'android'
            ? {
                barCodeTypes: ['qr', 'ean13', 'ean8', 'code128'],
              }
            : undefined
        }
      >
        {children}
      </Camera>
    );
  },
);
