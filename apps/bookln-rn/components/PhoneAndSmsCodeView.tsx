import { systemApiSmsSend } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { showToast } from '@jgl/utils';
import { useCountDown } from 'ahooks';
import { type RefObject, useCallback, useRef, useState } from 'react';
import { type TextInput, View } from 'react-native';
import { Button, Input, ScrollView, Spinner, XStack, YStack } from 'tamagui';
import { checkPhoneNumber } from '../utils/checkUtil';
import { LoginAgreement } from './LoginAgreement';
import { JglGameButton, JglText, JglXStack } from '@jgl/ui-v4';

export type PhoneAndSmsCodeViewBizProps = {
  showAgreementView: boolean;
  confirmButtonTitle: string;
  isConfirmButtonLoading: boolean;
  onPressConfirm: (args: {
    phoneNumber: string | undefined;
    smsCode: string | undefined;
  }) => void;
  headerRight?: () => React.ReactNode;
  headerLeft?: () => React.ReactNode;
};

export type PhoneAndSmsCodeViewCommonProps = {
  smsCodeInputRef: RefObject<TextInput>;
  phoneNumber: string | undefined;
  onChangePhoneNumber: (text: string) => void;
  smsCode: string | undefined;
  smsCodeButtonTitle: string;
  isSmsCodeButtonLoading: boolean;
  isSmsCodeButtonDisabled: boolean;
  onPressGetSmsCode: () => void;
  onChangeSmsCode: (text: string) => void;
};

/**
 * 登录、绑定手机号共用UI。包含
 * - 输入手机号
 * - 验证码
 * - 确认按钮
 * - 同意协议选项
 */
export const PhoneAndSmsCodeView = (props: PhoneAndSmsCodeViewBizProps) => {
  const {
    confirmButtonTitle,
    isConfirmButtonLoading,
    showAgreementView,
    onPressConfirm,
  } = props;
  const {
    isSmsCodeButtonDisabled,
    isSmsCodeButtonLoading,
    onChangePhoneNumber,
    onChangeSmsCode,
    onPressGetSmsCode,
    phoneNumber,
    smsCode,
    smsCodeButtonTitle,
    smsCodeInputRef,
  } = usePhoneAndSmsCodeView();

  const handlePressConfirm = useCallback(() => {
    onPressConfirm({ phoneNumber, smsCode });
  }, [onPressConfirm, phoneNumber, smsCode]);

  return (
    <ScrollView className='flex-1 bg-white' keyboardShouldPersistTaps='handled'>
      <YStack space={'$3'} className='w-[80%] max-w-[300] self-center py-10'>
        <Input
          className='flex-1'
          autoFocus
          placeholder='请输入手机号'
          size={'$5'}
          keyboardType='phone-pad'
          value={phoneNumber}
          onChangeText={onChangePhoneNumber}
          autoComplete='off'
        />

        <XStack className='items-center' space={'$2'}>
          <Input
            ref={smsCodeInputRef}
            className='flex-1'
            placeholder='请输入验证码'
            size={'$5'}
            keyboardType='number-pad'
            value={smsCode}
            onChangeText={onChangeSmsCode}
            returnKeyType='go'
            autoComplete='off'
          />
          <View className='w-28 items-center justify-center'>
            {isSmsCodeButtonLoading ? (
              <Spinner color='#3E63DD' />
            ) : (
              <Button
                chromeless
                className='rounded-full'
                size={'$4'}
                color='#3E63DD'
                onPress={onPressGetSmsCode}
                disabled={isSmsCodeButtonDisabled}
              >
                {smsCodeButtonTitle}
              </Button>
            )}
          </View>
        </XStack>

        {showAgreementView ? <LoginAgreement textClassName='my-2' /> : null}

        <JglGameButton
          radius={10}
          backgroundColor='#4E76FF'
          secondaryBgColor='#2E58E9'
          fontSize={16}
          loading={isConfirmButtonLoading}
          fontWeight='bold'
          onPress={handlePressConfirm}
        >
          <JglXStack minH={44} py={6} alignItems='center'>
            <JglText fontSize={16} fontWeight='bold' color='white'>
              {confirmButtonTitle}
            </JglText>
          </JglXStack>
        </JglGameButton>
      </YStack>
    </ScrollView>
  );
};

export const usePhoneAndSmsCodeView = (): PhoneAndSmsCodeViewCommonProps => {
  const [phoneNumber, setPhoneNumber] = useState<string | undefined>(undefined);
  const [smsCode, setSmsCode] = useState<string | undefined>(undefined);
  const [isRequestingCode, setIsRequestingCode] = useState<boolean>(false);
  const didRequestCode = useRef<boolean>(false);
  const [targetDate, setTargetDate] = useState<number | undefined>(undefined);
  const [requestCodeCountdown] = useCountDown({ targetDate });

  // 奇奇怪怪的写法，本来应该是 tamagui 的 Input
  const codeInputRef = useRef<TextInput>(null);

  const getCode = useCallback(async () => {
    const checkedPhoneNumber = checkPhoneNumber(phoneNumber);
    if (checkedPhoneNumber) {
      setIsRequestingCode(true);

      const request = systemApiSmsSend({ mobile: checkedPhoneNumber });
      const response = await container.net().fetch(request);
      if (response.success) {
        didRequestCode.current = true;

        showToast({ title: '已发送验证码' });
        codeInputRef.current?.focus();

        setTargetDate(Date.now() + 60 * 1000);
      }

      setIsRequestingCode(false);
    }
  }, [phoneNumber]);

  const handleChangePhoneNumber = useCallback((text: string) => {
    setPhoneNumber(text);
  }, []);

  const handleChangeCode = useCallback((text: string) => {
    setSmsCode(text);
  }, []);

  const isRequestCodeButtonDisabled = requestCodeCountdown > 0;

  let requestCodeButtonTitle = '获取验证码';
  if (isRequestCodeButtonDisabled) {
    requestCodeButtonTitle = `${Math.floor(requestCodeCountdown / 1000)}s`;
  } else if (didRequestCode.current) {
    requestCodeButtonTitle = '重新获取';
  }

  return {
    isSmsCodeButtonDisabled: isRequestCodeButtonDisabled,
    isSmsCodeButtonLoading: isRequestingCode,
    onChangePhoneNumber: handleChangePhoneNumber,
    onChangeSmsCode: handleChangeCode,
    onPressGetSmsCode: getCode,
    phoneNumber,
    smsCode,
    smsCodeButtonTitle: requestCodeButtonTitle,
    smsCodeInputRef: codeInputRef,
  };
};
