import React, { useCallback } from 'react';
import { Pressable } from 'react-native';
import { Image } from 'expo-image';
import { useNavigation } from 'expo-router';
import { CommonActions } from '@react-navigation/native';

export const CustomBackButton = () => {
  const navigation = useNavigation();

  const handlePressBack = useCallback(() => {
    if (navigation.canGoBack()) {
      navigation.dispatch(CommonActions.goBack());
    }
  }, [navigation]);

  return (
    <Pressable onPress={handlePressBack}>
      <Image
        source={require('../assets/images/ic_back.png')}
        style={{ width: 24, height: 24 }}
      />
    </Pressable>
  );
};
