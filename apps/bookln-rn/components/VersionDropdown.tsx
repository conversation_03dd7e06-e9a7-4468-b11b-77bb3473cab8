import { LearnPkgClassifyDTOLearnPkgVersion } from '@yunti-private/api-xingdeng-boot';
import { XStack, YStack, View, Text } from 'tamagui';

type Props = {
  versions?: LearnPkgClassifyDTOLearnPkgVersion[];
  selectedVersion?: LearnPkgClassifyDTOLearnPkgVersion;
  onDropdownChange?: (version: LearnPkgClassifyDTOLearnPkgVersion) => void;
  show?: boolean;
};
export const VersionDropdown = (props: Props) => {
  const {
    versions = [],
    selectedVersion,
    onDropdownChange,
    show = false,
  } = props;
  if (show) {
    return (
      <YStack
        position='absolute'
        width='100%'
        height='100%'
        top={44}
        left={0}
        right={0}
        backgroundColor='rgba(0,0,0,0.5)'
        onPress={() => {
          selectedVersion && onDropdownChange?.(selectedVersion);
        }}
      >
        <XStack className='flex-wrap overflow-y-auto bg-[#F5F8FB] px-4 py-3'>
          {versions?.map((version) => (
            <View
              backgroundColor={
                selectedVersion?.code === version?.code ? '#1677FF' : '#fff'
              }
              br={100}
              px={16}
              py={5}
              mb={12}
              mr={12}
              key={version?.code}
              onPress={() => {
                onDropdownChange?.(version);
              }}
            >
              <Text
                color={
                  selectedVersion?.code === version?.code ? '#fff' : '#595959'
                }
                fontSize='$4'
              >
                {version?.name}
              </Text>
            </View>
          ))}
        </XStack>
      </YStack>
    );
  }
  return null;
};
