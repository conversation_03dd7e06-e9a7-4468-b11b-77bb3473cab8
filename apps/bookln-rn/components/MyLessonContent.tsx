import { JglGridView, Jg<PERSON><PERSON><PERSON><PERSON>, JglTouchable, JglYStack } from '@jgl/ui-v4';
import { useAtomValue } from 'jotai';
import { useCallback } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Image, ScrollView } from 'tamagui';
import { myLessonsAtom } from '../atom';
import type { LessonDTO } from '../dtos/LessonDTO';
import { navigateToWebLesson } from '../utils/WebViewHelper';

/**
 * 我的课程内容
 */
export const MyLessonContent = () => {
  const myLessons = useAtomValue(myLessonsAtom);
  const { bottom } = useSafeAreaInsets();

  const onPressLessonItem = useCallback((item: LessonDTO) => {
    const { id, idSign } = item;
    navigateToWebLesson(id, idSign);
  }, []);

  return (
    <ScrollView
      contentContainerStyle={{ padding: 16, paddingBottom: bottom + 16 }}
      style={{ backgroundColor: 'white' }}
    >
      <JglGridView
        minColumns={2}
        minItemWidth={164}
        horizontalSpace={32}
        gap={16}
      >
        {myLessons.map((item) => {
          const { name, thumbnails } = item;
          return (
            <JglTouchable
              key={item.id}
              onPress={() => {
                onPressLessonItem(item);
              }}
              w={'100%'}
            >
              <JglYStack space={4}>
                <Image
                  source={{ uri: thumbnails }}
                  w={'100%'}
                  borderRadius={8}
                  aspectRatio={164 / 92}
                />
                <JglText maxLines={2} fontSize={12} color={'#151B37'}>
                  {name}
                </JglText>
              </JglYStack>
            </JglTouchable>
          );
        })}
      </JglGridView>
    </ScrollView>
  );
};
