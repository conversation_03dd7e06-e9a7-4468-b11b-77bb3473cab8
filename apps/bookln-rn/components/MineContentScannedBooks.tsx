import {
  JglStateView,
  JglText,
  JglTouchable,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { FlashList } from '@shopify/flash-list';
import { useCallback } from 'react';
import type { BookDTO } from '../api/dto';
import { useMineContentScannedBooks } from '../hooks/useMineContentScannedBooks';
import { BookItem } from './BookItem';
import BizConfig from '../constants/BizConfig';

type MineContentScannedBooksProps = {
  /**
   * 是否显示空状态
   */
  isShowEmpty?: boolean;
};

/**
 * 扫码看过的书
 */
export const MineContentScannedBooks = (
  props: MineContentScannedBooksProps,
) => {
  const { isShowEmpty = true } = props;
  const { bookShelf, onPressMore, retry } = useMineContentScannedBooks();

  const keyExtractor = useCallback((item: BookDTO) => `${item.id}`, []);

  const renderItem = useCallback(({ item }: { item: BookDTO }) => {
    return (
      <JglXStack jglClassName='w-[92px] aspect-[92/130]'>
        <BookItem book={item} maxDisplayLines={1} />
      </JglXStack>
    );
  }, []);

  if (!isShowEmpty && bookShelf.length === 0) {
    return null;
  }

  return (
    <JglYStack jglClassName='w-full items-center' py={16} space={12}>
      <JglXStack
        jglClassName='w-full items-center px-[16px]'
        justifyContent='space-between'
        alignItems='center'
      >
        <JglText fontSize={18} fontWeight='700' color='#151B37'>
          我扫码看过的书
        </JglText>
        <JglTouchable
          onPress={onPressMore}
          minH={0}
          display={
            bookShelf.length > BizConfig.MAX_DISPLAY_SCANNED_BOOKS
              ? 'flex'
              : 'none'
          }
        >
          <JglText fontSize={12} color='#9698A5'>
            查看更多
          </JglText>
        </JglTouchable>
      </JglXStack>
      <JglStateView
        jglClassName='w-full min-h-[127px]'
        isEmpty={bookShelf.length === 0}
        onRetry={retry}
        backgroundColor='transparent'
        emptyProps={{
          message: '暂无图书',
        }}
      >
        <JglXStack jglClassName='w-full'>
          <FlashList
            data={bookShelf.slice(0, BizConfig.MAX_DISPLAY_SCANNED_BOOKS)}
            renderItem={renderItem}
            contentContainerStyle={{
              paddingHorizontal: 16,
            }}
            key={`${bookShelf.length}`}
            keyExtractor={keyExtractor}
            ItemSeparatorComponent={() => <JglXStack jglClassName='w-[12px]' />}
            showsHorizontalScrollIndicator={false}
            horizontal
            showsVerticalScrollIndicator={false}
            alwaysBounceVertical={false}
          />
        </JglXStack>
      </JglStateView>
    </JglYStack>
  );
};
