import {
  JglImage,
  JglText,
  JglTouchable,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { router } from '@jgl/utils';
import { useCallback } from 'react';
import type { BookDTO } from '../api/dto';
import { getRealMpHost } from '../utils/mpUrlHostHelper';
import { routerMap } from '../utils/routerMap';

type Props = {
  book: BookDTO;
  /**
   * 最大显示行数
   */
  maxDisplayLines?: number;
};

/**
 * 图书条目
 */
export const BookItem = ({ book, maxDisplayLines = 2 }: Props) => {
  const { name = '', thumbnails, id, idSign } = book;

  // 点击图书条目
  const handlePress = useCallback(() => {
    router.push(routerMap.WebView, {
      url: `${getRealMpHost()}/book.htm?id=${id}&sign=${idSign}`,
    });
  }, [id, idSign]);

  return (
    <JglTouchable onPress={handlePress}>
      <JglYStack
        jglClassName='w-full items-center'
        position='relative'
        borderRadius={8}
        borderWidth={1}
        borderColor='#0000000D'
        overflow='hidden'
      >
        <JglImage
          source={thumbnails ?? ''}
          defaultImage={require('../assets/images/img_cover.png')}
          width={'100%'}
          borderRadius={8}
          jglClassName='aspect-[92/130]'
        />
        <JglXStack
          position='absolute'
          bottom={0}
          left={0}
          right={0}
          px={8}
          borderBottomLeftRadius={8}
          borderBottomRightRadius={8}
          justifyContent='center'
          bg='#00000080'
        >
          <JglText fontSize={12} maxLines={1} fontWeight='bold' color='white'>
            {name}
          </JglText>
        </JglXStack>
      </JglYStack>
    </JglTouchable>
  );
};
