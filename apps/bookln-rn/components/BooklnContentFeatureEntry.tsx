import { JglText, JglTouchable, JglXStack, JglYStack } from '@jgl/ui-v4';
// eslint-disable-next-line import/no-named-as-default
import LinearGradient from 'react-native-linear-gradient';
import { Image, ScrollView } from 'tamagui';
import { useBooklnContentFeatureEntry } from '../hooks/useBooklnContentFeatureEntry';
import { LearnToolsModal } from './LearnToolsModal';

/**
 * 书链内容功能入口
 */
export const BooklnContentFeatureEntry = () => {
  const { featureEntryList, learnToolsModalVisible, onLearnToolsModalDismiss } =
    useBooklnContentFeatureEntry();
  return (
    <>
      <ScrollView
        contentContainerStyle={{ paddingHorizontal: 12, minWidth: '100%' }}
        className='flex-row py-4'
        bounces={false}
        horizontal
      >
        <JglXStack space={12} jglClassName='w-full'>
          {featureEntryList.map((item) => {
            return (
              <JglTouchable
                key={item.id}
                onPress={item.onPress}
                jglClassName='flex-1 min-w-[64px]'
              >
                <LinearGradient
                  className='w-full rounded-2xl py-3'
                  colors={item.backgroundColor}
                >
                  <JglYStack
                    space={4}
                    jglClassName='w-full flex-center'
                    borderRadius={16}
                  >
                    <Image
                      source={item.image}
                      className='aspect-square w-full'
                    />
                    <JglText fontSize={12} color='#FAFAFB'>
                      {item.title}
                    </JglText>
                  </JglYStack>
                </LinearGradient>
              </JglTouchable>
            );
          })}
        </JglXStack>
      </ScrollView>
      <LearnToolsModal
        visible={learnToolsModalVisible}
        onDismiss={onLearnToolsModalDismiss}
      />
    </>
  );
};
