import { JglYStack } from '@jgl/ui-v4';
import { useCallback } from 'react';
import { KeyboardAvoidingView, Platform } from 'react-native';
import { type TabBarProps, Tabs } from 'react-native-collapsible-tab-view';

import { withLoginLogicVersion } from '@bookln/bookln-biz';
import { agreementStateAtom } from '@jgl/biz-func';
import { StatusBar } from 'expo-status-bar';
import { useAtomValue } from 'jotai';
import type { TabName } from 'react-native-collapsible-tab-view/lib/typescript/src/types';
import { useHomeContent } from '../hooks/useHomeContent';
import { ChatSessionListSideMenu } from './ChatSessionListSideMenu';
import { HomeContentTopTab } from './HomeContentTopTab';

/**
 * 首页内容
 */
export const HomeContent = () => {
  const {
    topTabContainerHeight,
    tabList,
    onIndexChange,
    onPressScan,
    chatSessionListSideMenuRef,
    onPressShowChatSessionListSideMenu,
    navigationBarHeight,
    onPressOpenChatSessionItem,
    onPressCreateNewChatSession,
    onLayoutTopTabContainer,
    miniTopTabContainerHeight,
  } = useHomeContent();

  const agreementState = useAtomValue(agreementStateAtom);

  const renderTabBar = useCallback(
    (props: TabBarProps<TabName>) => {
      return (
        <HomeContentTopTab
          miniTopTabContainerHeight={miniTopTabContainerHeight}
          onLayoutTopTabContainer={onLayoutTopTabContainer}
          onPressScan={onPressScan}
          onPressCreateNewChatSession={onPressCreateNewChatSession}
          onPressShowChatSessionListSideMenu={
            onPressShowChatSessionListSideMenu
          }
          {...props}
        />
      );
    },
    [
      miniTopTabContainerHeight,
      onLayoutTopTabContainer,
      onPressCreateNewChatSession,
      onPressScan,
      onPressShowChatSessionListSideMenu,
    ],
  );

  return (
    <>
      <StatusBar style={'dark'} />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <JglYStack jglClassName='flex-1 relative h-full'>
          <Tabs.Container
            renderTabBar={renderTabBar}
            onIndexChange={onIndexChange}
            pagerProps={{
              scrollEnabled: withLoginLogicVersion(
                true,
                agreementState === 'agreed',
              ),
              children: undefined,
            }}
          >
            {tabList.map((tab) => (
              <Tabs.Tab name={tab.title} key={tab.id}>
                {tab.renderContent(topTabContainerHeight)}
              </Tabs.Tab>
            ))}
          </Tabs.Container>
          <ChatSessionListSideMenu
            navigationBarHeight={navigationBarHeight}
            onPressHistoryItem={onPressOpenChatSessionItem}
            onPressCreateNewChatSession={onPressCreateNewChatSession}
            ref={chatSessionListSideMenuRef}
          />
        </JglYStack>
      </KeyboardAvoidingView>
    </>
  );
};
