import { JglSafeArea, JglText, JglTouchable, JglView, JglXStack } from '@jgl/ui-v4';
import { useCallback } from 'react';
import {
  ActivityIndicator,
  Platform,
  SectionList,
  type SectionListData,
  Switch,
  View,
} from 'react-native';
import { Image } from 'tamagui';
import { type Item, ItemType, type SectionData, useSetting } from '../hooks/useSetting';

/**
 * 设置
 */
export const Setting = () => {
  const {
    dataArray,
    onPressItem,
    isLoggingOut,
    messageNotificationSwitch,
    setMessageNotificationSwitch,
  } = useSetting();

  const keyExtractor = (item: Item) => `${item.type}`;

  const renderLogout = useCallback(
    (item: Item) => {
      return (
        <JglTouchable
          onPress={() => onPressItem(item.type)}
          height={49}
          backgroundColor="white"
          jglClassName="flex-center"
          disabled={isLoggingOut}
        >
          <JglXStack space={10}>
            {isLoggingOut && <ActivityIndicator color="#333333" />}
            <JglText fontSize={15} color="#333333">
              {isLoggingOut ? '正在退出' : item.name}
            </JglText>
          </JglXStack>
        </JglTouchable>
      );
    },
    [isLoggingOut, onPressItem],
  );

  const renderMessageNotificationView = useCallback(
    (item: Item) => {
      if (Platform.OS === 'android') {
        return (
          <JglXStack px={15} py={20} backgroundColor="white" alignItems="center">
            <JglText fontSize={14} color="#666666">
              {item.name}
            </JglText>
            <JglText
              fontSize={14}
              color="#333333"
              textAlign="right"
              flex={1}
              style={item.valueStyle}
            >
              {item.value}
            </JglText>
            <Switch
              value={messageNotificationSwitch}
              onValueChange={setMessageNotificationSwitch}
            />
          </JglXStack>
        );
      } else {
        return null;
      }
    },
    [messageNotificationSwitch, setMessageNotificationSwitch],
  );

  const renderItem = useCallback(
    ({ item }: { item: Item }): JSX.Element | null => {
      const disabled = item.disablePress || false;
      switch (item.type) {
        case ItemType.logout:
          return renderLogout(item);
        case ItemType.messageNotification:
          return renderMessageNotificationView(item);
        default:
          return (
            <JglTouchable
              // eslint-disable-next-line react/jsx-no-bind
              onPress={() => onPressItem(item.type)}
              disabled={disabled}
              paddingVertical={15}
              paddingHorizontal={20}
              backgroundColor="white"
              alignItems="center"
              flexDirection="row"
            >
              <JglText fontSize={14} color="#666666">
                {item.name}
              </JglText>
              <JglText
                flex={1}
                textAlign="right"
                fontSize={14}
                color="#333333"
                style={item.valueStyle}
              >
                {item.value}
              </JglText>
              {disabled ? (
                <View />
              ) : (
                <Image
                  className="ml-[13]"
                  source={require('../assets/images/ic_right_arrow.png')}
                />
              )}
            </JglTouchable>
          );
      }
    },
    [onPressItem, renderLogout, renderMessageNotificationView],
  );

  const renderSectionHeader = useCallback(
    (info: {
      section: SectionListData<Item, SectionData>;
    }) => {
      const { data } = info.section;

      if (data.length === 0) {
        return null;
      } else {
        return <JglView height={10} />;
      }
    },
    [],
  );

  return (
    <JglXStack backgroundColor="#f6f7f8" height="100%">
      <JglSafeArea>
        <SectionList
          keyExtractor={keyExtractor}
          renderItem={renderItem}
          renderSectionHeader={renderSectionHeader}
          sections={dataArray}
        />
      </JglSafeArea>
    </JglXStack>
  );
};
