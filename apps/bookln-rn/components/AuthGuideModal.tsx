import { router } from '@jgl/utils';
import { useAtom } from 'jotai';
import { memo, useCallback } from 'react';
import Modal from 'react-native-modal';
import { Button, Text, View, XStack } from 'tamagui';
import { routerMap } from '../utils/routerMap';
import { atoms } from '@bookln/bookln-biz';

/**
 * AuthGuideModal 身份认证引导弹窗
 * 用于引导用户进行身份认证，展示相关规则说明
 *
 * @component
 * @example
 * return (
 *   <AuthGuideModal />
 * )
 */
export const AuthGuideModal = memo(() => {
  const [isShowAuthGuideModal, setIsShowAuthGuideModal] = useAtom(atoms.isShowAuthGuideModalAtom);

  const onClose = useCallback(() => {
    setIsShowAuthGuideModal(false);
  }, [setIsShowAuthGuideModal]);

  const onConfirm = useCallback(() => {
    setIsShowAuthGuideModal(false);
    router.push(`${routerMap.AuthIdentity}`);
  }, [setIsShowAuthGuideModal]);

  return (
    <Modal
      isVisible={isShowAuthGuideModal}
      hardwareAccelerated
      useNativeDriver
      animationIn={'fadeIn'}
      animationOut={'fadeOut'}
      onBackdropPress={onClose}
      // https://github.com/react-native-modal/react-native-modal/issues/268
      hideModalContentWhileAnimating
    >
      <View className="flex items-center rounded-2xl bg-white px-6 pb-6">
        <Text className="py-[13px] text-center text-[20px] font-medium text-[#1F1F1F]">
          欢迎使用
        </Text>

        <View className="mb-[22px] mt-[6px]">
          <Text className="text-[14px] text-[#595959]">
            1. 根据国家法律法规要求，在使用生成式人工智能服务前请先进行真实身份信息认证
          </Text>
          <Text className="mt-[10px] text-[14px] text-[#595959]">
            2. 请勿使用本功能从事违法活动
          </Text>
          <Text className="mt-[10px] text-[14px] text-[#595959]">
            3. 本功能使用大模型：通义千问大模型，备案编号：Beijing-YunQue-20230821
          </Text>
        </View>

        <XStack space="$3">
          <Button
            className="flex-1 rounded-full border-solid border-[#4E76FF] bg-white text-[#4E76FF]"
            onPress={onClose}
          >
            取消
          </Button>
          <Button className="flex-1 rounded-full bg-[#4E76FF] text-white" onPress={onConfirm}>
            认证身份
          </Button>
        </XStack>
      </View>
    </Modal>
  );
});
