import { useAppSelector } from '@jgl/biz-func';
import { JglText, JglTouchable, JglXStack, JglYStack } from '@jgl/ui-v4';
import { router } from '@jgl/utils';
import { useCallback } from 'react';
// import { Image } from 'tamagui';
import { routerMap } from '../utils/routerMap';
/**
 * 个人信息
 */
export const PersonalInfo = () => {
  const nick = useAppSelector((state) => state.userInfo.nick);

  const onPressNick = useCallback(() => {
    router.push(routerMap.EditNick);
  }, []);

  return (
    <JglYStack pt={8}>
      <JglTouchable onPress={onPressNick} disabled>
        <JglXStack
          backgroundColor='white'
          alignItems='center'
          paddingHorizontal={20}
          paddingVertical={15}
        >
          <JglText fontSize={14} color='#666666'>
            昵称
          </JglText>
          <JglText flex={1} textAlign='right' fontSize={14} color='#333333'>
            {nick}
          </JglText>
          {/* <Image className="ml-[13]" source={require('../assets/images/ic_right_arrow.png')} /> */}
        </JglXStack>
      </JglTouchable>
    </JglYStack>
  );
};
