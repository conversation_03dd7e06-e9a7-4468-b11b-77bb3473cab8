import { View, Image, Text } from 'react-native';

type Props = {
  src: string;
  description: string;
};

/** 订单空态组件 */
export const EmptyView = (props: Props) => {
  const { src, description } = props;

  return (
    <View className='flex-1 flex-col items-center justify-center'>
      <Image source={{ uri: src, width: 128, height: 128 }} />
      <Text className='mt-3 text-sm text-[#636F87]'>{description}</Text>
    </View>
  );
};
