import { withLoginLogicVersion } from '@bookln/bookln-biz';
import { useAgreementCheck } from '@jgl/biz-func';
import { JglTouchable } from '@jgl/ui-v4';
import { useAtomValue } from 'jotai';
import { useCallback } from 'react';
import type { LayoutChangeEvent } from 'react-native';
import type { MaterialTabItemProps } from 'react-native-collapsible-tab-view';
import Animated, { useAnimatedStyle } from 'react-native-reanimated';
import { atomMap } from '../atom';

type Props = MaterialTabItemProps<string> & {
  tabItemSpace: number;
  onTabItemLayout: (index: number, event: LayoutChangeEvent) => void;
};

/**
 *
 * @param props
 * @returns
 */
export const HomeContentTopTabItem = (props: Props) => {
  const { index, name, onPress, indexDecimal, onTabItemLayout, tabItemSpace } =
    props;

  const { withAgreementCheck } = useAgreementCheck();

  const homeTabIndex = useAtomValue(atomMap.homeTabIndexAtom);

  const animatedStyle = useAnimatedStyle(() => {
    let fontSize = 16; // 默认字体大小

    // 获取当前tab的index和小数部分
    const currentIndex = Math.floor(indexDecimal.value);
    const progress = indexDecimal.value - currentIndex; // 0-1之间的动画进度

    // 判断当前tab是否是正在切换的tab
    const isCurrentTab = currentIndex === index;
    const isNextTab = currentIndex + 1 === index;

    if (isCurrentTab) {
      // 当前tab从18->16
      fontSize = 18 - progress * 2;
    } else if (isNextTab) {
      // 下一个tab从16->18
      fontSize = 16 + progress * 2;
    }

    return {
      fontSize,
      fontWeight: fontSize >= 17 ? 'bold' : '400',
      zIndex: 10,
    };
  });

  const isSelected = homeTabIndex === index;

  const onLayout = useCallback(
    (event: LayoutChangeEvent) => {
      onTabItemLayout?.(index, event);
    },
    [index, onTabItemLayout],
  );

  const handlePress = useCallback(() => {
    onPress(name);
  }, [onPress, name]);

  return (
    <JglTouchable
      display='flex'
      style={{ marginRight: tabItemSpace }}
      onLayout={onLayout}
      onPress={withLoginLogicVersion(
        handlePress,
        withAgreementCheck(handlePress),
      )}
    >
      <Animated.Text
        style={[animatedStyle, { color: isSelected ? '#151B37' : '#676B7D' }]}
      >
        {name}
      </Animated.Text>
    </JglTouchable>
  );
};
