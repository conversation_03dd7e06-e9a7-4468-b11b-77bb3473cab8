import { useAppSelector } from '@jgl/biz-func';
import { JglButton, JglSafeArea, JglText, JglXStack, JglYStack } from '@jgl/ui-v4';
import { useAccountAndSafety } from '../hooks/useAccountAndSafety';
/**
 * 账号与安全
 */
export const AccountAndSafety = () => {
  const userId = useAppSelector((state) => state.userInfo.userId);
  const { cancelAccount } = useAccountAndSafety();

  return (
    <JglYStack height={'100%'} pt={8}>
      <JglYStack flex={1}>
        <JglXStack
          backgroundColor="white"
          alignItems="center"
          paddingHorizontal={20}
          paddingVertical={15}
        >
          <JglText fontSize={14} color="#666666">
            书链ID
          </JglText>
          <JglText flex={1} textAlign="right" fontSize={14} color="#333333">
            {userId}
          </JglText>
        </JglXStack>
      </JglYStack>
      <JglSafeArea flex={0}>
        <JglButton onPress={cancelAccount} color="transparent" marginVertical={10}>
          <JglText color="#333333" fontSize={16}>
            注销账号
          </JglText>
        </JglButton>
      </JglSafeArea>
    </JglYStack>
  );
};
