import { JGLWebView } from '@jgl/components';
import { JglStateView } from '@jgl/ui-v4';
import { envVars } from '@jgl/utils';
import { Stack } from 'expo-router';
import type { WebViewProps } from 'react-native-webview';
import { useCommonProblem } from '../hooks/useCommonProblem';

export const CommonProblem = () => {
  const { data, isLoading, error, retry, title } = useCommonProblem();
  return (
    <>
      <Stack.Screen
        options={{
          title: title,
        }}
      />
      <JglStateView
        isLoading={isLoading}
        error={error}
        onRetry={retry}
        emptyProps={{ message: '暂无数据' }}
      >
        <JGLWebView<WebViewProps>
          overScrollMode={'never'}
          containerStyle={{
            backgroundColor: 'white',
          }}
          webviewDebuggingEnabled
          source={{ html: data ?? '' }}
          supportLogin={true}
          vid={envVars.appVersionId()}
        />
      </JglStateView>
    </>
  );
};
