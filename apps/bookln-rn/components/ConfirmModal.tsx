import React, {
  forwardRef,
  useImperativeHandle,
  useState,
  useCallback,
} from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Modal from 'react-native-modal';

type Props = {
  isVisible?: boolean;
  title?: string;
  content?: string;
  onConfirm?: VoidFunction;
  onCancel?: VoidFunction;
  confirmText?: string;
  cancelText?: string;
};

export type ConfirmModalRef = {
  showModal?: VoidFunction;
  onCancel?: VoidFunction;
};
const ConfirmModal = forwardRef<ConfirmModalRef, Props>((props, ref) => {
  const {
    title,
    content,
    onConfirm,
    confirmText = '确定',
    cancelText = '取消',
  } = props;
  const [visible, setVisible] = useState(false);

  const showModal = useCallback(() => {
    setVisible(true);
  }, []);

  const onCancel = useCallback(() => {
    setVisible(false);
    props?.onCancel?.();
  }, [props]);

  useImperativeHandle(
    ref,
    () => ({
      showModal,
      onCancel,
    }),
    [onCancel, showModal],
  );

  return (
    <Modal isVisible={visible} onBackdropPress={onCancel}>
      <View style={styles.modalContent}>
        {/* 可以定制标题和内容 */}
        {title && <Text style={styles.modalTitle}>{title}</Text>}
        {content && <Text style={styles.modalText}>{content}</Text>}

        {/* 操作按钮区 */}
        <View style={styles.modalButtons}>
          <TouchableOpacity
            style={[styles.button, styles.cancelButton]}
            onPress={onCancel}
          >
            <Text style={styles.buttonText}>{cancelText}</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.button} onPress={onConfirm}>
            <Text style={styles.buttonText}>{confirmText}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
});

const styles = StyleSheet.create({
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    marginBottom: 10,
  },
  modalText: {
    fontSize: 16,
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  button: {
    backgroundColor: '#2ecc71',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 4,
  },
  cancelButton: {
    backgroundColor: '#585858',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
  },
});

export default ConfirmModal;
