import { useMemo } from 'react';
import type { SharedValue } from 'react-native-reanimated';
import Animated, { useAnimatedStyle } from 'react-native-reanimated';

type Props = {
  indexDecimal: SharedValue<number>;
  tabItemLayouts: SharedValue<{
    [key: number]: {
      x: number;
      width: number;
    };
  }>;
  index: number;
  tabItemSpace: number;
};

/**
 * 指示器宽度
 */
const indicatorSize = 32;

/**
 * 首页顶部tab指示器
 */
export const HomeContentTopTabIndicator = (props: Props) => {
  const { indexDecimal, index, tabItemLayouts, tabItemSpace } = props;

  const stepWidth = useMemo(() => tabItemSpace + indicatorSize, [tabItemSpace]);

  const animatedStyle = useAnimatedStyle(() => {
    const currentIndexTab = tabItemLayouts.value[index];
    if (currentIndexTab !== undefined) {
      const { x, width } = currentIndexTab;
      const offsetX = x + (width - indicatorSize) / 2;
      return {
        transform: [
          { translateX: offsetX + (indexDecimal.value - index) * stepWidth },
        ],
      };
    }
    return {};
  });

  return (
    <Animated.Image
      source={require('../assets/images/ic_home_indicator.png')}
      width={indicatorSize}
      height={6}
      style={animatedStyle}
      className='absolute bottom-[6px]'
    />
  );
};
