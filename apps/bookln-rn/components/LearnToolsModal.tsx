/* eslint-disable import/no-commonjs */
import { JglBottomModal, JglImage, JglText, JglTouchable, JglXStack, JglYStack } from '@jgl/ui-v4';
import { router } from '@jgl/utils';
import { useCallback } from 'react';
import type { ImageSourcePropType } from 'react-native';

// 定义工具项和工具类别接口
interface ToolItem {
  name: string;
  icon: ImageSourcePropType;
  type: LearnToolsType;
  pageType: 'app' | 'h5';
}

enum LearnToolsType {
  OralArithmeticPractice = '口算练习',
  LearnPinYin = '学拼音',
  LearnToDraw = '学笔画',
  LearnPianPang = '学偏旁',
  SpeakingAssessment = '口语测评',
  MemorizeWords = '背单词',
  PracticeSpeaking = '练口语',
}

interface ToolCategory {
  title: string;
  tools: ToolItem[];
}

// 定义学习工具数据
// 注意：以下图标均为占位符，请根据您的实际资源进行替换。
// 设计稿中的图标：错题本(A字红底)、口算练习(放大镜)、学拼音(a字绿底)、学笔画(字黄底)、学偏旁(A字红底)、口语测评(话筒)、背单词(书本)、练口语(书卷)
const learningToolsData: ToolCategory[] = [
  // {
  //   title: '热门工具',
  //   tools: [{ name: '错题本', icon: require('../assets/images/ic_cuotiben.png') }],
  // },
  {
    title: '数学学习',
    tools: [
      {
        name: '口算练习',
        icon: require('../assets/images/ic_kousuanlianxi.png'),
        type: LearnToolsType.OralArithmeticPractice,
        pageType: 'app',
      },
    ],
  },
  {
    title: '语文学习',
    tools: [
      {
        name: '学拼音',
        icon: require('../assets/images/ic_xuepinyin.png'),
        type: LearnToolsType.LearnPinYin,
        pageType: 'h5',
      },
      {
        name: '学笔画',
        icon: require('../assets/images/ic_xuebihua.png'),
        type: LearnToolsType.LearnToDraw,
        pageType: 'h5',
      },
      {
        name: '学偏旁',
        icon: require('../assets/images/ic_xuepianpang.png'),
        type: LearnToolsType.LearnPianPang,
        pageType: 'h5',
      },
    ],
  },
  // {
  //   title: '英语学习',
  //   tools: [
  //     {
  //       name: '口语测评',
  //       icon: require('../assets/images/ic_kouyuceping.png'),
  //       type: LearnToolsType.SpeakingAssessment,
  //     },
  //     {
  //       name: '背单词',
  //       icon: require('../assets/images/ic_beidanci.png'),
  //       type: LearnToolsType.MemorizeWords,
  //     },
  //     {
  //       name: '练口语',
  //       icon: require('../assets/images/ic_liankouyu.png'),
  //       type: LearnToolsType.PracticeSpeaking,
  //     },
  //   ],
  // },
];

const learningToolsTypeToPageUrlMap: Record<LearnToolsType, string | undefined> = {
  [LearnToolsType.OralArithmeticPractice]: undefined,
  [LearnToolsType.LearnPinYin]: 'https://mp.bookln.cn/webappv2/studySpell/index.htm',
  [LearnToolsType.LearnToDraw]: 'https://mp.bookln.cn/q?c=120N4MJM1FA',
  [LearnToolsType.LearnPianPang]: 'https://mp.bookln.cn/q?c=120N83BP597',
  [LearnToolsType.SpeakingAssessment]: undefined,
  [LearnToolsType.MemorizeWords]: undefined,
  [LearnToolsType.PracticeSpeaking]: undefined,
};

/**
 * 学习工具模态框
 */
export const LearnToolsModal = ({
  visible,
  onDismiss,
}: {
  visible: boolean;
  onDismiss: () => void;
}) => {
  const onPressLearnTools = useCallback(
    (tool: ToolItem) => {
      console.log(tool);

      if (tool.pageType === 'app') {
        // router.push('/webView', { url: tool.type, title: tool.name });
        if (tool.name === LearnToolsType.OralArithmeticPractice) {
          router.push('/oralArithmetic');
        }
      } else {
        const targetPageUrl: string | undefined = learningToolsTypeToPageUrlMap[tool.type];

        // const env = container.env().env();
        // if (env === Env.Dev || env === Env.Daily || env === Env.Local) {
        //   targetPageUrl = targetPageUrl?.replace('mp.bookln.cn', 'mp-daily.bookln.cn');
        // } else if (env === Env.Prepub) {
        //   targetPageUrl = targetPageUrl?.replace('mp.bookln.cn', 'mp-prepub.bookln.com');
        // }

        if (targetPageUrl) {
          router.push('/webView', { url: targetPageUrl, title: tool.name, disableScroll: true });
        }
      }

      onDismiss();
    },
    [onDismiss],
  );

  return (
    <JglBottomModal
      isShow={visible}
      onDismiss={onDismiss}
      title="学习工具"
      bg="$background"
      containerClassName="max-h-[80%] h-full"
    >
      <JglYStack flex={1} bg="$background" space={16} overflowY="scroll">
        {learningToolsData.map((category) => (
          <JglYStack key={category.title} px={16} space={12}>
            <JglText fontSize={18} color="#151B37" fontWeight="500">
              {category.title}
            </JglText>
            <JglXStack space={32} flexWrap="wrap" jc="flex-start">
              {category.tools.map((tool) => (
                <JglTouchable key={tool.name} onPress={() => onPressLearnTools(tool)}>
                  <JglYStack key={tool.name} space={6} alignItems="center" px={5}>
                    <JglImage source={tool.icon as any} width={40} height={40} />
                    <JglText fontSize={12} color="#202020" fontWeight="400" textAlign="center">
                      {tool.name}
                    </JglText>
                  </JglYStack>
                </JglTouchable>
              ))}
            </JglXStack>
          </JglYStack>
        ))}
      </JglYStack>
    </JglBottomModal>
  );
};
