import { JglYStack } from '@jgl/ui-v4';
// eslint-disable-next-line import/no-named-as-default
import LinearGradient from 'react-native-linear-gradient';

/**
 * 首页背景
 */
export const HomeBackground = () => (
  <JglYStack jglClassName='flex-1 bg-[#FAFAFA] h-full absolute top-0 left-0 right-0 bottom-0'>
    <LinearGradient
      colors={['rgba(67, 125, 255, 1)', '#FAFAFA']}
      className='h-1/4'
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 1 }}
    />
  </JglYStack>
);
