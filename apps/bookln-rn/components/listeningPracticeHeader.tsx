import { JglText, JglTouchable, JglView, JglXStack, JglYStack } from '@jgl/ui-v4';
import { router } from '@jgl/utils';
import { useCallback, useMemo } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Image } from 'tamagui';

/**
 * 听力练习头部
 */
export const ListeningPracticeHeader = () => {
  const { top } = useSafeAreaInsets();

  /**
   * 返回按钮图片
   */
  const backImgSource = useMemo(() => {
    return require('../assets/images/ic_back_light.png');
  }, []);

  /**
   * 右侧记录按钮图片
   */
  const rightRecordImgSource = useMemo(() => {
    return require('../assets/images/ic_record_light.png');
  }, []);

  const onPressBack = useCallback(() => {
    router.back();
  }, []);

  const onPressRecord = useCallback(() => {
    console.log('onPressRecord');
  }, []);

  return (
    <JglYStack>
      <JglView height={top} />
      <JglXStack
        minHeight={44}
        paddingHorizontal={16}
        marginTop={8}
        justifyContent="space-between"
        alignItems="center"
      >
        <JglTouchable minWidth={0} onPress={onPressBack}>
          <Image width={24} height={24} source={backImgSource} />
        </JglTouchable>
        <JglText fontSize={18} fontWeight="500" color="white">
          听力练习
        </JglText>
        <JglTouchable minWidth={0} onPress={onPressRecord}>
          <Image width={32} height={32} source={rightRecordImgSource} />
        </JglTouchable>
      </JglXStack>
    </JglYStack>
  );
};
