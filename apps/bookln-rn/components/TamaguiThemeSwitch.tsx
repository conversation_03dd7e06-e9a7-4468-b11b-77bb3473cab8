import { tamaguiThemeNameAtom } from '@jgl/biz-func';
import { useAtom } from 'jotai';
import { useCallback } from 'react';
import { Button, Text, ThemeName, View, XStack, YStack } from 'tamagui';
import { tamaguiConfig } from '../tamagui.config';

export const TamaguiThemeSwitch = () => {
  const [tamaguiThemeName, setTamaguiThemeName] = useAtom(tamaguiThemeNameAtom);

  const {
    light,
    light_blue,
    light_pink,
    light_orange,
    light_red,
    light_purple,
    light_yellow,
    light_gray,

    dark,
    dark_blue,
    dark_pink,
    dark_orange,
    dark_red,
    dark_purple,
    dark_yellow,
    dark_gray,
  } = tamaguiConfig.themes;

  const lightThemes = {
    light,
    light_blue,
    light_pink,
    light_orange,
    light_red,
    light_purple,
    light_yellow,
    light_gray,
  };

  const darkThemes = {
    dark,
    dark_blue,
    dark_pink,
    dark_orange,
    dark_red,
    dark_purple,
    dark_yellow,
    dark_gray,
  };

  const lightAndDarkThemes = {
    lightThemes,
    darkThemes,
  };

  const handleClearTheme = useCallback(async () => {
    await setTamaguiThemeName(undefined);
  }, [setTamaguiThemeName]);

  return (
    <YStack space={'$2'} bg={'$background'}>
      {Object.entries(lightAndDarkThemes).map((themeArray, i) => {
        return (
          <YStack key={themeArray[0]} space={'$1'}>
            <Text>{themeArray[0]}</Text>
            <XStack space={'$2'} flexWrap='wrap'>
              {Object.entries(themeArray[1]).map((t) => {
                return (
                  <View
                    className='items-center justify-center'
                    onPress={() => {
                      setTamaguiThemeName(t[0] as ThemeName);
                    }}
                    key={t[1].color9.val}
                    bg={t[1].color9}
                    w={'$3'}
                    h={'$3'}
                  >
                    {tamaguiThemeName === t[0] ? (
                      <View bg={t[1].color5} w={'$1'} h={'$1'} />
                    ) : null}
                  </View>
                );
              })}
            </XStack>
          </YStack>
        );
      })}

      <Button onPress={handleClearTheme}>清空主题色</Button>
    </YStack>
  );
};
