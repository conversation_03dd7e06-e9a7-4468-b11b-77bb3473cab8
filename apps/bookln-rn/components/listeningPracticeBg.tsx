import { useWindowDimensions } from '@jgl/biz-func';
import { JglImage, JglView } from '@jgl/ui-v4';
// eslint-disable-next-line import/no-named-as-default
import LinearGradient from 'react-native-linear-gradient';

/**
 * 听力练习背景
 */
export const ListeningPracticeBg = () => {
  const { isWideScreen } = useWindowDimensions();

  return (
    <JglView position="absolute" top={0} left={0} right={0} bottom={0} backgroundColor="white">
      {isWideScreen ? (
        <LinearGradient
          colors={['#76A0FF', '#ADC6FFCC', '#E9EEFF00', '#00000000']}
          className="w-full aspect-[1024/239]"
        />
      ) : (
        <JglImage
          source={require('../assets/images/ic_listening_practice_top_bg.png')}
          width={'100%'}
          style={{ aspectRatio: 375 / 253 }}
        />
      )}
    </JglView>
  );
};
