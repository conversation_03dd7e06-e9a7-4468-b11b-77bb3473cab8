import { withLogin } from '@bookln/bookln-biz';
import {
  routerMap as bizRouterMap,
  useAgreementCheck,
  useBizRouter,
  useIsUuidUser,
} from '@jgl/biz-func';
import {
  JglGridView,
  JglText,
  JglTouchable,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { envVars, router } from '@jgl/utils';
import { useCallback, useMemo } from 'react';
import { Image } from 'tamagui';
import { getRealMpHost } from '../utils/mpUrlHostHelper';
import { routerMap } from '../utils/routerMap';

/**
 * 其他服务
 */
export const MineContentOtherServices = () => {
  const { withAgreementCheck } = useAgreementCheck();

  const isUuidUser = useIsUuidUser();

  const bizRouter = useBizRouter();

  const onPressOrder = useCallback(() => {
    if (envVars.loginLogicVersion() === 'new') {
      router.push(routerMap.WebView, {
        url: `${getRealMpHost()}/personal.htm#/myOrder`,
      });
    } else {
      if (isUuidUser) {
        bizRouter.push(bizRouterMap.logInModal, { backPath: '..' });
      } else {
        router.push(routerMap.WebView, {
          url: `${getRealMpHost()}/personal.htm#/myOrder`,
        });
      }
    }
  }, [bizRouter, isUuidUser]);

  /**
   * 客服中心
   */
  const onPressCustomerServiceCenter = useCallback(() => {
    router.push(routerMap.CustomerServiceCenter);
  }, []);

  const onPressSetting = useCallback(() => {
    router.push(routerMap.Setting);
  }, []);

  /**
   * 其他服务功能列表
   */
  const otherServiceList = useMemo(() => {
    const list = [
      {
        onPress:
          envVars.loginLogicVersion() === 'new'
            ? withAgreementCheck(onPressOrder)
            : withLogin(onPressOrder),
        key: 'my_order',
        icon: require('../assets/images/ic_my_order.png'),
        title: '我的订单',
      },
      {
        onPress: withAgreementCheck(onPressCustomerServiceCenter),
        key: 'customer_service_center',
        icon: require('../assets/images/ic_service_center.png'),
        title: '客服中心',
      },
      {
        onPress: withAgreementCheck(onPressSetting),
        key: 'setting',
        icon: require('../assets/images/ic_setting.png'),
        title: '设置',
      },
    ];

    return list;
  }, [
    onPressCustomerServiceCenter,
    onPressOrder,
    onPressSetting,
    withAgreementCheck,
  ]);

  return (
    <JglYStack
      jglClassName='p-[12px] mt-[16px] bg-white'
      marginHorizontal={16}
      borderRadius={8}
      space={12}
    >
      <JglText fontSize={16} fontWeight='bold'>
        其他服务
      </JglText>
      <JglXStack>
        <JglGridView
          minColumns={3}
          minItemWidth={114}
          horizontalSpace={56}
          gap={12}
        >
          {otherServiceList.map((item) => (
            <JglTouchable onPress={item.onPress} key={item.title} w={'100%'}>
              <JglYStack space={4} jglClassName='items-center'>
                <Image source={item.icon} width={26} height={26} />
                <JglText fontSize={12} color='#151B37'>
                  {item.title}
                </JglText>
              </JglYStack>
            </JglTouchable>
          ))}
        </JglGridView>
      </JglXStack>
    </JglYStack>
  );
};
