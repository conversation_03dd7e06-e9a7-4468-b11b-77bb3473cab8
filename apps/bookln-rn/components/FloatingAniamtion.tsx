import React, { useEffect, useRef } from 'react';
import { Animated, Easing, Image, ImageSourcePropType } from 'react-native';

/* 上下悬浮动画 */
const FloatingAnimation = ({
  source,
  style,
}: {
  source: ImageSourcePropType;
  style: Object;
}) => {
  // 创建一个动画值
  const floatAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // 定义动画循环的函数
    const startAnimation = () => {
      // 使用Animated.sequence来串联动画效果
      Animated.sequence([
        // Animated.timing用于推动一个值按照一个过渡曲线而随时间变化
        // 这里让floatAnim的值从0变为1，代表一个向上浮动的过程
        Animated.timing(floatAnim, {
          toValue: 1,
          duration: 1000, // 持续时间1000ms
          useNativeDriver: true,
          easing: Easing.inOut(Easing.quad), // 缓动函数
        }),
        // 再从1变回0，代表向下沉的过程
        Animated.timing(floatAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
          easing: Easing.inOut(Easing.quad),
        }),
      ]).start(() => {
        // 每当序列动画结束，再次开始，实现循环效果
        startAnimation();
      });
    };

    // 启动动画
    startAnimation();
  }, [floatAnim]);

  // 使用floatAnim创建悬浮动画样式
  const floatStyle = {
    transform: [
      {
        // 动画值floatAnim将会在0到1之间变化，我们把这个值映射到实际的移动距离
        translateY: floatAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0, -10], // 10个单位的上下移动
        }),
      },
    ],
  };

  return (
    <Animated.View style={[floatStyle, style]}>
      <Image source={source} className='h-full w-full' />
    </Animated.View>
  );
};

export default FloatingAnimation;
