import {
  agreementLinks,
  isLoginScreenAgreementCheckedAtom,
  openExternalLink,
} from '@jgl/biz-func';
import { Check } from '@tamagui/lucide-icons';
import { usePathname } from 'expo-router';
import { useAtom, useSetAtom } from 'jotai';
import { useCallback, useEffect } from 'react';
import { Checkbox, Image, Text, XStack } from 'tamagui';

type Props = { textClassName?: string };

const checkboxHitSlop = { bottom: 16, top: 16, left: 16, right: 16 };

export const LoginAgreement = (props: Props) => {
  const { textClassName } = props;
  const [isLoginScreenAgreementChecked, setIsLoginScreenAgreementChecked] =
    useAtom(isLoginScreenAgreementCheckedAtom);

  useResetIsLoginAgreementChecked();

  const handlePressNormalText = useCallback(() => {
    setIsLoginScreenAgreementChecked((checked) => !checked);
  }, [setIsLoginScreenAgreementChecked]);

  return (
    <XStack space={'$2'} className={textClassName}>
      <Checkbox
        hitSlop={checkboxHitSlop}
        borderRadius={1000}
        w={16}
        h={16}
        borderColor={'#D0D1D7'}
        checked={isLoginScreenAgreementChecked}
        onCheckedChange={(checked) => {
          if (checked === 'indeterminate') {
            setIsLoginScreenAgreementChecked(false);
          } else {
            setIsLoginScreenAgreementChecked(checked);
          }
        }}
      >
        <Checkbox.Indicator>
          <Image
            source={require('../assets/images/ic_checked.png')}
            width={18}
            height={18}
          />
        </Checkbox.Indicator>
      </Checkbox>
      <Text className='flex-1 text-sm'>
        <Text onPress={handlePressNormalText} className='text-description'>
          我已阅读并同意
        </Text>
        {agreementLinks.map((link) => {
          const { name, url } = link;
          return (
            <Text
              className='text-[#4E76FF]'
              key={name}
              onPress={() => openExternalLink(url)}
            >
              《{name}》
            </Text>
          );
        })}
      </Text>
    </XStack>
  );
};

/**
 * fix华为市场审核被拒绝的问题：
 * ```
    2.您应用的隐私政策未以明示同意的方式征得用户同意，不符合华为应用市场审核标准。
    测试步骤：微信登录模块点击同意隐私政策后，点击手机号登录，跳转页面默认勾选同意隐私政策。
    修改建议：请确保应用内的隐私政策有提供空白复选框且由用户自愿、明确勾选/弹窗有拒绝选项。
    请参考《审核指南》第7.5相关审核要求：https://developer.huawei.com/consumer/cn/doc/app/50104-07#h3-1683701612940-1
    APP常见个人信息保护问题FAQ请参考： https://developer.huawei.com/consumer/cn/doc/app/FAQ-faq-01#h3-1683538186544-7
    ```

    合理一点的方式是各个页面记录自己的状态，先简单粗暴的重置一下
 */
const useResetIsLoginAgreementChecked = () => {
  const setIsChecked = useSetAtom(isLoginScreenAgreementCheckedAtom);
  const pathname = usePathname();

  // ts-ignore
  useEffect(() => {
    setIsChecked(false);
  }, [setIsChecked, pathname]);
};
