import { externalUrls } from '@jgl/biz-func';
import {
  JglSafeArea,
  JglText,
  JglTouchable,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { copyToClipBoard, envVars } from '@jgl/utils';
import { showToast } from '@yunti-private/jgl-ui';
import { useCallback } from 'react';
import { Image, ScrollView } from 'tamagui';
import {
  docNames,
  OFFICIAL_QQ,
  showChildrenPrivacySwitch,
  URL_QQ_CHATTING_DETAIL,
} from '../utils/constants';
import { openUrl } from '../utils/UrlJumpHelper';

/**
 * 关于书链
 */
export const AboutBookln = () => {
  const onPressWeChat = useCallback(() => {
    // 复制书链客服微信号
    copyToClipBoard('bookln_cn');
    showToast({ title: '复制微信账号成功，打开微信后可以关注我们~' });
  }, []);

  const onPressQQ = useCallback(() => {
    openUrl(URL_QQ_CHATTING_DETAIL);
  }, []);

  const onPressUserAgreement = useCallback(() => {
    openUrl(externalUrls.agreement.url);
  }, []);

  const onPressPrivacy = useCallback(() => {
    openUrl(externalUrls.privacy.url);
  }, []);

  const onPressChildrenPrivacy = useCallback(() => {
    openUrl(externalUrls.privacyForChildren.url);
  }, []);

  const onPressRecordNumber = useCallback(() => {
    openUrl(externalUrls.icpLookUp.url);
  }, []);

  return (
    <JglSafeArea flex={1}>
      <ScrollView className='mx-[20px] mt-[20px] flex-1 rounded-[12px] bg-white'>
        <JglYStack mt={50} flex={3} jglClassName='flex-center'>
          <Image
            style={{ borderRadius: 10 }}
            source={require('../assets/images/about_bookln_logo.png')}
          />
          <JglTouchable
            onPress={onPressRecordNumber}
            mt={20}
            jglClassName='flex-center'
          >
            <JglText
              fontSize={16}
              color='#4E76FF'
            >{`${envVars.appIcp()} >`}</JglText>
          </JglTouchable>
          <JglText
            mt={20}
            fontSize={16}
          >{`Version ${envVars.appVersion()}`}</JglText>
        </JglYStack>
        <JglXStack mt={50} flex={2}>
          <JglTouchable flex={1} alignItems='center' onPress={onPressWeChat}>
            <JglYStack alignItems='center'>
              <Image source={require('../assets/images/about_wechat.png')} />
              <JglText fontSize={14} mt={15} color='#8f8f8f'>
                bookln_cn
              </JglText>
            </JglYStack>
          </JglTouchable>
          <JglTouchable flex={1} onPress={onPressQQ}>
            <JglYStack alignItems='center'>
              <Image source={require('../assets/images/about_QQ.png')} />
              <JglText fontSize={14} mt={15} color='#8f8f8f'>
                {' '}
                {OFFICIAL_QQ}
              </JglText>
            </JglYStack>
          </JglTouchable>
        </JglXStack>
      </ScrollView>
      <JglXStack pt={12} px={16} flexWrap='wrap' justifyContent='center'>
        <JglTouchable onPress={onPressUserAgreement}>
          <JglText fontSize={12} mt={6} color='#4E76FF'>
            {`《${docNames.userAgreement}》 `}
          </JglText>
        </JglTouchable>
        <JglTouchable testID={'1689692633'} onPress={onPressPrivacy}>
          <JglText fontSize={12} mt={6} color='#4E76FF'>
            {`《${docNames.privacy}》 `}
          </JglText>
        </JglTouchable>
        {showChildrenPrivacySwitch && (
          <JglTouchable testID={'1689692502'} onPress={onPressChildrenPrivacy}>
            <JglText fontSize={12} mt={6} color='#4E76FF'>
              {`《${docNames.childrenPrivacy}》`}
            </JglText>
          </JglTouchable>
        )}
      </JglXStack>

      <JglText fontSize={9} mb={10} textAlign='center' color='#999999'>
        {`© ${new Date().getFullYear()} Bookln. All Rights Reserved.`}
      </JglText>
    </JglSafeArea>
  );
};
