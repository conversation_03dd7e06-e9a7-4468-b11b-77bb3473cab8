import {
  JglImage,
  JglText,
  JglTouchable,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { useCallback } from 'react';
import type { LessonDTO } from '../dtos/LessonDTO';
import { navigateToWebLesson } from '../utils/WebViewHelper';

type Props = {
  lesson: LessonDTO;
};

/**
 * 图书条目
 */
export const LessonItem = ({ lesson }: Props) => {
  const { name = '', thumbnails, id, idSign } = lesson;

  // 点击图书条目
  const handlePress = useCallback(() => {
    navigateToWebLesson(id, idSign);
  }, [id, idSign]);

  return (
    <JglTouchable onPress={handlePress} w={'100%'}>
      <JglYStack
        jglClassName='w-full relative items-center'
        borderRadius={8}
        overflow='hidden'
      >
        <JglImage
          source={thumbnails ?? ''}
          borderRadius={8}
          defaultImage={require('../assets/images/img_lesson_cover.png')}
          width={'100%'}
          jglClassName='aspect-[142/80]'
        />
        <JglXStack
          position='absolute'
          justifyContent='center'
          w={'100%'}
          px={8}
          bottom={0}
          backgroundColor='rgba(0,0,0,0.5)'
        >
          <JglText fontSize={12} maxLines={1} color='#FFFFFF'>
            {name}
          </JglText>
        </JglXStack>
      </JglYStack>
    </JglTouchable>
  );
};
