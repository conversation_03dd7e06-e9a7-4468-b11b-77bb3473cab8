import {
  routerMap as bizRouterMap,
  useAgreementCheck,
  useAppSelector,
  useBizRouter,
  useIsUuidUser,
} from '@jgl/biz-func';
import { JglImage, JglText, JglTouchable, JglXStack } from '@jgl/ui-v4';
import { envVars, router } from '@jgl/utils';
import { useCallback, useMemo } from 'react';
import { Image } from 'tamagui';
import { routerMap } from '../utils/routerMap';

/**
 * 我的内容用户头像
 */
export const MineContentUserProfile = () => {
  const isUuidUser = useIsUuidUser();
  const bizRouter = useBizRouter();
  const userInfo = useAppSelector((state) => state.userInfo);
  const { nick, smallPhoto, name } = userInfo;
  const { withAgreementCheck } = useAgreementCheck();

  const handlePressLogin = useCallback(() => {
    router.push(routerMap.Login);
  }, []);

  const onPressLogin = useCallback(() => {
    bizRouter.push(bizRouterMap.logInModal, { backPath: '..' });
  }, [bizRouter]);

  // 渲染头像
  const renderHeader = useMemo(() => {
    if (isUuidUser || !smallPhoto) {
      return (
        <Image
          width={58}
          height={58}
          source={require('../assets/images/ic_uuid_header.png')}
        />
      );
    }
    return (
      <JglImage
        source={smallPhoto}
        defaultImage={require('../assets/images/ic_uuid_header.png')}
        borderRadius={9999}
        backgroundColor='transparent'
        width={58}
        height={58}
      />
    );
  }, [isUuidUser, smallPhoto]);

  return (
    <JglTouchable
      onPress={
        isUuidUser
          ? envVars.loginLogicVersion() === 'new'
            ? withAgreementCheck(handlePressLogin)
            : onPressLogin
          : undefined
      }
    >
      <JglXStack jglClassName='p-[16px] w-full items-center' space={8}>
        {renderHeader}
        <JglText fontWeight={'bold'} fontSize={20} color={'#171717'}>
          {isUuidUser ? '未登录' : nick ?? name}
        </JglText>
      </JglXStack>
    </JglTouchable>
  );
};
