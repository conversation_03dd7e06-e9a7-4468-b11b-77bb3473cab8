import { booklnAIAnimationDuration } from '@jgl/ai-qa-v2';
import {
  JglScrollView,
  JglText,
  JglTouchable,
  JglView,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { useAudio } from '@jgl/utils';
import { useAtomValue } from 'jotai';
import { Maximize2, Minimize2, Pause, Play } from 'lucide-react-native';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  LayoutAnimation,
  Platform,
  ScrollView,
  UIManager,
  type LayoutChangeEvent,
} from 'react-native';
import Sound from 'react-native-sound';
import { Progress, Spinner } from 'tamagui';
import { atomMap } from '../../../atom';
import { AiBookAudioTimestamp, AiBookTopicItem } from '../../../hooks/aiBook';

if (
  Platform.OS === 'android' &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

type Props = {
  audioUrl?: string;
  topic: AiBookTopicItem | undefined;
};
export const AiBookTopicListenSection = ({ audioUrl, topic }: Props) => {
  const { isPlay, play, resume, pause, seek, getAudioRef } = useAudio();
  const { topicId, pageId } = useAtomValue(atomMap.aiBookInfo);
  const topicDetailHeight = useAtomValue(atomMap.aiBookTopicDetailHeightAtom);
  const isVisible = topicId === topic?.itemId && pageId === topic?.pageId;
  const audioRef = getAudioRef?.()?.current as Sound | undefined;

  const [isLoading, setIsLoading] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [isMaximize, setIsMaximize] = useState(false);
  const containerYRef = useRef(0);
  const showLrc = isMaximize;

  useEffect(() => {
    if (!isVisible && isPlay) {
      pause();
    }
  }, [isPlay, isVisible, pause]);

  const handlePrev10s = () => {
    audioRef?.getCurrentTime((seconds) => {
      const time = Math.max(seconds - 10, 0);
      setCurrentTime(time);
      seek(time);
    });
  };

  const handleNext10s = () => {
    audioRef?.getCurrentTime((seconds) => {
      const time = Math.min(seconds + 10, audioRef?.getDuration() || 0);
      seek(time);
      setCurrentTime(time);
    });
  };

  const handlePlay = (src: string) => {
    if (audioRef?.isLoaded()) {
      resume();
    } else {
      setIsLoading(true);
      play({
        src,
        playerInitializedSuccessCallback: () => {
          setIsLoading(false);
        },
      });
    }
  };

  const handlePause = () => {
    pause();
    setIsLoading(false);
  };

  useEffect(() => {
    const timer = setInterval(() => {
      audioRef?.getCurrentTime((seconds) => {
        setCurrentTime(seconds);
      });
    }, 500);
    return () => {
      clearInterval(timer);
    };
  }, [audioRef]);

  const playingIndex = useMemo(() => {
    const index = topic?.audioTimestamps?.findIndex(
      (stamp) =>
        stamp.startTime <= currentTime && stamp.stopTime >= currentTime,
    );
    return index ?? -1;
  }, [currentTime, topic]);

  const playingLrc = useMemo(() => {
    if (playingIndex === -1) return [];
    return topic?.audioTimestamps?.[playingIndex]?.lrcs;
  }, [playingIndex, topic]);

  const renderDurationToShow = (time?: number) => {
    if (!time) return '00:00';
    time = Math.max(time, 0);
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handlePlaySubIndex = (t: AiBookAudioTimestamp) => {
    if (!isPlay) {
      setIsLoading(true);
      play({
        src: audioUrl || '',
        playerInitializedSuccessCallback: () => {
          setIsLoading(false);
          seek(t.startTime);
        },
      });
    } else {
      seek(t.startTime);
    }
  };

  const onPressMaximizeButton = () => {
    const customAnimation = {
      ...LayoutAnimation.Presets.easeInEaseOut,
      duration: booklnAIAnimationDuration, // 这里设置动画时长（单位：毫秒）
    };
    LayoutAnimation.configureNext(customAnimation);
    // setShowLrc(!isMaximize);
    setIsMaximize(!isMaximize);
  };

  const handleContainerLayout = useCallback((e: LayoutChangeEvent) => {
    const { y } = e.nativeEvent.layout;
    containerYRef.current = y;
  }, []);

  const containerHeight = useMemo(() => {
    return isMaximize ? topicDetailHeight - 42 : undefined;
  }, [isMaximize, topicDetailHeight]);

  const renderTopicIndex = () => {
    return (
      <JglXStack>
        <JglScrollView horizontal>
          <JglXStack spaceX={'$2'}>
            {topic?.audioTimestamps?.map((t, index) => {
              return (
                <JglTouchable
                  key={index}
                  borderColor='$gray7'
                  borderWidth={1}
                  borderRadius={3}
                  backgroundColor={
                    playingIndex === index ? '$gray7' : undefined
                  }
                  onPress={() => handlePlaySubIndex(t)}
                >
                  <JglText>{t.title}</JglText>
                </JglTouchable>
              );
            })}
          </JglXStack>
        </JglScrollView>

        {/* <JglTouchable
          onPress={() => {
            const customAnimation = {
              ...LayoutAnimation.Presets.easeInEaseOut,
              duration: booklnAIAnimationDuration, // 这里设置动画时长（单位：毫秒）
            };
            LayoutAnimation.configureNext(customAnimation);
            // setShowLrc(!showLrc);
          }}
        >
          <JglText color='$blue9'>{showLrc ? '隐藏原文' : '显示原文'}</JglText>
        </JglTouchable> */}
      </JglXStack>
    );
  };

  const lrcRef = useRef<ScrollView>(null);
  const lrcContainerRef = useRef<Map<number, number>>(new Map());
  const renderLrc = () => {
    if (!showLrc) return null;

    return (
      <JglView paddingVertical={'$0.5'} flex={1}>
        <JglScrollView flex={1} maxHeight={isMaximize ? 400 : 50} ref={lrcRef}>
          {playingLrc?.map((lrc, index) => {
            const nextLrc = playingLrc[index + 1] || { timestamp: Infinity };
            const isCurrentLine =
              currentTime >= lrc.timestamp && currentTime < nextLrc.timestamp;

            if (isCurrentLine) {
              const y = lrcContainerRef.current.get(index);
              lrcRef.current?.scrollTo({
                y,
                animated: true,
              });
            }
            return (
              <JglText
                paddingVertical={'$0.25'}
                key={lrc.id}
                color={isCurrentLine ? '$blue9' : undefined}
                // @ts-ignore
                onLayout={(e) => {
                  lrcContainerRef.current.set(index, e.nativeEvent.layout.y);
                }}
                fontSize={16}
              >
                {lrc.text}
              </JglText>
            );
          })}
        </JglScrollView>
      </JglView>
    );
  };

  const renderProgress = () => {
    return (
      <JglYStack>
        <Progress
          key={topicId}
          value={Math.round(
            (currentTime / (audioRef?.getDuration() || 1)) * 100,
          )}
          backgroundColor={'$gray7'}
          marginVertical={'$2'}
        >
          <Progress.Indicator animation='quick' />
        </Progress>
        <JglXStack justifyContent='space-between'>
          <JglText>{renderDurationToShow(currentTime)}</JglText>
          <JglText>{renderDurationToShow(audioRef?.getDuration())}</JglText>
        </JglXStack>
      </JglYStack>
    );
  };

  const renderControl = () => {
    return (
      <JglXStack justifyContent='center' spaceX={'$2'}>
        <JglTouchable onPress={handlePrev10s}>
          <JglText>后退10秒</JglText>
        </JglTouchable>
        <JglTouchable
          onPress={() => {
            isPlay ? handlePause() : handlePlay(audioUrl || '');
          }}
        >
          {isLoading ? (
            <Spinner width={24} height={24} />
          ) : isPlay ? (
            <Pause size={24} />
          ) : (
            <Play size={24} />
          )}
        </JglTouchable>
        <JglTouchable onPress={handleNext10s}>
          <JglText>前进10秒</JglText>
        </JglTouchable>
      </JglXStack>
    );
  };

  if (!audioUrl) return null;

  return (
    <JglYStack
      paddingVertical={'$0.5'}
      onLayout={handleContainerLayout}
      position='relative'
    >
      <JglText paddingVertical='$0.5' fontSize={16} fontWeight='600'>
        听力
      </JglText>
      <JglTouchable
        position='absolute'
        top={4}
        right={0}
        onPress={onPressMaximizeButton}
      >
        {isMaximize ? (
          <Minimize2 size={20} color='#171717' />
        ) : (
          <Maximize2 size={20} color='#171717' />
        )}
      </JglTouchable>
      <JglYStack
        ai='center'
        jc='center'
        position='relative'
        height={containerHeight}
        w='full'
      >
        <JglYStack w='full' h='full' flex={1}>
          {renderTopicIndex()}
          {renderLrc()}
          {renderProgress()}
          {renderControl()}
        </JglYStack>
      </JglYStack>
    </JglYStack>
  );
};
