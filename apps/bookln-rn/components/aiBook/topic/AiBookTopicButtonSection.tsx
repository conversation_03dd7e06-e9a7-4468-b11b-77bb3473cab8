import { JglGameButton, JglXStack } from '@jgl/ui-v4';

type Props = {
  showWordRecognition: boolean;
  handleWordRecognition: () => void;
  handleHint: () => void;
};
export const AiBookTopicButtonSection = ({
  handleWordRecognition,
  handleHint,
  showWordRecognition,
}: Props) => {
  return (
    <JglXStack marginTop={'$2.5'} spaceX={'$2.5'}>
      <JglGameButton onPress={handleHint} variant='primaryBorder'>
        小提示
      </JglGameButton>
      <JglGameButton
        onPress={handleWordRecognition}
        variant={showWordRecognition ? 'primary' : 'primaryBorder'}
      >
        单词识别
      </JglGameButton>
    </JglXStack>
  );
};
