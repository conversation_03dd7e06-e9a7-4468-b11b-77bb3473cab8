import { successVibrate } from '@jgl/biz-func';
import { container } from '@jgl/container';
import {
  JglText,
  JglTouchable,
  JglView,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { useAudio } from '@jgl/utils';
import { useGetState } from 'ahooks';
import { useAtomValue } from 'jotai';
import { Loader, Maximize2, Minimize2, Pause, Play } from 'lucide-react-native';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  Animated,
  DeviceEventEmitter,
  LayoutAnimation,
  type LayoutChangeEvent,
  Platform,
  UIManager,
  View,
} from 'react-native';
import ActionSheet from 'react-native-actionsheet';
import Markdown from 'react-native-markdown-display';
import type Sound from 'react-native-sound';
import { atomMap } from '../../../atom';
import type { AiBookTopicItem } from '../../../hooks/aiBook';
import { parseSrt, type Subtitle } from '../../../utils/parseSrt';
import { booklnAIAnimationDuration } from '@jgl/ai-qa-v2';

type Props = {
  topic: AiBookTopicItem | undefined;
  onScrollToPPTSection: (y: number) => void;
};

if (
  Platform.OS === 'android' &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

const playbackRateOptions = [0.5, 1.0, 1.5, 2.0];

export const AiBookTopicPPTSection = (props: Props) => {
  const { topic, onScrollToPPTSection } = props;
  const {
    audioSrc: playingSrc,
    play: playAudio,
    stop: stopAudio,
    resume: resumeAudio,
    pause: pauseAudio,
    changePlaybackRate: handleChangeAudioPlaybackRate,
    getAudioRef,
  } = useAudio();
  const topicDetailHeight = useAtomValue(atomMap.aiBookTopicDetailHeightAtom);
  const [isMaximize, setIsMaximize] = useState(false);
  const [audioPlayStatus, setAudioPlayStatus] = useState<
    'idle' | 'buffering' | 'playing' | 'paused'
  >('idle');
  const timerRef = useRef<NodeJS.Timer | null>(null);
  const [containerWidth, setContainerWidth] = useState(0);
  const [currentIndex, setCurrentIndex, getCurrentIndex] = useGetState(0);
  const [displayedIndex, setDisplayedIndex] = useState(currentIndex);
  const opacity = useRef(new Animated.Value(1)).current;
  const [animating, setAnimating] = useState(false);
  const [direction, setDirection] = useState(1); // 1: 下一题，-1: 上一题
  const translateXCurrent = useRef(new Animated.Value(0)).current;
  const translateXNext = useRef(new Animated.Value(0)).current;
  const hasSetWidthRef = useRef(false);

  const clearTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  const actionSheetRef = useRef<ActionSheet>(null);
  const [playbackRate, setPlaybackRate] = useState(1.0);
  const playbackRateOptionsInStr = useMemo(() => {
    return playbackRateOptions.map((item) => `${item}X`);
  }, []);

  const markdownList = useMemo(() => {
    if (!topic?.answerAnalysis?.markdown) return [];
    return topic.answerAnalysis.markdown
      .split('\n\n')
      .map((item) => item.trim());
  }, [topic]);

  const ttsUrls = useMemo(() => {
    if (!topic?.answerAnalysis?.ttsUrls) return [];
    return topic.answerAnalysis.ttsUrls.map((item) => item.trim());
  }, [topic]);

  const currentAudioUrl: string | undefined = useMemo(() => {
    return ttsUrls[currentIndex];
  }, [currentIndex, ttsUrls]);

  const srtUrls = useMemo(() => {
    if (!topic?.answerAnalysis?.srtUrls) return [];
    return topic.answerAnalysis.srtUrls.map((item) => item.trim());
  }, [topic]);

  const [srtList, setSrtList] = useState<string[]>([]);

  const dataValid = useMemo(() => {
    return (
      markdownList.length === ttsUrls.length &&
      ttsUrls.length === srtUrls.length
    );
  }, [markdownList, ttsUrls, srtUrls]);

  const totalLength = useMemo(() => {
    return dataValid ? markdownList.length : 0;
  }, [dataValid, markdownList]);

  const [parsedTitlesMap, setParsedTitlesMap] = useState<
    Map<number, Subtitle[]>
  >(new Map());

  useEffect(() => {
    const downloadAllSrts = async () => {
      try {
        const requests = srtUrls.map((url) => ({
          url,
          requestType: 'text' as const,
        }));

        const responses = await Promise.all(
          requests.map((request) => container.net().download(request)),
        );

        const srtListData = responses.map(
          (response) => response.data,
        ) as unknown as string[];
        setSrtList(srtListData);
      } catch (error) {
        console.error('Failed to download srt files:', error);
      }
    };

    if (dataValid) {
      downloadAllSrts();
    }
  }, [dataValid, srtUrls]);

  useEffect(() => {
    if (srtList.length > 0) {
      const newMap = new Map<number, Subtitle[]>();
      srtList.forEach((srt, index) => {
        const subtitles = parseSrt(srt);
        newMap.set(index, subtitles);
      });
      setParsedTitlesMap(newMap);
    }
  }, [srtList]);

  const currentSubtitles = useMemo(() => {
    return parsedTitlesMap.get(currentIndex) ?? [];
  }, [currentIndex, parsedTitlesMap]);

  const [currentDisplaySubtitle, setCurrentDisplaySubtitle] =
    useState<string>();

  // 切换 currentIndex 时启动动画
  useEffect(() => {
    if (currentIndex === displayedIndex) return;
    Animated.timing(opacity, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      setDisplayedIndex(currentIndex);
      Animated.timing(opacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();
    });
  }, [currentIndex, displayedIndex, opacity]);

  const updateCurrentPlayTime = useCallback(() => {
    timerRef.current = setInterval(() => {
      (getAudioRef?.()?.current as Sound)?.getCurrentTime(
        (seconds, isPlaying) => {
          const currentTimeInMiliseconds = seconds * 1000;

          // 判断当前播放进度在 currentSubtitles 中哪个 Item 的时间范围中

          const currentSubtitle = currentSubtitles.find((item) => {
            return (
              currentTimeInMiliseconds >= item.startTime &&
              currentTimeInMiliseconds <= item.endTime
            );
          });

          if (currentSubtitle) {
            setCurrentDisplaySubtitle(currentSubtitle.text);
          }
        },
      );
    }, 500);
  }, [currentSubtitles, getAudioRef]);

  useEffect(() => {
    if (audioPlayStatus === 'playing') {
      clearTimer();
      updateCurrentPlayTime();
    }

    return () => {
      clearTimer();
    };
  }, [clearTimer, audioPlayStatus, updateCurrentPlayTime]);

  const handleAudioInitializedSuccess = useCallback(() => {
    setAudioPlayStatus('playing');
  }, []);

  const playNextAudio = useCallback(() => {
    const latestIndex = getCurrentIndex();
    if (latestIndex < ttsUrls.length - 1) {
      const newIndex = latestIndex + 1;
      const isLastAudio = newIndex === ttsUrls.length - 1;
      console.log('[playNextAudio] newIndex:', newIndex);
      setCurrentIndex(newIndex);
      const targetUrl = ttsUrls[newIndex];
      console.log('[playNextAudio] ttsUrls:', ttsUrls);
      console.log('[playNextAudio] targetUrl:', targetUrl);
      if (!targetUrl) {
        console.log('[playNextAudio] no target url found, return');
        return;
      }
      setAudioPlayStatus('buffering');
      console.log('[playNextAudio] set audio status to buffering');
      playAudio({
        src: targetUrl,
        onended: () => {
          playNextAudio();
          if (isLastAudio) {
            successVibrate();
          }
        },
        playerInitializedSuccessCallback: handleAudioInitializedSuccess,
      });
      console.log('[playNextAudio] start playing audio:', targetUrl);
    } else {
      setAudioPlayStatus('idle');
    }
  }, [
    getCurrentIndex,
    handleAudioInitializedSuccess,
    playAudio,
    setCurrentIndex,
    ttsUrls,
  ]);

  const onPressPlayButton = useCallback(() => {
    if (audioPlayStatus === 'buffering') return;
    if (audioPlayStatus === 'idle') {
      if (currentAudioUrl) {
        setAudioPlayStatus('buffering');
        playAudio({
          src: currentAudioUrl,
          onended: () => {
            playNextAudio();
            if (currentIndex === ttsUrls.length - 1) {
              successVibrate();
            }
          },
          playerInitializedSuccessCallback: handleAudioInitializedSuccess,
        });
      }
    } else if (audioPlayStatus === 'playing') {
      pauseAudio();
      setAudioPlayStatus('paused');
    } else if (audioPlayStatus === 'paused') {
      resumeAudio();
      setAudioPlayStatus('playing');
    }
  }, [
    audioPlayStatus,
    currentAudioUrl,
    currentIndex,
    handleAudioInitializedSuccess,
    pauseAudio,
    playAudio,
    playNextAudio,
    resumeAudio,
    ttsUrls.length,
  ]);

  const onPressIndexButton = useCallback(
    (index: number) => {
      setCurrentIndex(index);
      const targetUrl = ttsUrls[index];
      if (!targetUrl) {
        return;
      }
      if (audioPlayStatus === 'buffering') return;
      if (audioPlayStatus === 'idle') {
        setAudioPlayStatus('buffering');
        playAudio({
          src: targetUrl,
          onended: () => {
            playNextAudio();
            console.log(
              'leejunhui - 🔥🔥🔥🔥🔥🔥 - onPressIndexButton - playAudio - onended',
              index,
              ttsUrls.length,
            );

            if (index === ttsUrls.length - 1) {
              console.log(
                'leejunhui - 🔥🔥🔥🔥🔥🔥 - AiBookTopicPPTSection - 播放到最后一个音频，震动提示',
              );
              successVibrate();
            }
          },
          playerInitializedSuccessCallback: handleAudioInitializedSuccess,
        });
      } else if (audioPlayStatus === 'playing') {
        if (targetUrl === playingSrc) {
          pauseAudio();
          setAudioPlayStatus('paused');
        } else {
          stopAudio();
          setTimeout(() => {
            setAudioPlayStatus('buffering');
            playAudio({
              src: targetUrl,
              onended: () => {
                playNextAudio();
                if (index === ttsUrls.length - 1) {
                  successVibrate();
                }
              },
              playerInitializedSuccessCallback: handleAudioInitializedSuccess,
            });
          }, 0);
        }
      } else if (audioPlayStatus === 'paused') {
        setAudioPlayStatus('buffering');
        playAudio({
          src: targetUrl,
          onended: () => {
            playNextAudio();
            if (index === ttsUrls.length - 1) {
              successVibrate();
            }
          },
          playerInitializedSuccessCallback: handleAudioInitializedSuccess,
        });
      }
    },
    [
      audioPlayStatus,
      handleAudioInitializedSuccess,
      pauseAudio,
      playAudio,
      playNextAudio,
      playingSrc,
      setCurrentIndex,
      stopAudio,
      ttsUrls,
    ],
  );

  const onPressPlaybackRateButton = useCallback(
    (index: number) => {
      if (index === playbackRateOptions.length) return;
      const targetPlaybackRate = playbackRateOptions[index] || 1.0;
      setPlaybackRate(targetPlaybackRate);
      handleChangeAudioPlaybackRate(targetPlaybackRate);
    },
    [handleChangeAudioPlaybackRate],
  );

  const renderPlayButtonIcon = useMemo(() => {
    if (audioPlayStatus === 'idle') {
      return <Play size={20} color='#171717' />;
    } else if (audioPlayStatus === 'buffering') {
      return <Loader size={20} color='#171717' />;
    } else if (audioPlayStatus === 'playing') {
      return <Pause size={20} color='#171717' />;
    } else if (audioPlayStatus === 'paused') {
      return <Play size={20} color='#171717' />;
    }
  }, [audioPlayStatus]);

  const containerYRef = useRef(0);

  const onPressMaximizeButton = useCallback(() => {
    const customAnimation = {
      ...LayoutAnimation.Presets.easeInEaseOut,
      duration: booklnAIAnimationDuration, // 这里设置你想要的动画时长（单位：毫秒）
    };
    LayoutAnimation.configureNext(customAnimation);
    setIsMaximize((prev) => {
      return !prev;
    });
  }, []);

  useEffect(() => {
    if (isMaximize) {
      console.log(
        'leejunhui - 🔥🔥🔥🔥🔥🔥 - AiBookTopicPPTSection - scrollTo',
        containerYRef.current,
      );
      onScrollToPPTSection(containerYRef.current);
    }
  }, [isMaximize, onScrollToPPTSection]);

  useEffect(() => {
    DeviceEventEmitter.addListener('aiBookTopicPPTSection', (event) => {
      console.log(
        'leejunhui - 🔥🔥🔥🔥🔥🔥 - AiBookTopicPPTSection - event',
        event,
      );
      if (event.type === 'play') {
        setIsMaximize(true);
        onPressPlayButton();
      } else if (event.type === 'pause') {
        pauseAudio();
        setAudioPlayStatus('paused');
      }
    });

    return () => {
      DeviceEventEmitter.removeAllListeners('aiBookTopicPPTSection');
    };
  }, [onPressPlayButton, pauseAudio]);

  const containerHeight = useMemo(() => {
    return isMaximize ? topicDetailHeight - 42 : undefined;
  }, [isMaximize, topicDetailHeight]);

  const handleContainerLayout = useCallback((event: LayoutChangeEvent) => {
    const { y } = event.nativeEvent.layout;
    console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - AiBookTopicPPTSection - y', y);
    containerYRef.current = y;
  }, []);

  // 获取容器宽度
  const handleAnimatedContainerLayout = useCallback(
    (event: LayoutChangeEvent) => {
      const w = event.nativeEvent.layout.width;
      if (!hasSetWidthRef.current) {
        setContainerWidth(w);
        hasSetWidthRef.current = true;
      }
      handleContainerLayout(event); // 保持原有逻辑
    },
    [handleContainerLayout],
  );

  if (!dataValid) {
    return null;
  }

  return (
    <JglYStack w='full' py={'$0.5'} onLayout={handleAnimatedContainerLayout}>
      <JglText paddingVertical='$0.5' fontSize={16} fontWeight='600'>
        AI 讲解
      </JglText>
      {dataValid ? (
        <JglYStack
          bg='#E1F0FF'
          ai='center'
          jc='center'
          p={16}
          space={10}
          position='relative'
          pt={36}
          height={containerHeight}
        >
          <JglTouchable
            position='absolute'
            top={10}
            right={10}
            onPress={onPressMaximizeButton}
          >
            {isMaximize ? (
              <Minimize2 size={20} color='#171717' />
            ) : (
              <Maximize2 size={20} color='#171717' />
            )}
          </JglTouchable>

          {/* Markdown 淡入淡出动画包裹 */}
          <JglView width='100%' overflow='hidden'>
            <View
              style={{ width: '100%', overflow: 'hidden' }}
              onLayout={handleAnimatedContainerLayout}
            >
              <Animated.View style={{ opacity, width: '100%' }}>
                <Markdown
                  style={{
                    text: { fontSize: 24 },
                    code_inline: {
                      color: '#171717',
                      backgroundColor: 'transparent',
                      fontWeight: 'bold',
                    },
                  }}
                >
                  {markdownList[displayedIndex]}
                </Markdown>
              </Animated.View>
            </View>
          </JglView>
          <JglYStack
            position={isMaximize ? 'absolute' : 'relative'}
            bottom={10}
            w='full'
            space={10}
          >
            {isMaximize && currentDisplaySubtitle ? (
              <JglView bg='$color3' ai='center' jc='center' w='full' p={10}>
                <JglText fontSize={14} color='#000000' textAlign='center'>
                  {currentDisplaySubtitle}
                </JglText>
              </JglView>
            ) : null}
            <JglXStack py={4} space={2} w='full' ai='center' jc='space-between'>
              {Array.from({ length: totalLength }).map((_, index) => {
                const isActive =
                  currentIndex === index && audioPlayStatus === 'playing';
                return (
                  <JglTouchable
                    bg={isActive ? '$color5' : '$color3'}
                    flex={1}
                    // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
                    key={index}
                    onPress={() => onPressIndexButton(index)}
                  >
                    <JglText>{index + 1}</JglText>
                  </JglTouchable>
                );
              })}
            </JglXStack>

            <JglXStack ai='center' jc='center' w='full' position='relative'>
              {/* <JglTouchable>
              <JglText>后退5秒</JglText>
            </JglTouchable> */}
              <JglTouchable onPress={onPressPlayButton}>
                {renderPlayButtonIcon}
              </JglTouchable>
              {/* <JglTouchable>
              <JglText>后退5秒</JglText>
            </JglTouchable> */}
              <JglTouchable
                position='absolute'
                right={16}
                onPress={() => actionSheetRef.current?.show()}
              >
                <JglText>{playbackRate}X</JglText>
              </JglTouchable>
            </JglXStack>
          </JglYStack>
        </JglYStack>
      ) : (
        <JglYStack bg='#E1F0FF' ai='center' jc='center' p={16}>
          <JglText>暂无数据</JglText>
        </JglYStack>
      )}
      <ActionSheet
        ref={actionSheetRef}
        options={[...playbackRateOptionsInStr, '取消']}
        cancelButtonIndex={playbackRateOptionsInStr.length}
        onPress={onPressPlaybackRateButton}
      />
    </JglYStack>
  );
};
