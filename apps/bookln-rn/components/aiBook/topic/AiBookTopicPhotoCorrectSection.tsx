import { JglView, JglYStack } from '@jgl/ui-v4';
import { AiBookTakePhotoResultDetail } from '../common/AiBookTakePhotoResultDetail';
import { useTakePhotoResult } from '../../../hooks/aiBook';

export const AiBookTopicPhotoCorrectSection = () => {
  let { answerResult } = useTakePhotoResult({ type: 'topic', autoFetch: true });
  if (answerResult.length === 0) return null;

  return (
    <JglView>
      <JglYStack paddingVertical={'$0.5'} spaceY={'$2'}>
        {/* <JglText paddingVertical='$0.5' fontSize={16} fontWeight='600'>
          我的作答
        </JglText> */}
        <AiBookTakePhotoResultDetail
          type='topic'
          specificAnswerResult={answerResult}
        />
      </JglYStack>
    </JglView>
  );
};
