import { JglText, JglView, JglYStack } from '@jgl/ui-v4';
import { Video, type AVPlaybackStatus } from 'expo-av';
import { useAtomValue } from 'jotai';
import { Play } from 'lucide-react-native';
import { useEffect, useRef, useState } from 'react';
import { Spinner } from 'tamagui';
import { atomMap } from '../../../atom';
import type { AiBookTopicItem } from '../../../hooks/aiBook';

type Props = {
  topic: AiBookTopicItem | undefined;
  videoUrl?: string;
};
export const AiBookTopicVideoSection = ({ videoUrl, topic }: Props) => {
  const { topicId, pageId } = useAtomValue(atomMap.aiBookInfo);
  const isVisible = topicId === topic?.itemId && pageId === topic?.pageId;
  const [status, setStatus] = useState<AVPlaybackStatus | null>(null);
  const video = useRef<Video | null>(null);
  const isPlaying = status?.isLoaded && status.isPlaying;

  useEffect(() => {
    if (!isVisible && isPlaying) {
      video.current?.pauseAsync();
    }
  }, [isPlaying, isVisible]);

  if (!videoUrl) return null;

  return (
    <JglYStack paddingVertical={'$0.5'}>
      <JglText paddingVertical='$0.5' fontSize={16} fontWeight='600'>
        视频讲解
      </JglText>
      <JglYStack spaceY={'$2'}>
        <Video
          source={{ uri: videoUrl }}
          style={{ width: '100%', height: 200 }}
          useNativeControls={true}
          ref={(r) => (video.current = r)}
          onPlaybackStatusUpdate={(val) => setStatus(val)}
        />
        {!isPlaying && (
          <JglView
            position='absolute'
            top={0}
            left={0}
            width={'100%'}
            height={'100%'}
            backgroundColor={'#00000020'}
            justifyContent='center'
            alignItems='center'
            style={{
              pointerEvents: 'none',
            }}
          >
            {/* <JglTouchable
              position='absolute'
              top='50%'
              left='50%'
              style={{
                transform: [{ translateX: -24 }, { translateY: -24 }],
              }}
            > */}
            <Play size={48} color={'white'} fill={'white'} />
            {/* </JglTouchable> */}
          </JglView>
        )}
        {!status?.isLoaded && (
          <Spinner
            style={{
              width: 20,
              height: 20,
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: [{ translateX: -10 }, { translateY: -10 }],
            }}
          />
        )}
      </JglYStack>
    </JglYStack>
  );
};
