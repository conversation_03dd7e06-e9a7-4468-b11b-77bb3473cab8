import { JglBottomModal, JglText, JglYStack } from '@jgl/ui-v4';
import { AiBookWordItem } from '../../../hooks/aiBook';

type Props = {
  word?: AiBookWordItem;
  setCurrentWord: (word?: AiBookWordItem) => void;
};
export const AiBookTopicWordSheet = (props: Props) => {
  const { setCurrentWord, word } = props;

  return (
    <JglBottomModal
      isShow={!!word}
      jglClassName='flex w-full flex-col overflow-hidden'
      style={{ height: '70%' }}
      showTop={false}
      keepBottom
      onDismiss={() => setCurrentWord(undefined)}
    >
      <JglYStack
        space={'$5'}
        width={'full'}
        height={'full'}
        alignItems='center'
        justifyContent='center'
        bg='$background'
        flex={1}
      >
        <JglText>{word?.text}</JglText>
        <JglText color='$blue10'>这是单词的解释</JglText>
      </JglYStack>
    </JglBottomModal>
  );
};
