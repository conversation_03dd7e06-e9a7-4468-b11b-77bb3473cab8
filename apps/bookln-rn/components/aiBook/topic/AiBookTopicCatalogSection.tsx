import { JglTex<PERSON>, Jgl<PERSON>ou<PERSON>ble, JglView, JglXStack } from '@jgl/ui-v4';
import { useAtom, useAtomValue } from 'jotai';
import { useMemo } from 'react';
import { FlatList } from 'react-native';
import { atomMap } from '../../../atom';
import type { AiBookTopicItem } from '../../../hooks/aiBook';

const cellWidth = 44;

export const AiBookTopicCatalogSection = () => {
  const [aiBookInfo, setAiBookInfo] = useAtom(atomMap.aiBookInfo);
  const aiBook = useAtomValue(atomMap.aiBook);
  const { pageId, topicId } = aiBookInfo;
  const currentPage = useMemo(() => {
    return aiBook?.pages?.find((item) => item.pageId === pageId);
  }, [aiBook?.pages, pageId]);

  const currentTopic = useMemo(() => {
    return currentPage?.topics?.find((item) => item.itemId === topicId);
  }, [currentPage?.topics, topicId]);

  const handlePressTopic = (item: AiBookTopicItem) => {
    setAiBookInfo((prev) => {
      return {
        ...prev,
        topicId: item.itemId,
      };
    });
  };

  const handlePressFullPage = () => {
    setAiBookInfo((prev) => {
      return {
        ...prev,
        topicId: undefined,
      };
    });
  };

  const renderCell = ({ item }: { item: AiBookTopicItem }) => {
    return (
      <JglTouchable
        onPress={() => handlePressTopic(item)}
        style={{ minWidth: 0, minHeight: 0 }}
      >
        <JglText
          color={item.itemId === currentTopic?.itemId ? '$gray12' : '$gray8'}
          fontWeight='bold'
          flexShrink={1}
          fontSize={16}
          w={cellWidth}
        >
          {item.title?.index}
        </JglText>
      </JglTouchable>
    );
  };

  return (
    <JglView paddingHorizontal={'$0.75'}>
      <JglXStack h={36}>
        <JglView px={'$2'} flex={1}>
          <FlatList
            data={currentPage?.topics || []}
            renderItem={renderCell}
            keyExtractor={(item) => item.itemId?.toString() ?? ''}
            horizontal
            showsHorizontalScrollIndicator={false}
            getItemLayout={(data, index) => ({
              length: cellWidth,
              offset: cellWidth * index,
              index,
            })}
          />
        </JglView>
        {currentTopic && (
          <JglTouchable ml={'$4'} onPress={handlePressFullPage}>
            <JglText fontSize={16} color={'$gray12'} textDecoration='underline'>
              查看整页
            </JglText>
          </JglTouchable>
        )}
      </JglXStack>
    </JglView>
  );
};
