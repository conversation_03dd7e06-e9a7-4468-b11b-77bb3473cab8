import { useNavigationBarHeight, useSafeAreaInsets } from '@jgl/biz-func';
import Icon from '@jgl/icon';
import { audioSounds, useAudio } from '@jgl/utils';
import { BlurView } from '@react-native-community/blur';
import type { INetworking } from '@yunti-private/net';
import { Image } from 'expo-image';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Pressable } from 'react-native';
import Modal from 'react-native-modal';
import Animated, {
  Easing,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import { Text, View } from 'tamagui';

type SceneChallengeResult = 'success' | 'fail';

type SceneChallengeModalProps = {
  net: INetworking;
  /**
   * 是否显示
   */
  visible: boolean;
  /**
   * 结果
   */
  result: SceneChallengeResult;
  /**
   * 成功音效
   */
  successAudio?: string;
  /**
   * 咕噜球数量
   */
  guluNum?: number;
  /**
   * 分数
   */
  score: number;
  /**
   * 题量
   */
  itemNum: number;
  /**
   * 点击了退出场景
   */
  onClickExitScene?: () => void;
  /**
   * 用户 ID
   */
  userId?: number;
  /**
   * 点击了重玩
   */
  onClickRestartScene?: () => void;
  /**
   * 点击了开通会员
   */
  onClickOpenVIP?: () => void;
  /**
   * 点击了排行榜
   */
  onClickRank?: () => void;
  /**
   * 点击了分享按钮
   */
  onClickShare?: () => void;

  showShareButton?: boolean;

  customChallengeSuccessHeroImg?: string;
  /**
   * 是否展示排行榜入口
   */
  showRank?: boolean;
  /**
   * 是否展示超过多少人
   */
  showSceneResult?: boolean;
};

/**
 * <AUTHOR>
 * @date 2024/03/26
 * @description 新版场景挑战结果 Modal
 */
export const SceneChallengeModal = (props: SceneChallengeModalProps) => {
  const {
    net,
    visible,
    result,
    guluNum,
    onClickExitScene,
    onClickRestartScene,
    onClickRank,
    showRank,
    customChallengeSuccessHeroImg,
  } = props;

  const navHeight = useNavigationBarHeight();
  const safeInsets = useSafeAreaInsets();
  const [maxRank, setMaxRank] = useState<number>(0);
  const { play } = useAudio();
  const sceneLightAnimateVal = useSharedValue(0);
  const sceneLightAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          rotate: `${interpolate(
            sceneLightAnimateVal.value,
            [0, 1],
            [0, 90],
          )}deg`,
        },
      ],
    };
  });

  useEffect(() => {
    if (visible) {
      if (result === 'success') {
        sceneLightAnimateVal.value = withTiming(
          1,
          { duration: 1500, easing: Easing.linear },
          () => {
            sceneLightAnimateVal.value = 0;
          },
        );
        play({ src: audioSounds.challenge_success_audio });
      } else {
        play({ src: audioSounds.challenge_fail_audio });
      }
    }
  }, [play, result, sceneLightAnimateVal, visible]);

  const renderResultHeroIcon = useMemo(() => {
    if (result === 'fail') {
      return (
        <Image
          source={Icon.sceneChallengeFail}
          className='z-[1] mb-[10px] h-[222px] w-[316px]'
        />
      );
    } else {
      return (
        <Image
          source={customChallengeSuccessHeroImg ?? Icon.sceneChallengeSuccess}
          className='z-[1] mb-[10px] h-[222px] w-[316px]'
        />
      );
    }
  }, [customChallengeSuccessHeroImg, result]);

  const shareButtonAnimatedSharedValue = useSharedValue(0);

  useEffect(() => {
    if (visible) {
      shareButtonAnimatedSharedValue.value = withRepeat(
        withTiming(1, { duration: 1000, easing: Easing.linear }),
        -1,
        false,
      );
    }
  }, [shareButtonAnimatedSharedValue, visible]);

  const renderButtonGroup = useMemo(() => {
    return (
      <View className='mt-[120px] flex w-full flex-col items-center justify-between gap-y-[20px]'>
        <Pressable
          onPress={onClickRestartScene}
          className={'h-[52px] w-[124px]'}
        >
          <Image
            source={Icon.sceneGoOnButton}
            className={'h-[52px] w-[124px]'}
          />
        </Pressable>
        <Pressable onPress={onClickExitScene} className={'h-[52px] w-[124px]'}>
          <Image source={Icon.backToHome} className={'h-[52px] w-[124px]'} />
        </Pressable>
      </View>
    );
  }, [onClickExitScene, onClickRestartScene]);

  const renderSceneLight = useMemo(() => {
    if (result === 'success') {
      return (
        <Animated.Image
          source={{ uri: Icon.sceneLight }}
          style={sceneLightAnimatedStyle}
          className='absolute left-[27px] top-[10px] z-0 h-[265px] w-[265px]'
        />
      );
    } else {
      return null;
    }
  }, [result, sceneLightAnimatedStyle]);

  const getSuccessResultPrefixText = useCallback((rank: number) => {
    if (rank <= 50) {
      return '加油哦，已超过';
    } else if (rank < 90) {
      return '很不错，超过';
    } else {
      return '太棒啦，超过';
    }
  }, []);

  const getRankTextColor = useCallback((rank: number) => {
    if (rank <= 50) {
      return '#54D6FF';
    } else if (rank > 50 && rank < 90) {
      return '#FFE500';
    } else {
      return '#52FF00';
    }
  }, []);

  const renderSceneResultText = useMemo(() => {
    if (result === 'success') {
      return (
        <Text
          className='mb-[10px] text-center font-semibold text-[#FFFCE1] after:w-full'
          style={{
            fontSize: 18,
          }}
        >
          {getSuccessResultPrefixText(maxRank)}
          <Text
            className='mx-[5px] font-semibold'
            style={{
              fontSize: 24,
              color: getRankTextColor(maxRank),
            }}
          >
            {maxRank}%
          </Text>
          的人
        </Text>
      );
    } else {
      return (
        <Text
          className='mb-[10px] font-semibold text-[#FFFCE1]'
          style={{
            fontSize: 18,
          }}
        >
          别灰心，再接再厉
        </Text>
      );
    }
  }, [getRankTextColor, getSuccessResultPrefixText, maxRank, result]);

  const renderGuluBall = useMemo(() => {
    if (guluNum && guluNum > 0) {
      return (
        <View className='my-[10px] flex flex-row items-center justify-center'>
          <Image
            source={Icon.guluBall}
            className='mr-[6px] h-[32px] w-[32px]'
          />
          <Text
            className='font-semibold text-[#E09400]'
            style={{
              fontSize: 24,
            }}
          >
            {`+${guluNum}`}
          </Text>
        </View>
      );
    } else {
      return null;
    }
  }, [guluNum]);

  const renderCenter = useMemo(() => {
    return (
      <View className='relative flex w-[316px] flex-col items-center px-[24px]'>
        {renderSceneLight}
        {renderResultHeroIcon}

        {renderButtonGroup}
      </View>
    );
  }, [renderButtonGroup, renderResultHeroIcon, renderSceneLight]);

  const renderRankEntry = useMemo(() => {
    if (!showRank) {
      return null;
    }
    let top = 0;
    const navbarHidden = navHeight === 0;
    if (navbarHidden) {
      const navBarHeight = 44;
      top = safeInsets.top + navBarHeight;
    }
    return (
      <View
        className='absolute right-0 top-0 flex flex-row items-center'
        style={{ top }}
        onPress={onClickRank}
      >
        <Image source={Icon.rankEntry} className='h-[67px] w-[53px]' />
      </View>
    );
  }, [navHeight, showRank, onClickRank, safeInsets.top]);

  if (visible) {
    return (
      <Modal
        isVisible={visible}
        coverScreen={false}
        animationIn={'fadeIn'}
        animationOut={'fadeOut'}
        className='flex flex-col items-center'
        customBackdrop={
          <BlurView
            style={{
              position: 'absolute',
              left: 0,
              bottom: 0,
              right: 0,
              top: 0,
            }}
            blurType='dark'
            blurAmount={25}
            // 解决Android机型 影响到上层容器问题 https://github.com/Kureev/react-native-blur/issues/595
            // overlayColor={'#00000000'}
            reducedTransparencyFallbackColor='black'
          />
        }
      >
        {renderRankEntry}
        {renderCenter}
      </Modal>
    );
  } else {
    return null;
  }
};
