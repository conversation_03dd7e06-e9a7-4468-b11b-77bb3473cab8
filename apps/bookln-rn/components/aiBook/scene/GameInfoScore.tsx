import { isNil } from 'lodash';
import { memo } from 'react';
import { Text, View } from 'tamagui';

type GameInfoScoreProps = {
  className?: string;

  /**
   * 分数 默认值：0, 值为undefined | null时不展示分数模块
   */
  score?: number | undefined | null;

  /**
   * “分数”文案, 默认值：分数
   */
  scoreLabel?: string;
  /**
   * "分数"的颜色，默认值：#fff
   */
  scoreLabelColor?: string;

  /**
   * 分数数字的颜色，默认值：#FFEC3D
   */
  scoreColor?: string;

  /**
   * 分数数字的背景色，默认值：rgba(255, 255, 255, 0.1)
   */
  scoreBackgroundColor?: string;

  /**
   * 分数数字 的边框颜色，默认值：#fff
   */
  scoreBorderColor?: string;
};

/**
 * 分数组件，如 分数：10
 */
export const GameInfoScore = memo((props: GameInfoScoreProps) => {
  const {
    score,
    scoreLabelColor = '#fff',
    scoreColor = '#FFEC3D',
    scoreBackgroundColor = 'rgba(255, 255, 255, 0.1)',
    scoreBorderColor = '#fff',
    scoreLabel = '分数',
  } = props;

  if (isNil(score)) return null;

  const scores = score.toString().split('');

  return (
    <View className='flex flex-row items-center justify-center'>
      <Text
        className='mr-1 text-base leading-6'
        fontWeight='bold'
        color={scoreLabelColor}
      >
        {scoreLabel}
      </Text>

      <View className='flex-row'>
        {scores.map((text, index) => {
          return (
            <View
              key={index}
              style={{
                backgroundColor: scoreBackgroundColor,
                borderColor: scoreBorderColor,
              }}
              className='ml-[2px] items-center overflow-hidden rounded border-[1px] border-solid px-[2px]'
            >
              <Text
                className='text-center last:mr-0'
                fontWeight='bold'
                width={14}
                fontSize={20}
                color={scoreColor}
              >
                {text}
              </Text>
            </View>
          );
        })}
      </View>
    </View>
  );
});
