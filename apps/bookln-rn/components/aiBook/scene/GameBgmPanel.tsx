import { useBgmPlay, useToggleAppVisible } from '@jgl/biz-func';
import Icon from '@jgl/icon';
import { audioSounds, useDidHide, useDidShow } from '@jgl/utils';
import { useMount } from 'ahooks';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Animated, Easing } from 'react-native';
import { View } from 'tamagui';

type Props = {
  audioSrc?: string;
  showSwitch?: boolean;
  icon?: string;
};

export const GameBgmPanel = memo((props: Props) => {
  const {
    audioSrc = audioSounds.maze_game_bgm,
    showSwitch = true,
    icon = Icon.music,
  } = props;
  const isShow = useRef<boolean>(false);

  const { bgmPlayer, play, unload } = useBgmPlay(audioSrc, {
    loop: isShow.current,
  });
  const [playingBgm, setPlayingBgm] = useState(true);

  const userSetOpen = useRef(true);

  const [rotate] = useState(new Animated.Value(0));

  const spinAnimation = useCallback(() => {
    if (!userSetOpen.current) {
      return;
    }
    Animated.loop(
      Animated.timing(rotate, {
        toValue: 1, // 下落到的位置
        duration: 4000, // 动画持续时间
        easing: Easing.linear,
        useNativeDriver: true, // 使用原生动画驱动，提高性能
      }),
    ).start();
  }, [rotate]);

  useMount(() => {
    spinAnimation();
  });

  useEffect(() => {
    if (playingBgm) {
      play?.();
    } else {
      bgmPlayer?.pause?.();
    }
  }, [playingBgm, bgmPlayer, play]);

  const pageShow = useCallback(() => {
    isShow.current = true;
    if (userSetOpen.current && !playingBgm) {
      setPlayingBgm(true);
    }
  }, [playingBgm]);

  const pageHide = useCallback(() => {
    playingBgm && setPlayingBgm(false);
    isShow.current = false;
  }, [playingBgm]);

  useToggleAppVisible({
    hide: pageHide,
    show: pageShow,
  });

  const handleClickBgm = useCallback(() => {
    setPlayingBgm(!playingBgm);
    userSetOpen.current = !playingBgm;

    if (userSetOpen.current) {
      spinAnimation();
    } else {
      rotate.stopAnimation();
      rotate.resetAnimation();
    }
  }, [playingBgm, rotate, spinAnimation]);

  useDidHide(() => {
    unload?.();
    pageHide();
  });

  useDidShow(() => {
    pageShow();
  });

  const spin = useMemo(() => {
    return rotate.interpolate({
      inputRange: [0, 1],
      outputRange: ['0deg', '360deg'],
    });
  }, [rotate]);

  return (
    <View>
      {showSwitch ? (
        <View onPress={handleClickBgm}>
          <Animated.Image
            style={{ transform: [{ rotate: spin }] }}
            source={{ uri: icon, width: 36, height: 36 }}
          />
        </View>
      ) : null}
    </View>
  );
});
