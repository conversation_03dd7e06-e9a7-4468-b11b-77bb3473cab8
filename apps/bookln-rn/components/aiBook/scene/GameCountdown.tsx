import Icon from '@jgl/icon';
import { memo } from 'react';
import { Image, Text, View } from 'tamagui';
import { getLimitSeconds } from '../../../utils/getLimitSeconds';

type GameCountdownProps = {
  countdown?: number;
  className?: string;
};

export const GameCountdown = memo((props: GameCountdownProps) => {
  const { countdown = 0, className = '' } = props;

  return (
    <View className={`flex-row items-center justify-center ${className}`}>
      <Image className='h-[20px] w-[20px]' source={{ uri: Icon.clock }} />
      <Text className='ml-[8px] text-[18px] font-medium tracking-widest text-white'>
        {getLimitSeconds(countdown)}
      </Text>
    </View>
  );
});
