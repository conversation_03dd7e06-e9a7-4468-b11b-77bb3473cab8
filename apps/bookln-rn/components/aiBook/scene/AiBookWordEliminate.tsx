import { useNavigationBarHeight, useSafeAreaInsets } from '@jgl/biz-func';
import { container } from '@jgl/container';
import Icon from '@jgl/icon';
import { JglView, JglXStack } from '@jgl/ui-v4';
import {
  audioSounds,
  useGenerateItemFlex,
  useWindowDimensions,
} from '@jgl/utils';
import classNames from 'classnames';
import { ImageBackground } from 'expo-image';
import { useCallback, useMemo } from 'react';
import * as Animatable from 'react-native-animatable';
import DropShadow from 'react-native-drop-shadow';
import { ScrollView, Text, View } from 'tamagui';
import { useAiBookEliminate } from '../../../hooks/aiBook/useAiBookEliminate';
import { useAiBookSceneData } from '../../../hooks/aiBook/useAiBookSceneData';
import type { SplitItemDTO } from '../../../utils/splitEliminateItem';
import { GameBgmPanel } from './GameBgmPanel';
import { GameCountdown } from './GameCountdown';
import { GameInfoScore } from './GameInfoScore';
import { SceneChallengeModal } from './SceneChallengeModal';

interface AiBookWordEliminateProps {
  onEnd: (score: number, alreadyPass?: boolean) => void;
  onExit: () => void;
}

type AnimationStyle = {
  active?: boolean;
  success?: boolean;
  error?: boolean;
};

const successAnimate = {
  0: {
    opacity: 1,
  },
  0.25: {
    opacity: 0,
  },
  0.5: {
    opacity: 1,
  },
  0.75: {
    opacity: 0,
  },
  0.99: {
    opacity: 1,
  },
  1: {
    opacity: 0,
  },
};

const errorAnimate = {
  0: {
    translateX: 0,
  },
  0.25: {
    translateX: -6,
  },
  0.75: {
    translateX: 6,
  },
  1: {
    translateX: 0,
  },
};

export const AiBookWordEliminate = (props: AiBookWordEliminateProps) => {
  const { onEnd, onExit } = props;
  const { wordList, refresh } = useAiBookSceneData();

  const {
    countdown,
    // challengeStatus,
    chosenItems,
    onChoseWordItem,
    setAnimating,
    setLongPressId,
    onAnimationEnd,
    finished,
    score,
    guluNum,
    setFinished,
    setChosenItems,
    wordsItems,
    challengeStatus,
  } = useAiBookEliminate({
    items: wordList,
    onEnd,
  });

  const { bottom } = useSafeAreaInsets();
  const { height: windowHeight } = useWindowDimensions();
  const navigationBarHeight = useNavigationBarHeight();

  const contentHeight = useMemo(() => {
    const headerHeight = 56;
    const countdownHeight = 30;
    return (
      windowHeight -
      headerHeight -
      countdownHeight -
      bottom -
      navigationBarHeight
    );
  }, [bottom, windowHeight, navigationBarHeight]);

  const getAnimate = useCallback((option: AnimationStyle) => {
    const { active, success, error } = option;
    if (!active) {
      return undefined;
    }
    if (success) {
      return successAnimate;
    }

    if (error) {
      return errorAnimate;
    }
  }, []);

  const style = useGenerateItemFlex({
    minColumn: 3,
    minWidth: 108,
    extraWidth: 52,
  });

  const onChose = useCallback(
    (item: SplitItemDTO) => {
      if (chosenItems.length === 1) {
        setAnimating(true);
      }
      onChoseWordItem(item);
    },
    [chosenItems, onChoseWordItem, setAnimating],
  );

  const onRefresh = useCallback(() => {
    setFinished(false);
    setChosenItems([]);
    refresh();
  }, [setFinished, setChosenItems, refresh]);

  const renderContent = useMemo(() => {
    // 结束后不显示这个内容, 要不然RN 上显示弹窗的效果很奇怪
    if (finished) return null;
    return (
      <>
        <DropShadow
          style={{
            shadowColor: '#0000004D',
            shadowOpacity: 1,
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowRadius: 15,
          }}
        >
          <JglXStack jc='center'>
            <JglView jglClassName='flex-center h-[30px] min-w-[115px] flex-row rounded-t-[12px] bg-[#907AE2] px-[16px]'>
              <GameCountdown countdown={Math.round(countdown / 1000)} />
            </JglView>
          </JglXStack>
        </DropShadow>

        <DropShadow
          style={{
            shadowColor: '#0000004D',
            shadowOpacity: 1,
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowRadius: 15,
          }}
        >
          <JglView
            jglClassName='mx-[16px] overflow-hidden rounded-[16px] border-2 border-solid border-[#C5B5FF] bg-[#AE9FE7]'
            style={{
              marginBottom: bottom,
              height: contentHeight,
            }}
          >
            <ScrollView className='h-full w-full'>
              <JglXStack flexWrap='wrap' px={8} py={16}>
                {wordsItems.map((item) => {
                  const {
                    text = '',
                    id = '',
                    disappear = false,
                    success = false,
                    error = false,
                    bgColor = '#D4ECFF',
                  } = item;
                  const isChosen = chosenItems.some((val) => val.id === id);

                  const animateOption = {
                    active: success || error,
                    success,
                    error,
                  };
                  const animate = getAnimate(animateOption);
                  const duration = success ? 1000 : 600;

                  let ImageBackgroundUrl = Icon.boxTopicBg;

                  // 答案颜色
                  if (bgColor !== '#D4ECFF') {
                    ImageBackgroundUrl = Icon.boxAnswerBg;
                  }

                  // 错误颜色
                  if (error) {
                    ImageBackgroundUrl = Icon.boxErrorBg;
                  }

                  return (
                    <View style={style} key={id} className='px-1'>
                      <Animatable.View
                        className={classNames('mb-2 rounded-lg', {
                          'opacity-0': disappear,
                        })}
                        animation={animate}
                        duration={duration}
                        onAnimationEnd={onAnimationEnd}
                      >
                        <ImageBackground
                          source={ImageBackgroundUrl}
                          className='overflow-hidden rounded-lg bg-cover text-center'
                          style={{
                            ...(isChosen && !error
                              ? {
                                  opacity: 0.3,
                                }
                              : {}),
                          }}
                        >
                          <View
                            className={classNames(
                              'h-[60px] rounded-lg p-2 text-center',
                            )}
                            onPress={() => onChose(item)}
                            onLongPress={() => setLongPressId(id)}
                          >
                            <View className='text-text max-h-[40px] flex-1 flex-row items-center justify-center'>
                              <Text numberOfLines={2}>{text ?? null}</Text>
                            </View>
                          </View>
                        </ImageBackground>
                      </Animatable.View>
                    </View>
                  );
                })}
              </JglXStack>
            </ScrollView>
          </JglView>
        </DropShadow>
      </>
    );
  }, [
    bottom,
    chosenItems,
    contentHeight,
    countdown,
    finished,
    getAnimate,
    onAnimationEnd,
    onChose,
    setLongPressId,
    style,
    wordsItems,
  ]);

  const renderModals = useMemo(() => {
    return (
      <SceneChallengeModal
        net={container.net()}
        visible={finished}
        result={challengeStatus}
        score={score}
        itemNum={wordList.length}
        onClickExitScene={onExit}
        guluNum={guluNum}
        successAudio={audioSounds.varyGood}
        onClickRestartScene={onRefresh}
      />
    );
  }, [
    finished,
    challengeStatus,
    score,
    wordList.length,
    onExit,
    guluNum,
    onRefresh,
  ]);

  return (
    <ImageBackground
      source={Icon.eliminateBg}
      className='flex h-full w-full flex-col bg-[#f5f5f5]'
    >
      <>
        <JglXStack h={56} ai='center' jc='space-between' px={12}>
          {/* <View className='flex flex-row items-center'>
            <RankEntry
              onClickRank={onClickRank}
              net={net}
              sceneCode={sceneCode}
              score={score}
              hiddenRankText
              iconSize={36}
              itemNum={items.length}
              appId={envVars.appId()}
              sceneChallengeModalVisible={finished}
            />
          </View> */}

          <GameInfoScore score={score} />

          <GameBgmPanel audioSrc={audioSounds.eliminate_bgm} />
        </JglXStack>
        {renderContent}
        {renderModals}
      </>
    </ImageBackground>
  );
};
