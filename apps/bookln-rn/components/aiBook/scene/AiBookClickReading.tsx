import { VoicePlay } from '@jgl/components';
import Icon from '@jgl/icon';
import {
  JglGridView,
  JglScrollView,
  JglText,
  JglTouchable,
  JglView,
} from '@jgl/ui-v4';
import classNames from 'classnames';
import { useCallback } from 'react';
import { useClickReading } from '../../../hooks/aiBook/useAiBookClickReading';
import { useAiBookSceneData } from '../../../hooks/aiBook/useAiBookSceneData';

const defaultCardItemMinWidth = 170;

/** 音频播放按钮位置 */
enum SoundPlayIconPosition {
  /* 左上 */
  TopLeft = 'TopLeft',
  /* 右上 */
  TopRight = 'TopRight',
  /* 右下 */
  BottomRight = 'BottomRight',
  /* 左下 */
  BottomLeft = 'BottomLeft',
}

/**
 * 单词点读-组件
 */
export const AiBookClickReading = () => {
  const { wordList } = useAiBookSceneData();
  const {
    realPlay,
    currentItem,
    renderMean,
    onClickItem: handleClickItem,
  } = useClickReading();

  /**
   * 获取播放 Icon 的样式
   */
  const getPlayIconStyle = useCallback((position: SoundPlayIconPosition) => {
    switch (position) {
      case SoundPlayIconPosition.TopLeft: {
        return {
          top: -3,
          left: -3,
        };
      }
      case SoundPlayIconPosition.TopRight: {
        return {
          top: -3,
          right: -3,
        };
      }
      case SoundPlayIconPosition.BottomRight: {
        return {
          bottom: -3,
          right: -3,
        };
      }
      case SoundPlayIconPosition.BottomLeft: {
        return {
          bottom: -3,
          left: -3,
        };
      }
    }
  }, []);

  return (
    <JglScrollView jglClassName='flex-1' px={16}>
      <JglGridView
        minColumns={2}
        minItemWidth={defaultCardItemMinWidth}
        gap={16}
        horizontalSpace={7 * 2 + 16 * 2}
      >
        {wordList.map((word) => {
          const active = currentItem.id === word.id;
          const subTitle = word.phAm;
          const playIconPosition = SoundPlayIconPosition.TopRight;
          const playIconStyle = getPlayIconStyle(playIconPosition);
          return (
            <JglTouchable
              key={word.name}
              jglClassName={classNames(
                'relative flex h-[88px] w-full flex-col items-center justify-center rounded-lg border border-solid border-transparent bg-white p-2 text-center shadow',
                { 'border-[#1677FF] !bg-[#E6F4FF]': active },
              )}
              flexDirection='column'
              onPress={() => handleClickItem(word)}
            >
              <JglView jglClassName='w-full overflow-hidden text-ellipsis whitespace-nowrap'>
                <JglText
                  jglClassName='text-center font-medium text-[#1f1f1f]'
                  fontSize={18}
                >
                  {word.name}
                </JglText>
              </JglView>
              {subTitle ? (
                <JglView jglClassName='w-full overflow-hidden text-ellipsis whitespace-nowrap text-center'>
                  <JglText
                    jglClassName='text-center text-[#595959]'
                    fontSize={14}
                    maxLines={1}
                  >
                    {subTitle}
                  </JglText>
                </JglView>
              ) : null}
              <JglView jglClassName='w-full overflow-hidden text-ellipsis whitespace-nowrap'>
                <JglText
                  jglClassName='text-center text-[#8c8c8c]'
                  fontSize={12}
                  style={{
                    transform: [{ scale: 0.8 }],
                  }}
                  maxLines={1}
                >
                  {renderMean(word.content ?? '')}
                </JglText>
              </JglView>
              {active ? (
                <JglView jglClassName='absolute' style={playIconStyle}>
                  <VoicePlay
                    icon={Icon.voiceQueryIcon}
                    size={[24, 24]}
                    className='icon'
                    play={realPlay}
                  />
                </JglView>
              ) : null}
            </JglTouchable>
          );
        })}
      </JglGridView>
    </JglScrollView>
  );
};
