import {
  JglSafeArea,
  JglStateView,
  JglText,
  JglView,
  JglXStack,
} from '@jgl/ui-v4';
import { useCallback } from 'react';
import { FlatList } from 'react-native';
import type { AiBookPage, AiBookTopicItem } from '../../hooks/aiBook';
import { AiTopicType, useAiBookPage } from '../../hooks/aiBook';
import { AiBookTopicDetail } from './AiBookTopicDetail';
import { AiBookBottomToolbar } from './common/AiBookBottomToolbar';
import { AiBookNavBar } from './common/AiBookNavBar';
import { AiBookZoomableImage } from './common/AiBookZoomableImage';
import { AiBookTopicCatalogSection } from './topic/AiBookTopicCatalogSection';

export const AiBookPageDetail = () => {
  const {
    isLoading,
    book,
    handleClickTopic,
    handleScroll,
    cellWidth,
    pageTableRef,
    flatListScrollEnabled,
    setFlatListScrollEnabled,
    handleScrollToNextItem,
    handleScrollToPrevItem,
    currentTopic,
    currentPageIndex,
    currentPageId,
  } = useAiBookPage();

  const handleZoomActiveChange = useCallback(
    (isActive: boolean) => {
      setFlatListScrollEnabled(!isActive);
    },
    [setFlatListScrollEnabled],
  );

  const renderContent = () => {
    const isFullPage = !currentTopic;
    if (isFullPage) {
      return renderFullPages();
    }
    return <AiBookTopicDetail />;
  };

  const renderFullPages = () => {
    return (
      <FlatList
        data={book?.pages || []}
        renderItem={renderPageCell}
        keyExtractor={(item) => item.pageId?.toString() ?? ''}
        horizontal
        pagingEnabled
        initialScrollIndex={currentPageIndex}
        onMomentumScrollEnd={handleScroll}
        showsHorizontalScrollIndicator={false}
        ref={pageTableRef}
        windowSize={3}
        initialNumToRender={3}
        scrollEnabled={flatListScrollEnabled}
        getItemLayout={(data, index) => ({
          length: cellWidth,
          offset: cellWidth * index,
          index,
        })}
      />
    );
  };

  const renderContentInRect = useCallback((item: AiBookTopicItem) => {
    const renderTag = (text: string) => {
      return (
        <JglView backgroundColor='$blue5' p='$1' borderRadius={4}>
          <JglText fontSize={14}>{text}</JglText>
        </JglView>
      );
    };

    const renderListen = () => {
      if (item.type === AiTopicType.Listen) {
        return renderTag('听力');
      } else {
        return null;
      }
    };

    const renderVideo = () => {
      if (item.videoUrl) {
        return renderTag('视频讲解');
      } else if (item.answerAnalysis) {
        const { markdown, ttsUrls = [], srtUrls = [] } = item.answerAnalysis;
        const markdowns = markdown?.split('\n\n') || [];
        const valid =
          markdowns.length === ttsUrls.length &&
          ttsUrls.length === srtUrls.length;
        if (valid) {
          return renderTag('AI讲解');
        } else {
          return null;
        }
      } else {
        return null;
      }
    };

    return (
      <JglView position='absolute' top={0} right={0}>
        <JglXStack spaceX={'$2'}>
          {renderListen()}
          {renderVideo()}
        </JglXStack>
      </JglView>
    );
  }, []);

  const renderPageCell = ({ item }: { item: AiBookPage }) => {
    return (
      <AiBookZoomableImage
        rects={item.topics || []}
        extractId={(value) => value.itemId?.toString() ?? ''}
        onPress={handleClickTopic}
        showRects={true}
        enableZoom={true}
        imageUrl={item.pageUrl || ''}
        cellWidth={cellWidth}
        currentItem={undefined}
        onScaleActiveChange={handleZoomActiveChange}
        onScrollToNextItem={handleScrollToNextItem}
        onScrollToPrevItem={handleScrollToPrevItem}
        visible={item.pageId === currentPageId}
        renderContentInRect={renderContentInRect}
      />
    );
  };

  return (
    <JglSafeArea jglClassName='relative'>
      <AiBookNavBar />
      <JglStateView
        flex={1}
        isLoading={isLoading}
        isEmpty={(book?.pages?.length || 0) <= 0}
      >
        <JglView flex={1}>
          <AiBookTopicCatalogSection />
          {renderContent()}
        </JglView>
        <AiBookBottomToolbar />
      </JglStateView>
    </JglSafeArea>
  );
};
