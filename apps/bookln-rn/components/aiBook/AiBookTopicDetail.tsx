import {
  JglS<PERSON><PERSON>View,
  Jgl<PERSON><PERSON><PERSON>,
  Jgl<PERSON>ouchable,
  JglView,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { useCallback, useRef, useState } from 'react';
import {
  FlatList,
  InteractionManager,
  type LayoutChangeEvent,
  ScrollView,
} from 'react-native';
// import { ScrollView } from 'tamagui';
import { showToast } from '@yunti-private/jgl-ui';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import { Star } from 'lucide-react-native';
import { View } from 'tamagui';
import { atomMap } from '../../atom';
import type { AiBookTopicItem, AiBookWordItem } from '../../hooks/aiBook';
import { AiTopicType, useAiBookTopic } from '../../hooks/aiBook';
import { AiBookZoomableImage } from './common/AiBookZoomableImage';
import { EmptyListFooter } from './common/EmptyListFooter';
import { AiBookClickReading } from './scene/AiBookClickReading';
import { AiBookTopicListenSection } from './topic/AiBookTopicListenSection';
import { AiBookTopicPhotoCorrectSection } from './topic/AiBookTopicPhotoCorrectSection';
import { AiBookTopicPPTSection } from './topic/AiBookTopicPPTSection';
import { AiBookTopicVideoSection } from './topic/AiBookTopicVideoSection';

export const AiBookTopicDetail = () => {
  const {
    allTopics,
    handleScroll,
    cellWidth,
    topicTableRef,
    currentTopicIndex,
    showWordRecognition,
    handlePressWord,
    currentWord,
    handleScrollToNextItem,
    handleScrollToPrevItem,
    topicTableScrollEnabled,
    setTopicTableScrollEnabled,
  } = useAiBookTopic();

  const scrollViewRef = useRef<ScrollView>(null);
  const setTopicDetailHeight = useSetAtom(atomMap.aiBookTopicDetailHeightAtom);
  const { answerResults, showResultPages } = useAtomValue(
    atomMap.aiBookTakePhotoResult,
  );
  const [aiBookMockData, setAiBookMockData] = useAtom(atomMap.aiBookMockData);
  const [showTakePhotoResult, setShowTakePhotoResult] = useState(true);

  const handleZoomActiveChange = useCallback(
    (active: boolean) => {
      setTopicTableScrollEnabled(!active);
    },
    [setTopicTableScrollEnabled],
  );

  const renderTopics = () => {
    return (
      <FlatList
        data={allTopics}
        renderItem={renderTopic}
        keyExtractor={(item) => item.itemId?.toString() ?? ''}
        horizontal
        pagingEnabled
        onMomentumScrollEnd={handleScroll}
        initialScrollIndex={currentTopicIndex}
        showsHorizontalScrollIndicator={false}
        windowSize={3}
        initialNumToRender={3}
        scrollEnabled={topicTableScrollEnabled}
        style={{ flex: 1 }}
        getItemLayout={(data, index) => ({
          length: cellWidth,
          offset: cellWidth * index,
          index,
        })}
        ref={topicTableRef}
      />
    );
  };

  const renderImageType = (item: AiBookTopicItem) => {
    const currentAnswerResult = answerResults.find(
      (result) =>
        result.pageId === item.pageId && result.itemId === item.itemId,
    );

    // 有拍照批改结果
    const hasTakePhotoResult = !!currentAnswerResult;
    // 并且已经拍照了
    const hadTakePhoto = showResultPages.has(item.pageId || 0);

    if (!hasTakePhotoResult || !hadTakePhoto) return null;
    return (
      <JglTouchable
        onPress={() => setShowTakePhotoResult(!showTakePhotoResult)}
      >
        <JglText>{showTakePhotoResult ? '原' : '答'}</JglText>
      </JglTouchable>
    );
  };

  const handleCollect = (item: AiBookTopicItem, hasCollected: boolean) => {
    if (hasCollected) {
      showToast({ title: '已取消收藏' });
      setAiBookMockData((prev) => {
        return {
          ...prev,
          collectionTopics: prev.collectionTopics.filter(
            (topic) => topic.itemId !== item.itemId,
          ),
        };
      });
    } else {
      showToast({ title: '已收藏' });
      setAiBookMockData((prev) => {
        return {
          ...prev,
          collectionTopics: [...prev.collectionTopics, item],
        };
      });
    }
  };

  const renderImage = (item: AiBookTopicItem) => {
    const currentAnswerResult = answerResults.find(
      (result) =>
        result.pageId === item.pageId && result.itemId === item.itemId,
    );

    // 有拍照批改结果
    const hasTakePhotoResult = !!currentAnswerResult;
    // 并且已经拍照了
    const hadTakePhoto = showResultPages.has(item.pageId || 0);
    // 用户点击了切换
    const showResult =
      showTakePhotoResult && hasTakePhotoResult && hadTakePhoto;

    const hasCollected = !!aiBookMockData.collectionTopics.find(
      (topic) => topic.itemId === item.itemId,
    );

    return (
      <JglView position='relative'>
        {!showResult ? (
          <AiBookZoomableImage<AiBookWordItem>
            imageUrl={item.itemUrl ?? ''}
            cellWidth={cellWidth}
            rects={item.words || []}
            onScaleActiveChange={handleZoomActiveChange}
            enableZoom={false}
            currentItem={currentWord}
            extractId={(value) => value.id ?? ''}
            showRects={showWordRecognition}
            onPress={handlePressWord}
            onScrollToNextItem={handleScrollToNextItem}
            onScrollToPrevItem={handleScrollToPrevItem}
            maxHeight={300}
            visible={true}
          />
        ) : (
          <AiBookTopicPhotoCorrectSection />
        )}

        <JglXStack
          position='absolute'
          top={0}
          right={0}
          padding={'$0.25'}
          bg='$gray4'
          borderRadius={6}
        >
          {renderImageType(item)}
          <JglTouchable onPress={() => handleCollect(item, hasCollected)}>
            <Star
              size={16}
              color='black'
              fill={hasCollected ? 'black' : 'none'}
            />
          </JglTouchable>
        </JglXStack>
      </JglView>
    );
  };

  const renderWordReadItem = (item: AiBookTopicItem) => {
    if (item.type === AiTopicType.Word) {
      return (
        <JglYStack py={'$0.5'}>
          <JglText paddingVertical='$0.5' fontSize={16} fontWeight='600'>
            单词学习
          </JglText>
          <AiBookClickReading />
        </JglYStack>
      );
    }
    return null;
  };

  const handleScrollToPPTSection = useCallback((y: number) => {
    InteractionManager.runAfterInteractions(() => {
      scrollViewRef.current?.scrollTo({ x: 0, y, animated: true });
    });
  }, []);

  const renderTopic = ({ item }: { item: AiBookTopicItem }) => {
    return (
      <View style={{ flex: 1 }}>
        <JglScrollView
          ref={scrollViewRef}
          style={{
            width: cellWidth,
            flex: 1,
          }}
        >
          <JglView borderWidth={1} flex={1} borderColor='$gray5'>
            {renderImage(item)}
          </JglView>
          {/* <TouchableWithoutFeedback
            onPressIn={() => handleZoomActiveChange(false)}
          > */}
          <JglView flex={1} paddingHorizontal={10}>
            {renderWordReadItem(item)}
            <AiBookTopicListenSection audioUrl={item.audioUrl} topic={item} />
            <AiBookTopicVideoSection videoUrl={item.videoUrl} topic={item} />
            {item.answerAnalysis && !item.videoUrl ? (
              <AiBookTopicPPTSection
                topic={item}
                onScrollToPPTSection={handleScrollToPPTSection}
              />
            ) : null}
            <EmptyListFooter />
            {/* <AiBookTopicButtonSection
              handleHint={handleHint}
              showWordRecognition={showWordRecognition}
              handleWordRecognition={handleWordRecognition}
            /> */}
          </JglView>
          {/* </TouchableWithoutFeedback> */}
        </JglScrollView>
      </View>
    );
  };

  const handleContainerLayout = useCallback(
    (event: LayoutChangeEvent) => {
      const { height } = event.nativeEvent.layout;
      setTopicDetailHeight(height);
    },
    [setTopicDetailHeight],
  );

  return (
    <JglView flex={1} onLayout={handleContainerLayout}>
      {renderTopics()}
      {/* <AiBookTopicWordSheet
        setCurrentWord={setCurrentWord}
        word={currentWord}
      /> */}
    </JglView>
  );
};
