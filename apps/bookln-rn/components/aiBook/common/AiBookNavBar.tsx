import { JglText, JglTouchable, JglXStack, JglYStack } from '@jgl/ui-v4';
import { router } from '@jgl/utils';
import { useAtom, useAtomValue } from 'jotai';
import { <PERSON>Lef<PERSON>, Ellipsis, Menu } from 'lucide-react-native';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { FlatList, type LayoutChangeEvent } from 'react-native';
import { atomMap } from '../../../atom';
import type { AiBookPage } from '../../../hooks/aiBook';
import type { AiBookCatalogRef } from './AiBookCatalog';
import { AiBookCatalog } from './AiBookCatalog';
import {
  AiBookDropDownMenu,
  type AiBookDropDownMenuRef,
} from './AiBookDropDownMenu';
import { useSafeAreaInsets } from '@jgl/biz-func';
import { Ai<PERSON>ookSummaryList, AiBookSummaryListRef } from './AiBookSummaryList';

type Props = {
  onlyRenderBack?: boolean;
};

const cellWidth = 44;
export const AiBookNavBar = (props: Props) => {
  const { onlyRenderBack } = props;
  const catalogRef = useRef<AiBookCatalogRef>(null);
  const [aiBookInfo, setAiBookInfo] = useAtom(atomMap.aiBookInfo);
  const { pageId } = aiBookInfo;
  const book = useAtomValue(atomMap.aiBook);
  const dropDownMenuRef = useRef<AiBookDropDownMenuRef>(null);
  const [containerHeight, setContainerHeight] = useState(0);
  const safeAreaInsets = useSafeAreaInsets();
  const summaryListRef = useRef<AiBookSummaryListRef>(null);

  const catalogTableRef = useRef<FlatList>(null);
  const tableViewIndexRef = useRef(0);

  const currentPageIndex = useMemo(() => {
    const index = book?.pages?.findIndex((item) => item.pageId === pageId);
    if (index === undefined || index === -1) {
      return 0;
    }
    return index;
  }, [book?.pages, pageId]);

  const handleBack = useCallback(() => {
    router.back();
  }, []);

  useEffect(() => {
    if (
      catalogTableRef.current &&
      tableViewIndexRef.current !== currentPageIndex &&
      book?.pages?.length
    ) {
      tableViewIndexRef.current = currentPageIndex;
      catalogTableRef.current.scrollToIndex({
        index: currentPageIndex,
        viewPosition: 0.5,
        animated: false,
      });
    }
  }, [book, book?.pages, currentPageIndex]);

  const renderCell = ({ item }: { item: AiBookPage }) => {
    return (
      <JglTouchable
        onPress={() => {
          setAiBookInfo((prev) => {
            return {
              ...prev,
              pageId: item.pageId,
              topicId: undefined,
            };
          });
        }}
        style={{ minWidth: 0, minHeight: 0 }}
      >
        <JglText
          color={item.pageId === pageId ? '$gray12' : '$gray8'}
          fontWeight='bold'
          flexShrink={1}
          fontSize={16}
          w={cellWidth}
        >
          P{item.pageNo}
        </JglText>
      </JglTouchable>
    );
  };

  const renderNavContent = () => {
    if (onlyRenderBack) {
      return null;
    }
    return (
      <JglXStack flex={1}>
        <JglTouchable
          onPress={() => {
            dropDownMenuRef.current?.closeMenu();
            catalogRef.current?.toggleMenu();
          }}
        >
          <Menu size={24} color='#171717' />
        </JglTouchable>

        <JglXStack space={8} px={'$2'} flex={1} ai='center'>
          <FlatList
            data={book?.pages || []}
            renderItem={renderCell}
            keyExtractor={(item) => item.pageId?.toString() ?? ''}
            horizontal
            showsHorizontalScrollIndicator={false}
            ref={catalogTableRef}
            getItemLayout={(data, index) => ({
              length: cellWidth,
              offset: cellWidth * index,
              index,
            })}
            initialScrollIndex={currentPageIndex}
          />
          <JglTouchable onPress={() => dropDownMenuRef.current?.toggleMenu()}>
            <Ellipsis size={24} color='#171717' />
          </JglTouchable>
        </JglXStack>
      </JglXStack>
    );
  };

  const handleContainerLayout = useCallback((event: LayoutChangeEvent) => {
    const height = event.nativeEvent.layout.height;
    setContainerHeight(height);
  }, []);

  return (
    <>
      <JglXStack paddingHorizontal={'$0.5'} onLayout={handleContainerLayout}>
        <JglXStack jglClassName='items-center' flex={1}>
          <JglTouchable onPress={handleBack} mr='$0.5'>
            <ArrowLeft size={24} color='#171717' />
          </JglTouchable>
          {renderNavContent()}
        </JglXStack>
      </JglXStack>
      <AiBookDropDownMenu
        ref={dropDownMenuRef}
        topOffset={containerHeight + safeAreaInsets.top}
      >
        <AiBookSummaryList
          ref={summaryListRef}
          onCloseMenu={() => dropDownMenuRef.current?.closeMenu()}
        />
      </AiBookDropDownMenu>
      {!onlyRenderBack && <AiBookCatalog ref={catalogRef} />}
    </>
  );
};
