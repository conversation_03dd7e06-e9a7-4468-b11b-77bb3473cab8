import { useSafeAreaInsets } from '@jgl/biz-func';
import {
  JglImage,
  JglStateView,
  JglText,
  JglTouchable,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { useWindowDimensions } from '@jgl/utils';
import { isPlatform } from '@yunti-private/platform-check';
import { useAtom, useAtomValue } from 'jotai';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  FlatList,
  StyleSheet,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {
  type GestureEvent,
  PanGestureHandler,
  type PanGestureHandlerEventPayload,
  State,
} from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import { atomMap } from '../../../atom';
import { type AiBookPage, AiBookTopicItem } from '../../../hooks/aiBook';
import { EmptyListFooter } from './EmptyListFooter';

const menuWidthRatio = 0.85;
const deltaWidth = 0;

export type AiBookCatalogRef = {
  openMenu: () => void;
  closeMenu: () => void;
  toggleMenu: () => void;
};

/**
 * 右侧滑出侧边栏菜单组件
 * - 支持手势右滑关闭
 * - 遮罩层点击关闭
 * - 动画与遮罩透明度联动
 */

export const AiBookCatalog = forwardRef<AiBookCatalogRef>((props, ref) => {
  const [aiBookInfo, setAiBookInfo] = useAtom(atomMap.aiBookInfo);
  const book = useAtomValue(atomMap.aiBook);
  const { pageId, topicId } = aiBookInfo;

  const safeInsets = useSafeAreaInsets();
  const navigationBarHeight = safeInsets.top;

  // 获取屏幕尺寸，适配横竖屏
  const windowSize = useWindowDimensions();
  const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = windowSize;
  const flattenListData = useMemo(() => {
    return (
      book?.pages?.flatMap((value) => {
        return [
          {
            type: 'page',
            value,
          },
          ...(value.topics || []).map((v) => ({
            type: 'topic',
            value: v,
          })),
        ];
      }) || []
    );
  }, [book?.pages]);

  const initX = -2 * SCREEN_WIDTH;
  const lastScreenWidthRef = useRef(initX);

  // 菜单宽度自适应屏幕
  const sideMenuWidth = useMemo(() => {
    const resultWidth = SCREEN_WIDTH * menuWidthRatio;
    return resultWidth;
  }, [SCREEN_WIDTH]);

  // 动画相关状态
  const translateX = useSharedValue(initX); // 菜单初始隐藏在左侧
  const isMenuOpen = useSharedValue(0); // 动画层菜单开关
  const [isOpen, setIsOpen] = useState(false); // React 层菜单开关（用于遮罩渲染）

  // 菜单滑动动画样式
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  // 遮罩层动画样式，透明度与菜单滑动联动
  const maskAnimatedStyle = useAnimatedStyle(() => {
    const opacity = ((-initX + translateX.value) / sideMenuWidth) * 0.5;
    return {
      opacity: Math.max(0, Math.min(0.4, opacity)),
      backgroundColor: '#000000',
      position: 'absolute',
      top: 0,
      left: 0,
      width: SCREEN_WIDTH,
      bottom: 0,
      zIndex: 11,
    };
  });

  // 暴露控制方法给父组件
  useImperativeHandle(ref, () => ({
    openMenu,
    closeMenu,
    toggleMenu,
  }));

  /** 打开菜单（动画） */
  const openMenu = useCallback(() => {
    const newTranslateX = deltaWidth;
    translateX.value = withSpring(newTranslateX, {
      stiffness: 100,
      damping: 20,
      mass: 0.25,
    });
    isMenuOpen.value = 1;
    setIsOpen(true);
  }, [isMenuOpen, translateX]);

  /** 关闭菜单（动画） */
  const closeMenu = useCallback(() => {
    const newTranslateX = initX;
    translateX.value = withSpring(newTranslateX, {
      stiffness: 100,
      damping: 20,
      mass: 0.25,
    });
    isMenuOpen.value = 0;
    setIsOpen(false);
  }, [initX, translateX, isMenuOpen]);

  useEffect(() => {
    if (lastScreenWidthRef.current !== initX) {
      lastScreenWidthRef.current = initX;
      closeMenu();
    }
  }, [initX, closeMenu]);

  useEffect(() => {
    if (isOpen) {
      listRef.current?.scrollToIndex({
        index: flattenListData.findIndex(
          (item) => item.type === 'page' && item.value?.pageId === pageId,
        ),
        animated: true,
      });
    }
  }, [flattenListData, isOpen, pageId]);

  /** 切换菜单开关 */
  const toggleMenu = useCallback(() => {
    if (isMenuOpen.value === 1) {
      closeMenu();
    } else {
      openMenu();
    }
  }, [closeMenu, isMenuOpen.value, openMenu]);

  const handleClickPage = useCallback(
    (param: { page?: AiBookPage; topic?: AiBookTopicItem }) => {
      const { page, topic } = param || {};
      if (page) {
        setAiBookInfo((prev) => {
          return {
            ...prev,
            pageId: page.pageId,
            topicId: undefined,
          };
        });
      } else if (topic) {
        setAiBookInfo((prev) => {
          return {
            ...prev,
            pageId: topic.pageId,
            topicId: topic.itemId,
          };
        });
      }

      closeMenu();
    },
    [closeMenu, setAiBookInfo],
  );

  /**
   * 手势滑动菜单
   * - 以菜单展开位置为基准，累加 translationX
   * - 限制滑动范围在 [展开, 隐藏] 区间
   */
  const handlePan = useCallback(
    (event: GestureEvent<PanGestureHandlerEventPayload>) => {
      'worklet';
      const menuStartX = 0;
      const newX = menuStartX + event.nativeEvent.translationX;
      translateX.value = Math.min(0, Math.max(newX, initX));
    },
    [initX, translateX],
  );

  /**
   * 手势结束时判断菜单收回或展开
   * - 超过一半则收回，否则保持展开
   * - 同步动画层和 React 层状态
   */
  const handleStateChange = useCallback(
    (state: State) => {
      'worklet';
      if (state === State.END) {
        if (translateX.value > -sideMenuWidth / 2) {
          translateX.value = withSpring(0, {
            stiffness: 100,
            damping: 20,
          });
          runOnJS(() => {
            isMenuOpen.value = 1;
            setIsOpen(true);
          })();
        } else {
          translateX.value = withSpring(initX, {
            stiffness: 100,
            damping: 20,
          });
          runOnJS(() => {
            isMenuOpen.value = 0;
            setIsOpen(false);
          })();
        }
      }
    },
    [isMenuOpen, initX, sideMenuWidth, translateX],
  );

  const listRef = useRef<FlatList>(null);

  const renderPageItem = (page: AiBookPage) => {
    const isCurrentPage = pageId === page.pageId && !topicId;

    return (
      <JglTouchable onPress={() => handleClickPage({ page })} flex={1}>
        <JglXStack justifyContent='space-between' flex={1}>
          <JglText
            flex={1}
            fontSize={16}
            lineHeight={24}
            flexWrap='wrap'
            color={isCurrentPage ? '$color12' : '$color10'}
          >
            {page.catalogName}
          </JglText>
          <JglText
            color={isCurrentPage ? '$color12' : '$color10'}
            fontSize={16}
            lineHeight={24}
          >
            {page.pageNo}
          </JglText>
        </JglXStack>
      </JglTouchable>
    );
  };

  const renderTopicItem = (topic: AiBookTopicItem, pageNo: number) => {
    const isCurrentTopic = topicId === topic.itemId && pageId === topic.pageId;

    return (
      <JglTouchable
        key={topic.itemId}
        onPress={() => handleClickPage({ topic })}
      >
        <JglXStack justifyContent='space-between' flex={1}>
          <JglText
            color={isCurrentTopic ? '$color12' : '$color10'}
            flexWrap='wrap'
            flexShrink={1}
            fontWeight={isCurrentTopic ? 'bold' : 'normal'}
            pl={'$4'}
          >
            {topic.title?.name}
          </JglText>
          <JglText color={isCurrentTopic ? '$color12' : '$color10'}>
            {pageNo}
          </JglText>
        </JglXStack>
      </JglTouchable>
    );
  };

  const renderCatalogList = () => {
    return (
      <FlatList
        ref={listRef}
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        data={flattenListData}
        keyExtractor={(item) => {
          const { type, value } = item;
          return type === 'page'
            ? `page-${value?.pageId}`
            : `topic-${value?.pageId}-${value?.itemId}`;
        }}
        ListFooterComponent={<EmptyListFooter />}
        windowSize={2}
        initialNumToRender={1}
        onScrollToIndexFailed={(info) => {}}
        renderItem={({ item }) => {
          const { type, value } = item;
          if (type === 'page') {
            return renderPageItem(value as AiBookPage);
          } else if (type === 'topic') {
            return renderTopicItem(value as AiBookTopicItem, value.pageNo);
          } else {
            return null;
          }
        }}
      />
    );
  };

  const renderCatalog = () => {
    return (
      <JglYStack flex={1}>
        <JglXStack h={68} alignItems='center'>
          <JglImage
            source={require('../../../assets/images/aibook_cover.png')}
            h={47}
            w={34}
          />
          <JglYStack ml={'$2'}>
            <JglText fontSize={14} marginVertical={'$0.25'}>
              拔尖新方案
            </JglText>
            <JglText fontSize={14} marginVertical={'$0.25'}>
              浙江教育出版社
            </JglText>
          </JglYStack>
        </JglXStack>
        {renderCatalogList()}
      </JglYStack>
    );
  };

  return (
    <>
      {/* 遮罩层，菜单打开时显示，点击关闭 */}
      <Animated.View
        style={maskAnimatedStyle}
        pointerEvents={isOpen ? 'auto' : 'none'}
      >
        {isOpen && (
          <TouchableWithoutFeedback onPress={closeMenu}>
            <View style={{ flex: 1 }} />
          </TouchableWithoutFeedback>
        )}
      </Animated.View>
      {/* 侧边栏菜单，支持手势右滑关闭 */}
      <PanGestureHandler
        // simultaneousHandlers={scrollViewRef}
        onGestureEvent={handlePan}
        onHandlerStateChange={({ nativeEvent }) =>
          handleStateChange(nativeEvent.state)
        }
      >
        <Animated.View
          style={[
            styles.sideMenuContainer,
            {
              top: 0,
              width: sideMenuWidth,
              height:
                SCREEN_HEIGHT +
                (isPlatform({ os: 'android' })
                  ? safeInsets.top * 2
                  : safeInsets.top),
            },
            animatedStyle,
            { zIndex: 12 },
          ]}
        >
          <JglYStack flex={1} pt={navigationBarHeight}>
            <JglStateView
              width={sideMenuWidth}
              px={16}
              isEmpty={book?.pages?.length === 0}
            >
              {renderCatalog()}
            </JglStateView>
          </JglYStack>
        </Animated.View>
      </PanGestureHandler>
    </>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sideMenuContainer: {
    position: 'absolute',
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  menuContent: {
    flex: 1,
  },
});
