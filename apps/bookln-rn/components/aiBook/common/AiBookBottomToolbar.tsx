import { useBookAICommonQuestions, useBookAISendMessage } from '@jgl/ai-qa-v2';
import { JglButton, JglScrollView, JglView, JglXStack } from '@jgl/ui-v4';
import { showToast } from '@yunti-private/jgl-ui';
import { useAtomValue } from 'jotai';
import { useMemo } from 'react';
import { atomMap } from '../../../atom';
import { AiTopicType, useBookActions } from '../../../hooks/aiBook';

type Props = {
  paddingHorizontal?: number;
};

export const AiBookBottomToolbar = (props: Props) => {
  const { paddingHorizontal } = props;
  const { handleOpenBookAICommonQuestions } = useBookAICommonQuestions();
  const { topicId, pageId } = useAtomValue(atomMap.aiBookInfo);
  const book = useAtomValue(atomMap.aiBook);
  const currentTopic = useMemo(() => {
    if (!topicId || !pageId) {
      return null;
    }
    const currentPage = book?.pages?.find((page) => page.pageId === pageId);
    return currentPage?.topics?.find((topic) => topic.itemId === topicId);
  }, [book, topicId, pageId]);

  const { takePhotoAction, bookResourceAction, wordEliminateAction } =
    useBookActions();

  const { sendMessage } = useBookAISendMessage();

  const topic = useMemo(() => {
    const takePhotoItem = {
      key: 'takePhoto',
      title: '拍照批改',
      onPress: async () => {
        await takePhotoAction();
      },
    };
    if (!topicId) {
      return [
        takePhotoItem,
        // {
        //   key: 'bookResource',
        //   title: '图书资源',
        //   onPress: bookResourceAction,
        // },
        {
          key: 'clickRead',
          title: '扩展:点读书',
          onPress: () => {
            showToast({
              title: '点读书',
            });
          },
        },
      ];
    } else if (currentTopic?.type === AiTopicType.Word) {
      return [
        takePhotoItem,

        {
          key: 'wordMatch',
          title: '单词消消乐',
          onPress: wordEliminateAction,
        },

        // {
        //   key: 'bookResource',
        //   title: '图书资源',
        //   onPress: bookResourceAction,
        // },
        {
          key: 'pronunciation',
          title: '扩展:发音练习',
          onPress: () => {
            showToast({
              title: '发音练习',
            });
          },
        },
        {
          key: 'wordListen',
          title: '扩展:单词听写',
          onPress: () => {
            showToast({
              title: '单词听写',
            });
          },
        },
      ];
    } else {
      return [
        takePhotoItem,
        {
          key: 'solution',
          title: '解题思路',
          onPress: () => {
            sendMessage({
              textContent: '解题思路',
              bizData: JSON.stringify({
                bookId: book?.bookId,
                pageId: pageId,
                itemId: topicId,
                rewriteUserMsg: true,
                bizType: 'book',
              }),
            });
          },
        },
        {
          key: 'commonQuestion',
          title: '大家都在问',
          onPress: () => {
            handleOpenBookAICommonQuestions();
          },
        },
        {
          key: 'translation',
          title: '扩展:翻译',
          onPress: () => {
            showToast({
              title: '翻译',
            });
          },
        },
      ];
    }
  }, [
    book?.bookId,
    bookResourceAction,
    currentTopic?.type,
    handleOpenBookAICommonQuestions,
    pageId,
    sendMessage,
    takePhotoAction,
    topicId,
    wordEliminateAction,
  ]);

  return (
    <JglView paddingHorizontal={paddingHorizontal || '$1'} marginTop={'$2'}>
      <JglScrollView horizontal height={40}>
        <JglXStack flexDirection='row' spaceX={'$2.5'}>
          {topic.map((item) => (
            <JglButton
              size={'small'}
              color='$gray5'
              textColor='$gray12'
              fontWeight='100'
              key={item.key}
              onPress={item.onPress}
            >
              {item.title}
            </JglButton>
          ))}
        </JglXStack>
      </JglScrollView>
    </JglView>
  );
};
