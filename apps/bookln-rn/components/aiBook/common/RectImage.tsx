import { JglImage, JglView } from '@jgl/ui-v4';
import { useState } from 'react';
import { useImageRenderSize } from '../../../hooks/aiBook';

type Props = {
  imageUrl: string;
  rect: Partial<{
    width: number;
    height: number;
    left: number;
    top: number;
  }>;
  contentOverImage?: React.ReactNode;
  maxHeight?: number;
};
export const RectImage = ({
  imageUrl,
  rect,
  contentOverImage,
  maxHeight,
}: Props) => {
  const { imgSize, calculateImageSize } = useImageRenderSize({
    url: imageUrl,
  });

  const [loaded, setLoaded] = useState(false);

  return (
    <JglView
      flex={1}
      width='100%'
      // @ts-ignore
      onLayout={(e: LayoutChangeEvent) => {
        if (e.nativeEvent) {
          calculateImageSize({
            width: e.nativeEvent?.layout?.width,
            height: maxHeight ?? Number.POSITIVE_INFINITY,
          });
        }
      }}
    >
      <JglView
        width={(rect?.width || 0) * imgSize.width || '100%'}
        height={(rect?.height || 0) * imgSize.height || '100%'}
        position='relative'
        overflow='hidden'
      >
        <JglView
          position='relative'
          width={imgSize.width || '100%'}
          height={imgSize.height || '100%'}
          style={{
            transform: [
              {
                translateX: (rect?.left || 0) * imgSize.width,
              },
              { translateY: -(rect?.top || 0) * imgSize.height },
            ],
          }}
        >
          <JglImage
            source={imageUrl}
            resizeMode={'contain'}
            width={imgSize.width || '100%'}
            height={imgSize.height || '100%'}
            onLoad={() => {
              setLoaded(true);
            }}
          />
          {loaded && contentOverImage}
        </JglView>
      </JglView>
    </JglView>
  );
};
