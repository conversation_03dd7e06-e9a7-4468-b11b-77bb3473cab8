import { useSafeAreaInsets } from '@jgl/biz-func';
import { useWindowDimensions } from '@jgl/utils';
import type { ReactNode } from 'react';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import {
  StyleSheet,
  TouchableWithoutFeedback,
  View,
  type ViewStyle,
} from 'react-native';
import Animated, {
  Extrapolate,
  interpolate,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

const springConfig = {
  stiffness: 120,
  damping: 20,
  mass: 0.3,
};

export type AiBookDropDownMenuRef = {
  openMenu: () => void;
  closeMenu: () => void;
  toggleMenu: () => void;
};

type Props = {
  children: ReactNode;
  topOffset?: number;
  menuContainerStyle?: Animated.AnimateStyle<ViewStyle>;
  maskContainerBgColor?: string;
  initialEstimatedHeight?: number; // Optional: for a more precise initial off-screen position
};

export const AiBookDropDownMenu = forwardRef<AiBookDropDownMenuRef, Props>(
  (props, ref) => {
    const {
      children,
      topOffset,
      menuContainerStyle,
      maskContainerBgColor,
      initialEstimatedHeight = 0,
    } = props;
    const safeInsets = useSafeAreaInsets();
    const windowSize = useWindowDimensions();
    const { height: SCREEN_HEIGHT } = windowSize;

    const actualTopOffset = topOffset ?? safeInsets.top;

    const animationProgress = useSharedValue(0); // 0 = closed, 1 = open
    const [isActive, setIsActive] = useState(false); // Controls mask presence and interactivity

    const lastScreenHeightRef = useRef(SCREEN_HEIGHT);

    // Estimate off-screen position. Uses SCREEN_HEIGHT to ensure it's well above the viewport.
    const offScreenYPosition = -initialEstimatedHeight;

    const openMenu = useCallback(() => {
      'worklet';
      runOnJS(setIsActive)(true);
      animationProgress.value = withSpring(1, springConfig);
    }, [animationProgress]);

    const closeMenu = useCallback(() => {
      'worklet';
      animationProgress.value = withSpring(0, springConfig, (finished) => {
        if (finished) {
          runOnJS(setIsActive)(false);
        }
      });
    }, [animationProgress]);

    useEffect(() => {
      if (lastScreenHeightRef.current !== SCREEN_HEIGHT) {
        lastScreenHeightRef.current = SCREEN_HEIGHT;
        if (animationProgress.value !== 0) {
          animationProgress.value = 0; // Reset without spring
          runOnJS(setIsActive)(false);
        }
      }
    }, [SCREEN_HEIGHT, animationProgress]);

    const toggleMenu = useCallback(() => {
      if (isActive) {
        // If substantially open or opening
        closeMenu();
      } else {
        openMenu();
      }
    }, [closeMenu, isActive, openMenu]);

    useImperativeHandle(ref, () => ({
      openMenu,
      closeMenu,
      toggleMenu,
    }));

    const animatedMenuStyle = useAnimatedStyle(() => {
      const yTranslate = interpolate(
        animationProgress.value,
        [0, 1],
        [offScreenYPosition, 0], // Slide from off-screen (negative Y) to 0 (at its positioned top)
        Extrapolate.CLAMP,
      );
      const opacity = interpolate(
        animationProgress.value,
        [0, 0.5, 1], // Fade in as it slides
        [0, 0.7, 1],
        Extrapolate.CLAMP,
      );
      return {
        transform: [{ translateY: yTranslate }],
        opacity: opacity,
        pointerEvents: animationProgress.value ? 'auto' : 'none',
      };
    });

    const maskAnimatedStyle = useAnimatedStyle(() => {
      const opacity = interpolate(
        animationProgress.value,
        [0, 1], // Opacity range for mask
        [0, 0.4], // Target opacity for mask (matches ChatSessionListSideMenu)
        Extrapolate.CLAMP,
      );
      return {
        opacity: opacity,
        backgroundColor: maskContainerBgColor ?? '#000000',
        position: 'absolute',
        top: actualTopOffset,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1001, // Below menu, above other content
      };
    });

    return (
      <>
        {/* Mask layer: Rendered when isActive is true to enable interaction */}
        {isActive && (
          <Animated.View style={maskAnimatedStyle}>
            <TouchableWithoutFeedback onPress={closeMenu} accessible={false}>
              <View style={{ flex: 1 }} />
            </TouchableWithoutFeedback>
          </Animated.View>
        )}

        {/* Dropdown Menu Container: Always mounted, animated in/out of view */}
        <Animated.View
          style={[
            styles.dropdownMenuContainer,
            { top: actualTopOffset }, // Positioned from the top based on prop or safe area
            menuContainerStyle, // User-provided styles for width, backgroundColor, etc.
            animatedMenuStyle, // Applies transform and opacity animations
          ]}
          // pointerEvents managed by its position and opacity; off-screen or transparent views don't catch events.
          // If it needs to be 'box-none' when hidden, isActive could control this via a regular style prop,
          // or use useAnimatedProps for 'pointerEvents' if fine-grained control needed.
          // For now, relying on it being visually non-interactive when closed.
        >
          {children}
        </Animated.View>
      </>
    );
  },
);

const styles = StyleSheet.create({
  dropdownMenuContainer: {
    position: 'absolute',
    left: 0,
    right: 0, // Default to full width; can be overridden by menuContainerStyle
    zIndex: 1002, // Higher than mask
    backgroundColor: 'white', // Default background, can be overridden
    // Shadow styles similar to ChatSessionListSideMenu, adjusted for dropdown
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2, // Shadow typically downwards for a dropdown
    },
    shadowOpacity: 0.1, // Softer shadow for dropdowns
    shadowRadius: 3,
    elevation: 3, // For Android shadow
  },
});
