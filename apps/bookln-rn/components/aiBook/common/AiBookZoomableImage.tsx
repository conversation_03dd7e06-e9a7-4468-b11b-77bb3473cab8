import { JglImage, JglTouchable, JglView } from '@jgl/ui-v4';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Animated, LayoutChangeEvent } from 'react-native';
import { useImageRenderSize } from '../../../hooks/aiBook';
import { ZoomView } from './AiBookZoomView';

type Rect = Partial<{
  left: number;
  top: number;
  width: number;
  height: number;
}>;
type Props<T extends Rect> = {
  imageUrl: string;
  cellWidth: number;
  maxHeight?: number;
  rects: T[];
  extractId: (item: T) => string;
  onScaleActiveChange: (scaleActive: boolean) => void;
  onPress: (item: T) => void;
  currentItem: T | undefined;
  enableZoom: boolean;
  showRects: boolean;
  onScrollToNextItem: () => boolean;
  onScrollToPrevItem: () => boolean;
  visible: boolean;
  renderContentInRect?: (item: T) => React.ReactNode;
};

const percent = (value?: number) => {
  const percentValue = (value || 0) * 100;
  return `${percentValue}%`;
};

export function AiBookZoomableImage<T extends Rect>(props: Props<T>) {
  const {
    imageUrl,
    cellWidth,
    rects,
    onPress,
    onScaleActiveChange,
    enableZoom,
    showRects,
    extractId,
    currentItem,
    onScrollToNextItem,
    onScrollToPrevItem,
    maxHeight,
    visible,
  } = props;
  const { imgSize, calculateImageSize } = useImageRenderSize({
    url: imageUrl,
  });

  // 添加闪烁动画状态
  const flashAnimation = useRef(new Animated.Value(0)).current;
  const hasAnimated = useRef(false);
  const [isFlashing, setIsFlashing] = useState(false);

  const [imageLoaded, setImageLoaded] = useState(false);
  // 创建闪烁动画
  useEffect(() => {
    // 只有当矩形框显示且尚未执行过动画时才执行
    const shouldShow =
      imageLoaded &&
      showRects &&
      visible &&
      rects &&
      rects.length > 0 &&
      !hasAnimated.current;

    if (!shouldShow) {
      return;
    }

    const timer = setTimeout(() => {
      setIsFlashing(true);
      hasAnimated.current = true;

      // 创建闪烁序列 (闪烁2次)
      Animated.sequence([
        Animated.timing(flashAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false, // 不能使用原生驱动，因为我们要改变背景色
        }),
        Animated.timing(flashAnimation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.timing(flashAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.timing(flashAnimation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: false,
        }),
      ]).start(() => {
        setIsFlashing(false);
      });
    }, 300);
    return () => {
      clearTimeout(timer);
    };
  }, [flashAnimation, imageLoaded, rects, showRects, visible]);

  const content = useMemo(() => {
    // 计算动画的颜色值
    const animatedBorderColor = flashAnimation.interpolate({
      inputRange: [0, 1],
      outputRange: ['rgba(34, 197, 94, 0.4)', 'rgba(34, 197, 94, 1)'], // 从淡绿色到亮绿色
    });

    const animatedBgColor = flashAnimation.interpolate({
      inputRange: [0, 1],
      outputRange: ['rgba(0, 255, 0, 0)', 'rgba(0, 255, 0, 0.4)'], // 从透明到半透明绿色
    });

    return (
      <JglView
        width={imgSize.width || undefined}
        height={imgSize.height || undefined}
        position='relative'
      >
        <JglImage
          source={imageUrl}
          resizeMode={'contain'}
          onLoad={() => setImageLoaded(true)}
        />

        {showRects &&
          imageLoaded &&
          rects?.map((item) => {
            const { left, top, width, height } = item;
            const isCurrent =
              currentItem && extractId(currentItem) === extractId(item);

            return (
              <Animated.View
                key={extractId(item)}
                // @ts-ignore
                style={{
                  position: 'absolute',
                  left: percent(left),
                  top: percent(top),
                  width: percent(width),
                  height: percent(height),
                  borderWidth: 1,
                  borderColor: isFlashing ? animatedBorderColor : '#22c55e', // $green7的RGB值
                  backgroundColor: isCurrent
                    ? 'rgba(0, 255, 0, 0.2)'
                    : isFlashing
                    ? animatedBgColor
                    : undefined,
                  minWidth: 0,
                  minHeight: 0,
                }}
              >
                <JglTouchable
                  style={{
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    right: 0,
                    bottom: 0,
                    minWidth: 0,
                    minHeight: 0,
                  }}
                  onPress={() => onPress(item)}
                >
                  {props.renderContentInRect?.(item)}
                </JglTouchable>
              </Animated.View>
            );
          })}
      </JglView>
    );
  }, [
    flashAnimation,
    imgSize.width,
    imgSize.height,
    imageUrl,
    showRects,
    imageLoaded,
    rects,
    currentItem,
    extractId,
    isFlashing,
    props,
    onPress,
  ]);

  const renderContent = () => {
    if (enableZoom) {
      return (
        <ZoomView
          onZoomActiveChange={onScaleActiveChange}
          onScrollToNextItem={onScrollToNextItem}
          onScrollToPrevItem={onScrollToPrevItem}
        >
          {content}
        </ZoomView>
      );
    } else {
      return content;
    }
  };

  return (
    <JglView
      flex={1}
      w={cellWidth}
      justifyContent='center'
      alignItems='center'
      overflow='hidden'
      // @ts-ignore
      onLayout={(e: LayoutChangeEvent) => {
        if (e.nativeEvent) {
          calculateImageSize({
            width: e.nativeEvent?.layout?.width,
            height: maxHeight ?? Number.POSITIVE_INFINITY,
          });
        }
      }}
    >
      {renderContent()}
    </JglView>
  );
}
