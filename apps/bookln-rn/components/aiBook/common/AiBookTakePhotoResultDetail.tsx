import {
  JglScrollView,
  JglStateView,
  JglText,
  JglTouchable,
  JglView,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { Check, X } from 'lucide-react-native';
import {
  TakePhotoAnswerResult,
  useTakePhotoResult,
} from '../../../hooks/aiBook';
import { RectImage } from './RectImage';
import { EmptyListFooter } from './EmptyListFooter';

const percent = (value?: number) => {
  const percentValue = (value || 0) * 100;
  return `${percentValue}%`;
};

type Props = {
  type: 'topic' | 'page';
  onPress?: (item: { pageId?: number; itemId?: number }) => void;
  specificAnswerResult?: TakePhotoAnswerResult[];
  hiddenInfo?: boolean;
};

export const AiBookTakePhotoResultDetail = ({
  type,
  onPress,
  specificAnswerResult,
  hiddenInfo,
}: Props) => {
  let { answerResult } = useTakePhotoResult({
    type,
    autoFetch: !specificAnswerResult,
  });

  if (specificAnswerResult) {
    answerResult = specificAnswerResult;
  }

  const imageUrl = answerResult?.[0]?.imageUrl ?? '';

  const renderInfo = () => {
    if (hiddenInfo) {
      return null;
    }
    return (
      <JglView flex={1}>
        <JglXStack spaceX={'$4'}>
          <JglText fontSize={16} lineHeight={36}>
            正确:{' '}
            {answerResult.reduce((acc, item) => acc + (item.rightNum || 0), 0)}
          </JglText>
          <JglText fontSize={16} lineHeight={36}>
            错误:{' '}
            {answerResult.reduce((acc, item) => acc + (item.wrongNum || 0), 0)}
          </JglText>
        </JglXStack>
        <JglYStack>
          <JglYStack spaceY={'$2'} flexWrap='wrap'>
            {answerResult?.map((result) => {
              return (
                <JglXStack
                  key={result.id}
                  spaceX={'$4'}
                  flexWrap='wrap'
                  borderWidth={1}
                  width='100%'
                  borderColor='$gray7'
                  flexShrink={0}
                  flex={1}
                >
                  {result.recResult?.map((item, index) => {
                    return (
                      <JglView
                        alignItems='center'
                        flexDirection='row'
                        key={item.id}
                        flexWrap='wrap'
                      >
                        <JglText fontSize={16} mr={4}>
                          {index + 1}.
                        </JglText>
                        {!item.isRight && (
                          <JglText fontSize={16} mr={4}>
                            {item?.refAnswer?.trim()}
                          </JglText>
                        )}
                        <JglText
                          fontSize={16}
                          color={item?.isRight ? '$green11' : '$red11'}
                        >
                          {item?.userAnswer?.trim()}
                        </JglText>
                      </JglView>
                    );
                  })}
                </JglXStack>
              );
            })}
          </JglYStack>
        </JglYStack>
      </JglView>
    );
  };

  const renderAnswerRect = () => {
    return answerResult
      .flatMap((item) => item.recResult)
      .map((rect, index) => (
        <JglView
          style={{ minWidth: 0, minHeight: 0 }}
          pos='absolute'
          key={index}
          left={percent(rect?.left || 0 + (rect?.width || 0))}
          top={percent(rect?.top)}
          width={24}
          height={24}
        >
          {rect?.isRight ? (
            <Check size={24} color={'green'} />
          ) : (
            <X size={24} color={'red'} />
          )}
        </JglView>
      ));
  };

  const renderTopicRect = () => {
    if (type === 'topic') return null;
    return answerResult.map((item, index) => {
      return (
        <JglTouchable
          key={index}
          pos='absolute'
          left={percent(item?.left)}
          top={percent(item?.top)}
          width={percent(item?.width)}
          height={percent(item?.height)}
          borderWidth={1}
          borderColor='$blue8'
          onPress={() => {
            onPress?.({
              pageId: item.pageId,
              itemId: item.itemId,
            });
          }}
        />
      );
    });
  };

  const getRect = () => {
    const defaultRect = {
      top: 0,
      left: 0,
      width: 1,
      height: 1,
    };
    if (type === 'topic' && answerResult?.[0]) {
      return answerResult?.[0] || defaultRect;
    } else {
      return defaultRect;
    }
  };
  const renderContent = () => {
    return (
      <RectImage
        imageUrl={imageUrl}
        contentOverImage={
          <>
            {renderAnswerRect()}
            {renderTopicRect()}
          </>
        }
        rect={getRect()}
      />
    );
  };

  const isEmpty = answerResult.length <= 0;
  return (
    <JglScrollView
      flex={1}
      contentContainerStyle={{
        flex: isEmpty ? 1 : undefined,
      }}
    >
      <JglStateView
        flex={1}
        justifyContent='center'
        alignItems='center'
        isEmpty={isEmpty}
        paddingHorizontal={type === 'topic' ? undefined : '$1'}
      >
        <JglView flex={1} width='100%'>
          {renderContent()}
          {renderInfo()}
          {type === 'page' && <EmptyListFooter />}
        </JglView>
      </JglStateView>
    </JglScrollView>
  );
};
