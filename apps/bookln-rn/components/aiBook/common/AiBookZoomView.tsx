import React, { useCallback } from 'react';
import { LayoutChangeEvent, View } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  runOnJS,
  runOnUI,
  useAnimatedStyle,
  useSharedValue,
  withDecay,
  withSpring,
} from 'react-native-reanimated';

interface Props {
  children: React.ReactNode;
  onZoomActiveChange: (isActive: boolean) => void;
  onScrollToNextItem: () => boolean;
  onScrollToPrevItem: () => boolean;
}

export const ZoomView: React.FC<Props> = ({
  children,
  onZoomActiveChange,
  onScrollToNextItem,
  onScrollToPrevItem,
}) => {
  const scale = useSharedValue<number>(1);
  const savedScale = useSharedValue<number>(1);
  const translateX = useSharedValue<number>(0);
  const translateY = useSharedValue<number>(0);
  const savedTranslateX = useSharedValue<number>(0);
  const savedTranslateY = useSharedValue<number>(0);

  const cellIsActive = useSharedValue<boolean>(false);

  const containerWidth = useSharedValue<number>(0);
  const containerHeight = useSharedValue<number>(0);

  const MAX_SCALE = 3;
  // 额外定义一个阈值，用于判断是否触发外部滚动
  const SCROLL_FLATLIST_THRESHOLD = 50; // 像素，用户需要额外拖出多少距离才触发外部滚动

  const onLayout = useCallback(
    (event: LayoutChangeEvent) => {
      const w = event.nativeEvent.layout.width;
      const h = event.nativeEvent.layout.height;
      containerWidth.value = w;
      containerHeight.value = h;
    },
    [containerHeight, containerWidth],
  );

  const resetTransforms = useCallback(() => {
    'worklet';
    scale.value = withSpring(1);
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
    savedScale.value = 1;
    savedTranslateX.value = 0;
    savedTranslateY.value = 0;
    if (cellIsActive.value) {
      cellIsActive.value = false;
      runOnJS(onZoomActiveChange)(false);
    }
  }, [
    cellIsActive,
    onZoomActiveChange,
    savedScale,
    savedTranslateX,
    savedTranslateY,
    scale,
    translateX,
    translateY,
  ]);

  const handleScrollToNextItem = useCallback(() => {
    const enable = onScrollToNextItem();
    if (enable) {
      setTimeout(() => {
        runOnUI(resetTransforms)();
      }, 300);
    }
  }, [onScrollToNextItem, resetTransforms]);

  const handleScrollToPrevItem = useCallback(() => {
    const enable = onScrollToPrevItem();
    if (enable) {
      setTimeout(() => {
        runOnUI(resetTransforms)();
      }, 300);
    }
  }, [onScrollToPrevItem, resetTransforms]);

  // --- Pinch Gesture ---
  const pinchGesture = Gesture.Pinch()
    .onStart(() => {
      cellIsActive.value = true;
      runOnJS(onZoomActiveChange)(true);
    })
    .onUpdate((event) => {
      const newScale = Math.min(
        MAX_SCALE,
        Math.max(1, savedScale.value * event.scale),
      );
      if (scale.value !== newScale) {
        scale.value = newScale;
      }
    })
    .onEnd(() => {
      savedScale.value = scale.value;
      if (scale.value <= 1) {
        resetTransforms();
      } else {
        const currentScale = scale.value;
        const maxDx = Math.max(
          0,
          (containerWidth.value * currentScale - containerWidth.value) / 2,
        );
        const maxDy = Math.max(
          0,
          (containerHeight.value * currentScale - containerHeight.value) / 2,
        );

        let finalTranslateX = translateX.value;
        let finalTranslateY = translateY.value;

        finalTranslateX = Math.min(Math.max(finalTranslateX, -maxDx), maxDx);
        finalTranslateY = Math.min(Math.max(finalTranslateY, -maxDy), maxDy);

        if (finalTranslateX !== translateX.value) {
          translateX.value = withSpring(finalTranslateX);
        }
        if (finalTranslateY !== translateY.value) {
          translateY.value = withSpring(finalTranslateY);
        }
        savedTranslateX.value = finalTranslateX;
        savedTranslateY.value = finalTranslateY;
      }
    });

  // --- Pan Gesture ---
  const panGesture = Gesture.Pan()
    // *** 关键修改：设置激活阈值 ***
    // 这意味着手指向任一方向移动超过 5 像素（可以调整）后，才会被识别为平移。
    // 一个正常的“点击”通常不会有超过 5 像素的位移。
    .activeOffsetX([-5, 5]) // 水平方向移动超过 5 像素才激活 Pan 手势
    .activeOffsetY([-5, 5]) // 垂直方向移动超过 5 像素才激活 Pan 手势
    .onStart(() => {
      if (scale.value > 1) {
        cellIsActive.value = true;
        runOnJS(onZoomActiveChange)(true);
        savedTranslateX.value = translateX.value;
        savedTranslateY.value = translateY.value;
      }
    })
    .onUpdate((event) => {
      if (scale.value > 1) {
        const currentScale = scale.value;
        const maxDx = Math.max(
          0,
          (containerWidth.value * currentScale - containerWidth.value) / 2,
        );
        const maxDy = Math.max(
          0,
          (containerHeight.value * currentScale - containerHeight.value) / 2,
        );

        let newTranslateX = savedTranslateX.value + event.translationX;
        let newTranslateY = savedTranslateY.value + event.translationY;

        // 允许的弹性拉出距离
        const BOUNCE_THRESHOLD_X = SCROLL_FLATLIST_THRESHOLD; // 可以和SCROLL_FLATLIST_THRESHOLD相同，也可以单独设置
        const BOUNCE_THRESHOLD_Y = SCROLL_FLATLIST_THRESHOLD;

        // 如果内容在 X 轴已经到边界，并且用户还在向外拉
        const isAtLeftEdgeAndPullingLeft = newTranslateX < -maxDx;
        const isAtRightEdgeAndPullingRight = newTranslateX > maxDx;

        // 如果内容在 Y 轴已经到边界，并且用户还在向外拉
        const isAtTopEdgeAndPullingUp = newTranslateY < -maxDy;
        const isAtBottomEdgeAndPullingDown = newTranslateY > maxDy;

        if (maxDx === 0) {
          // 如果没有放大到足以在X轴滚动，那么直接允许平移
          translateX.value = newTranslateX;
        } else {
          if (isAtLeftEdgeAndPullingLeft) {
            // 限制拉出的最大距离，只允许超出 BOUNCE_THRESHOLD_X
            translateX.value = Math.max(
              -maxDx - BOUNCE_THRESHOLD_X,
              newTranslateX,
            );
          } else if (isAtRightEdgeAndPullingRight) {
            // 限制拉出的最大距离，只允许超出 BOUNCE_THRESHOLD_X
            translateX.value = Math.min(
              maxDx + BOUNCE_THRESHOLD_X,
              newTranslateX,
            );
          } else {
            // 正常情况下，限制在边界内
            translateX.value = Math.max(-maxDx, Math.min(newTranslateX, maxDx));
          }
        }

        if (maxDy === 0) {
          // 如果没有放大到足以在Y轴滚动，那么直接允许平移
          translateY.value = newTranslateY;
        } else {
          if (isAtTopEdgeAndPullingUp) {
            translateY.value = Math.max(
              -maxDy - BOUNCE_THRESHOLD_Y,
              newTranslateY,
            );
          } else if (isAtBottomEdgeAndPullingDown) {
            translateY.value = Math.min(
              maxDy + BOUNCE_THRESHOLD_Y,
              newTranslateY,
            );
          } else {
            translateY.value = Math.max(-maxDy, Math.min(newTranslateY, maxDy));
          }
        }
      }
    })
    .onEnd((event) => {
      if (scale.value > 1) {
        const currentScale = scale.value;
        const maxDx = Math.max(
          0,
          (containerWidth.value * currentScale - containerWidth.value) / 2,
        );
        const maxDy = Math.max(
          0,
          (containerHeight.value * currentScale - containerHeight.value) / 2,
        );

        // --- 核心修改：基于用户实际的拖动量判断是否触发外部滚动 ---
        // 计算如果没有任何限制，用户实际拖动到的理论位置
        const theoreticalTranslateX =
          savedTranslateX.value + event.translationX;

        // 判断是否应该触发外部 FlatList 滚动
        const shouldScrollFlatListNext =
          theoreticalTranslateX < -maxDx - SCROLL_FLATLIST_THRESHOLD;
        const shouldScrollFlatListPrev =
          theoreticalTranslateX > maxDx + SCROLL_FLATLIST_THRESHOLD;

        // 如果内容真的到了水平边缘并且用户尝试拉过阈值
        if (shouldScrollFlatListNext) {
          runOnJS(handleScrollToNextItem)();
          return; // 阻止内部 withDecay 动画
        } else if (shouldScrollFlatListPrev) {
          runOnJS(handleScrollToPrevItem)();
          return; // 阻止内部 withDecay 动画
        }

        // 如果没有触发外部滚动，则进行内部减速动画，并回弹到边界
        // 注意：这里 translateY.value 和 translateX.value 会被 withDecay 自动回弹到 clamp 范围
        translateX.value = withDecay({
          velocity: event.velocityX,
          clamp: [-maxDx, maxDx],
        });

        translateY.value = withDecay({
          velocity: event.velocityY,
          clamp: [-maxDy, maxDy],
        });
      } else {
        resetTransforms();
      }
    });

  // --- Double Tap Gesture ---
  const doubleTapGesture = Gesture.Tap()
    .numberOfTaps(2)
    .onStart(() => {
      resetTransforms();
    });

  const internalGesture = Gesture.Race(
    doubleTapGesture,
    pinchGesture,
    panGesture.enabled(cellIsActive.value),
  );

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateY: translateY.value },
        { translateX: translateX.value },
        { scale: scale.value },
      ],
      // 当图片处于放大状态 (scale.value > 1) 时，允许 children 接收触摸事件。
      // Gesture Handler 会在原生层处理手势冲突。
      // 只有当 Pan 或 Pinch 手势真正被识别并激活时，才不会触发 children 的 onPress。
      // 如果没有 Pan 或 Pinch 手势被激活，点击事件会传递给 children。
      pointerEvents: 'auto', // 默认一直保持 auto，让手势识别器处理
    };
  });

  return (
    <View style={{ flex: 1, width: '100%' }}>
      <GestureDetector gesture={internalGesture}>
        <Animated.View
          style={[
            {
              width: '100%',
              height: '100%',
              justifyContent: 'center',
              alignItems: 'center',
              overflow: 'hidden',
            },
            animatedStyle,
          ]}
        >
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
              width: '100%',
            }}
            onLayout={onLayout}
          >
            {children}
          </View>
        </Animated.View>
      </GestureDetector>
    </View>
  );
};
