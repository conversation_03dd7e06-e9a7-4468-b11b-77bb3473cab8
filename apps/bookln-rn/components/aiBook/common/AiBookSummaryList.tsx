import { useSafeAreaInsets } from '@jgl/biz-func';
import {
  JglButton,
  JglCenterModal,
  JglImage,
  JglScrollView,
  JglText,
  JglTouchable,
  JglView,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { router, useWindowDimensions } from '@jgl/utils';
import { useAtom, useAtomValue } from 'jotai';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { StyleSheet } from 'react-native';
import {
  type GestureEvent,
  ScrollView as GestureScrollView,
  type PanGestureHandlerEventPayload,
  State,
} from 'react-native-gesture-handler';
import {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import { atomMap } from '../../../atom';
import {
  type AiBookPage,
  type AiBookTopicItem,
  useBookActions,
} from '../../../hooks/aiBook';
import { routerMap } from '../../../utils/routerMap';

const menuWidthRatio = 0.85;
const deltaWidth = 0;

export type AiBookSummaryListRef = {
  closeMenu: () => void;
};

export interface AiBookSummaryListProps {
  onCloseMenu: () => void;
}

const randomDialogs = [
  '你最近的练习特别稳定，继续加油就是高手！',
  '认真的人运气都不会差，看好你哦～',
  '学习这件事，你已经走在了前面！👏',
  '你的坚持让知识慢慢变得简单！',
  '上次的小难题，这次你就轻松拿下了～👍',
  '一点点进步，也是在偷偷变强哟~',
  '最近答题越来越准了，好苗头！',
  '继续保持，知识在你脑袋里扎根了！🌱',
  '你的专注力，值得一个超级赞！',
  '这个节奏太棒了，太羡慕你啦！🐳',
];

const badgeConfigs = {
  '🏹': {
    title: '连续答对22题，开挂了吧？',
    value: '22',
    emoji: '🏹',
  },
  '📸': {
    title: '已完成拍照批改50次',
    value: '50',
    emoji: '📸',
  },
  '💯': {
    title: '单页所有题目全对15次，完美主义！',
    value: '15',
    emoji: '💯',
  },
  '🏆': {
    title: '完成通关，学霸就是你！',
    value: '',
    emoji: '🏆',
  },
};

const randomDialog = () => {
  return randomDialogs[Math.floor(Math.random() * randomDialogs.length)];
};

export const AiBookSummaryList = forwardRef<
  AiBookSummaryListRef,
  AiBookSummaryListProps
>((props: AiBookSummaryListProps, ref) => {
  const { onCloseMenu } = props;
  const [aiBookInfo, setAiBookInfo] = useAtom(atomMap.aiBookInfo);
  const { totalCount, wrongCount } = useAtomValue(
    atomMap.aiBookTakePhotoResult,
  );
  const book = useAtomValue(atomMap.aiBook);
  const { collectionTopics = [] } = useAtomValue(atomMap.aiBookMockData);
  const { pageId, topicId } = aiBookInfo;

  const safeInsets = useSafeAreaInsets();
  const navigationBarHeight = safeInsets.top;
  const [centerModalVisible, setCenterModalVisible] = useState(false);
  const [selectedBadge, setSelectedBadge] = useState<string>('');

  // 获取屏幕尺寸，适配横竖屏
  const windowSize = useWindowDimensions();
  const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = windowSize;

  const initX = -2 * SCREEN_WIDTH;
  const lastScreenWidthRef = useRef(initX);

  // 菜单宽度自适应屏幕
  const sideMenuWidth = useMemo(() => {
    const resultWidth = SCREEN_WIDTH * menuWidthRatio;
    return resultWidth;
  }, [SCREEN_WIDTH]);

  // 动画相关状态
  const translateX = useSharedValue(initX); // 菜单初始隐藏在左侧
  const isMenuOpen = useSharedValue(0); // 动画层菜单开关
  const [isOpen, setIsOpen] = useState(false); // React 层菜单开关（用于遮罩渲染）

  // 菜单滑动动画样式
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  // 遮罩层动画样式，透明度与菜单滑动联动
  const maskAnimatedStyle = useAnimatedStyle(() => {
    const opacity = ((-initX + translateX.value) / sideMenuWidth) * 0.5;
    return {
      opacity: Math.max(0, Math.min(0.4, opacity)),
      backgroundColor: '#000000',
      position: 'absolute',
      top: 0,
      left: 0,
      width: SCREEN_WIDTH,
      bottom: 0,
      zIndex: 11,
    };
  });

  const { takePhotoAction, bookResourceAction } = useBookActions();

  // 暴露控制方法给父组件
  useImperativeHandle(ref, () => ({
    closeMenu,
  }));

  /** 打开菜单（动画） */
  const openMenu = useCallback(() => {
    const newTranslateX = deltaWidth;
    translateX.value = withSpring(newTranslateX, {
      stiffness: 100,
      damping: 20,
      mass: 0.25,
    });
    isMenuOpen.value = 1;
    setIsOpen(true);
  }, [isMenuOpen, translateX]);

  /** 关闭菜单（动画） */
  const closeMenu = useCallback(() => {
    //   const newTranslateX = initX;
    //   translateX.value = withSpring(newTranslateX, {
    //     stiffness: 100,
    //     damping: 20,
    //     mass: 0.25,
    //   });
    //   isMenuOpen.value = 0;
    //   setIsOpen(false);
    onCloseMenu();
  }, [onCloseMenu]);

  useEffect(() => {
    if (lastScreenWidthRef.current !== initX) {
      lastScreenWidthRef.current = initX;
      closeMenu();
    }
  }, [initX, closeMenu]);

  /** 切换菜单开关 */
  const toggleMenu = useCallback(() => {
    closeMenu();
  }, [closeMenu]);

  const actions = useMemo(() => {
    return [
      // {
      //   title: '拍照批改',
      //   onPress: () => {
      //     toggleMenu();
      //     takePhotoAction();
      //   },
      // },
      {
        title: '本书题集',
        onPress: () => {
          toggleMenu();
          router.push(routerMap.BookTopic);
        },
      },

      // {
      //   title: '本书知识点',
      //   onPress: () => {
      //     toggleMenu();
      //     router.push(routerMap.WebView, {
      //       title: '本书知识点',
      //       url: myKnowledgeUrl,
      //       supportScreenRotation: true,
      //       supportLogin: false,
      //     });
      //   },
      // },
      {
        title: '配套资料',
        onPress: () => {
          toggleMenu();
          bookResourceAction();
        },
      },
      {
        title: '扩展:本书单词',
        onPress: () => {
          // showToast({ title: '本书单词' });
          router.push(routerMap.WebView, {
            title: '本书单词',
            url: 'https://mp.pxwhqr.cn/a/2hx/110000010F00000006000007G4K41BDD.q?sh=sh',
            supportScreenRotation: true,
            supportLogin: false,
          });
        },
      },
    ];
  }, [bookResourceAction, toggleMenu]);

  const topActions = useMemo(() => {
    return [
      {
        title: '本书错题',
        value: wrongCount,
        onPress: () => {
          toggleMenu();
          router.push(routerMap.BookWrongExercise);
        },
      },
      {
        title: '本书收藏',
        value: collectionTopics.length,
        onPress: () => {
          toggleMenu();
          router.push(routerMap.BookCollection);
        },
      },
    ];
  }, [collectionTopics.length, toggleMenu, wrongCount]);

  const handleClickPage = useCallback(
    (param: { page?: AiBookPage; topic?: AiBookTopicItem }) => {
      const { page, topic } = param || {};
      if (page) {
        setAiBookInfo((prev) => {
          return {
            ...prev,
            pageId: page.pageId,
            topicId: undefined,
          };
        });
      } else if (topic) {
        setAiBookInfo((prev) => {
          return {
            ...prev,
            pageId: topic.pageId,
            topicId: topic.itemId,
          };
        });
      }

      closeMenu();
    },
    [closeMenu, setAiBookInfo],
  );

  /**
   * 手势滑动菜单
   * - 以菜单展开位置为基准，累加 translationX
   * - 限制滑动范围在 [展开, 隐藏] 区间
   */
  const handlePan = useCallback(
    (event: GestureEvent<PanGestureHandlerEventPayload>) => {
      'worklet';
      const menuStartX = 0;
      const newX = menuStartX + event.nativeEvent.translationX;
      translateX.value = Math.min(0, Math.max(newX, initX));
    },
    [initX, translateX],
  );

  /**
   * 手势结束时判断菜单收回或展开
   * - 超过一半则收回，否则保持展开
   * - 同步动画层和 React 层状态
   */
  const handleStateChange = useCallback(
    (state: State) => {
      'worklet';
      if (state === State.END) {
        if (translateX.value > -sideMenuWidth / 2) {
          translateX.value = withSpring(0, {
            stiffness: 100,
            damping: 20,
          });
          runOnJS(() => {
            isMenuOpen.value = 1;
            setIsOpen(true);
          })();
        } else {
          translateX.value = withSpring(initX, {
            stiffness: 100,
            damping: 20,
          });
          runOnJS(() => {
            isMenuOpen.value = 0;
            setIsOpen(false);
          })();
        }
      }
    },
    [isMenuOpen, initX, sideMenuWidth, translateX],
  );

  const scrollViewRef = useRef<GestureScrollView>(null);

  const renderCatalogList = useCallback(() => {
    return (
      <GestureScrollView
        ref={scrollViewRef}
        style={{ flex: 1 }}
        contentContainerStyle={{
          paddingBottom:
            navigationBarHeight + safeInsets.bottom + safeInsets.top,
        }}
        showsVerticalScrollIndicator={false}
      >
        {book?.pages?.map((page) => {
          const isCurrentPage = pageId === page.pageId && !topicId;
          return (
            <JglYStack
              key={page.pageId}
              flex={1}
              borderBottomWidth={1}
              borderColor='$color6'
              // paddingBottom={'$1'}
              spaceY={'$2'}
            >
              <JglTouchable onPress={() => handleClickPage({ page })} flex={1}>
                <JglXStack justifyContent='space-between' flex={1}>
                  <JglText
                    flex={1}
                    fontSize={16}
                    lineHeight={24}
                    flexWrap='wrap'
                    color={isCurrentPage ? '$color12' : '$color10'}
                  >
                    {page.catalogName}
                  </JglText>
                  <JglText
                    color={isCurrentPage ? '$color12' : '$color10'}
                    fontSize={16}
                    lineHeight={24}
                  >
                    {page.pageNo}
                  </JglText>
                </JglXStack>
              </JglTouchable>

              {page.topics?.map((topic) => {
                const isCurrentTopic =
                  topicId === topic.itemId && pageId === topic.pageId;
                return (
                  <JglTouchable
                    key={topic.itemId}
                    onPress={() => handleClickPage({ topic })}
                  >
                    <JglXStack justifyContent='space-between' flex={1}>
                      <JglText
                        color={isCurrentTopic ? '$color12' : '$color10'}
                        flexWrap='wrap'
                        flexShrink={1}
                        fontWeight={isCurrentTopic ? 'bold' : 'normal'}
                        pl={'$4'}
                      >
                        {topic.title?.name}
                      </JglText>
                      <JglText color={isCurrentTopic ? '$color12' : '$color10'}>
                        {page.pageNo}
                      </JglText>
                    </JglXStack>
                  </JglTouchable>
                );
              })}
            </JglYStack>
          );
        })}
      </GestureScrollView>
    );
  }, [
    navigationBarHeight,
    safeInsets.bottom,
    safeInsets.top,
    book?.pages,
    pageId,
    topicId,
    handleClickPage,
  ]);

  const renderSummaryList = useCallback(() => {
    const knowledgeHeight = 60;
    const firstSectionConfigs = [
      { key: 'totalCount', value: totalCount, title: '已练习题目' },
      { key: 'days', value: 24, title: '学习天数' },
    ];

    const mockCorrectRate = [0.7, 0.65, 0.75, 0.8, 0.7, 0.9, 0.8];
    const knowledgeConfigs = [
      { title: '新手', value: 0.8, color: '#BCCA56' },
      { title: '熟练', value: 0.69, color: '#89C854' },
      { title: '精通', value: 0.35, color: '#F2CD5A' },
    ];
    return (
      <JglScrollView px={10}>
        <JglYStack flex={1} mt={'$1'} pb={20}>
          {/* 已练习, 学习天数 */}
          <JglXStack>
            {firstSectionConfigs.map((item) => {
              return (
                <JglYStack
                  flex={1}
                  alignItems='center'
                  justifyContent='flex-end'
                  spaceY={'$1'}
                  key={item.key}
                >
                  <JglView
                    justifyContent='flex-end'
                    alignItems='center'
                    h={knowledgeHeight}
                    // bg='red'
                  >
                    <JglText
                      fontSize={36}
                      alignItems='center'
                      justifyContent='center'
                    >
                      {item.value}
                    </JglText>
                  </JglView>
                  <JglText>{item.title}</JglText>
                </JglYStack>
              );
            })}
            <JglYStack flex={1} jc='space-between' ai='center'>
              <JglXStack
                height={55}
                w={'full'}
                alignItems='flex-end'
                // justifyContent='space-around'
                space={5}
                jc='center'
              >
                {mockCorrectRate.map((item, index) => {
                  return (
                    <JglView
                      bg='$gray6'
                      borderRadius={3}
                      w={6}
                      key={index}
                      h={`${item * 100}%`}
                    />
                  );
                })}
              </JglXStack>
              <JglText>正确率趋势</JglText>
            </JglYStack>

            <JglYStack flex={1} spaceY={'$1'} alignItems='center'>
              <JglTouchable
                flexDirection='column'
                flex={1}
                onPress={() => {
                  closeMenu();
                  // router.push(routerMap.AiBookKnowledgePointDetail);
                  router.push(routerMap.WebView, {
                    title: '知识点掌握情况',
                    url: 'https://ajyuntitmp.bookln.cn/aj/app/day90/product/xingdengboot/default/332922936/332922936_d3a97ac97067-4660-be83-e9758749cf85.html',
                    supportScreenRotation: true,
                    supportLogin: false,
                  });
                }}
              >
                <JglYStack
                  flex={1}
                  h={knowledgeHeight}
                  // ai='center'
                  w={'full'}
                  space={5}
                  pt={5}
                  ml={12}
                  jc='center'
                  // alignSelf='center'
                >
                  {knowledgeConfigs.map((item, index) => {
                    return (
                      <JglXStack key={item.title} alignItems='center'>
                        {/* <JglText fontSize={14} mr={'$2'}>
                        {item.title} {item.value * 100}%
                      </JglText> */}
                        <JglView
                          bg={item.color}
                          h={6}
                          w={`${item.value * 100}%`}
                        />
                        {/* <JglView flex={1} h={6}>
                        <JglView
                          bg='$gray6'
                          borderRadius={3}
                          h={6}
                          key={index}
                          w={`${item.value * 100}%`}
                        />
                      </JglView> */}
                      </JglXStack>
                    );
                  })}
                </JglYStack>
                <JglText>知识点掌握情况</JglText>
              </JglTouchable>
            </JglYStack>
          </JglXStack>

          {/* 图片徽章 */}
          {/* <JglImage
            mt={'$4'}
            source={require('../../../assets/images/aibook_fake_award.png')}
            // w={'full'}
            h={44}
            w={172}
          // /> */}
          <JglXStack ml={8} my={20} spaceX={15}>
            <JglTouchable
              onPress={() => {
                setSelectedBadge('🏹');
                setCenterModalVisible(true);
              }}
            >
              <JglText>🏹22</JglText>
            </JglTouchable>
            <JglTouchable
              onPress={() => {
                setSelectedBadge('📸');
                setCenterModalVisible(true);
              }}
            >
              <JglText>📸50</JglText>
            </JglTouchable>
            <JglTouchable
              onPress={() => {
                setSelectedBadge('💯');
                setCenterModalVisible(true);
              }}
            >
              <JglText>💯15</JglText>
            </JglTouchable>
            <JglTouchable
              onPress={() => {
                setSelectedBadge('🏆');
                setCenterModalVisible(true);
              }}
            >
              <JglText>🏆通关</JglText>
            </JglTouchable>
          </JglXStack>

          {/* 随机文案 */}
          {/* <JglText mt={'$4'}>{randomDialog()}</JglText> */}

          {/* 上部按钮 */}
          <JglYStack mt={20}>
            <JglXStack justifyContent='space-between' spaceX={'$4'}>
              {topActions.map((item) => (
                <JglButton
                  flex={1}
                  key={item.title}
                  onPress={item.onPress}
                  color='$gray3'
                >
                  <JglXStack spaceX={'$2'}>
                    <JglText>{item.title}</JglText>
                    <JglText>{item.value}</JglText>
                  </JglXStack>
                </JglButton>
              ))}
            </JglXStack>

            <JglXStack
              flexWrap='wrap'
              flexDirection='row'
              spaceX={'$4'}
              spaceY={'$2'}
              mt={'$4'}
            >
              {actions.map((item) => (
                <JglButton
                  key={item.title}
                  onPress={item.onPress}
                  color='$gray3'
                  flexShrink={0}
                >
                  <JglText>{item.title}</JglText>
                </JglButton>
              ))}
            </JglXStack>
          </JglYStack>

          <JglText mt={'$5'} lineHeight={20}>
            <JglText fontWeight='bold'>{'学习建议：'}</JglText>
            {`基于你以上学习情况，发现你在单词的比较级，音节学习存在薄弱，建议你去学习本书`}
            <JglText color='#4E76FF'>第7页第2题</JglText>
            {`、`}
            <JglText color='#4E76FF'>第8页第3题</JglText>
            {'。同时，为你准备了学习课包'}
            <JglTouchable
              style={{
                transform: [{ translateX: 6 }, { translateY: 12 }],
              }}
              onPress={() => {
                toggleMenu();
                router.push(routerMap.WebView, {
                  title: '学习建议',
                  url: 'https://mp.bookln.cn/a/1de/110000010F000000060000025B161BD1.q',
                  supportScreenRotation: true,
                  supportLogin: false,
                });
              }}
            >
              <JglText color='#4E76FF'>{'去提升 >'}</JglText>
            </JglTouchable>
          </JglText>

          <JglCenterModal
            isShow={centerModalVisible}
            onDismiss={() => {
              setCenterModalVisible(false);
              setSelectedBadge('');
            }}
            bg='$background'
          >
            <JglYStack
              space={'$5'}
              width={'full'}
              height={'full'}
              alignItems='center'
              justifyContent='center'
              bg='$background'
              flex={1}
              py={'$10'}
            >
              <JglText fontSize={150}>
                {badgeConfigs[selectedBadge]?.emoji}
                {badgeConfigs[selectedBadge]?.value && (
                  <JglText fontSize={30}>
                    {badgeConfigs[selectedBadge]?.value}
                  </JglText>
                )}
              </JglText>
              <JglText fontSize={25}>
                {badgeConfigs[selectedBadge]?.title}
              </JglText>
            </JglYStack>
          </JglCenterModal>
        </JglYStack>
      </JglScrollView>
    );
  }, [
    totalCount,
    topActions,
    actions,
    centerModalVisible,
    selectedBadge,
    closeMenu,
    toggleMenu,
  ]);

  return <>{renderSummaryList()}</>;
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sideMenuContainer: {
    position: 'absolute',
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  menuContent: {
    flex: 1,
  },
});
