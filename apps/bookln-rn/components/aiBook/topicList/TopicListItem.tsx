import {
  JglText,
  Jg<PERSON><PERSON>ou<PERSON>ble,
  Jgl<PERSON>iew,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import React from 'react';
import { AiBookTopicItem, AiTopicType } from '../../../hooks/aiBook';
import { RectImage } from '../common/RectImage';

type Props = {
  imageUrl: string;
  topic: AiBookTopicItem;
  onPress: (topic: AiBookTopicItem) => void;
  rect?: {
    width: number;
    height: number;
    left: number;
    top: number;
  };
  contentOverImage?: React.ReactNode;
  showTag?: boolean;
  maxHeight?: number;
};

const defaultRect = {
  width: 1,
  height: 1,
  left: 0,
  top: 0,
};
export const TopicListItem = ({
  imageUrl,
  topic,
  onPress,
  rect,
  contentOverImage,
  showTag = true,
  maxHeight,
}: Props) => {
  const renderTags = () => {
    if (!showTag) return null;
    return (
      <JglText backgroundColor='$gray5' p='$1' borderRadius={20}>
        {topic.type === AiTopicType.Word ? '知识' : '题'}
      </JglText>
    );
  };

  const renderTitle = () => {
    return (
      <JglText>
        P{topic.pageNo} {topic.title?.index}
      </JglText>
    );
  };

  const renderTime = () => {
    const ymd = new Date(topic.time || 0).toLocaleDateString().split(' ')[0];
    const [year = '', month = '', date = ''] = ymd?.split('/') || [];
    if (year !== new Date().getFullYear() + '') {
      return <JglText>{`${year}年${month}月${date}日`}</JglText>;
    } else {
      return <JglText>{`${month}月${date}日`}</JglText>;
    }
  };

  return (
    <JglYStack paddingVertical={8}>
      <JglXStack justifyContent='space-between' p='$2'>
        <JglXStack spaceX={'$2'} alignItems='center'>
          {renderTags()}
          {renderTitle()}
        </JglXStack>
        {renderTime()}
      </JglXStack>
      <JglView>
        <JglTouchable onPress={() => onPress(topic)}>
          <RectImage
            imageUrl={imageUrl}
            rect={rect || defaultRect}
            contentOverImage={contentOverImage}
            maxHeight={maxHeight}
          />
        </JglTouchable>
      </JglView>
    </JglYStack>
  );
};
