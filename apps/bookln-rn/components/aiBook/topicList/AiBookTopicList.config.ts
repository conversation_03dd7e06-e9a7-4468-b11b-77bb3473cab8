import { AiBookTopicItem } from '../../../hooks/aiBook';

export enum CollectionFilterType {
  All = 'all',
  Topic = 'topic',
  Knowledge = 'knowledge',
}

export enum CollectionOrderFilterType {
  TimeAsc = 'timeAsc',
  TimeDesc = 'timeDesc',
  PageAsc = 'pageAsc',
  PageDesc = 'pageDesc',
}

export enum TopicFilterType {
  All = 'all',
  Normal = 'normal',
  Listen = 'listen',
  Write = 'write',
}

export enum TopicLevelType {
  All = 'all',
  Easy = 'easy',
  Medium = 'medium',
  Hard = 'hard',
}

export const contentFilters = [
  {
    label: '所有',
    value: CollectionFilterType.All,
  },
  {
    label: '题目',
    value: CollectionFilterType.Topic,
  },
  {
    label: '知识',
    value: CollectionFilterType.Knowledge,
  },
];
export const orderFilters = [
  {
    label: '时间倒序',
    value: CollectionOrderFilterType.TimeDesc,
  },
  {
    label: '时间正序',
    value: CollectionOrderFilterType.TimeAsc,
  },

  {
    label: '页码倒序',
    value: CollectionOrderFilterType.PageDesc,
  },
  {
    label: '页码正序',
    value: CollectionOrderFilterType.PageAsc,
  },
];

export const topicContentFilters = [
  {
    label: '题型',
    value: TopicFilterType.All,
  },
  {
    label: '常规',
    value: TopicFilterType.Normal,
  },
  {
    label: '听力',
    value: TopicFilterType.Listen,
  },
  {
    label: '写作',
    value: TopicFilterType.Write,
  },
];

export const levelFilters = [
  {
    label: '难度',
    value: TopicLevelType.All,
  },
  {
    label: '简单',
    value: TopicLevelType.Easy,
  },
  {
    label: '一般',
    value: TopicLevelType.Medium,
  },
  {
    label: '困难',
    value: TopicLevelType.Hard,
  },
];

export const handleOrder = (
  value: CollectionOrderFilterType,
  data: AiBookTopicItem[],
) => {
  switch (value) {
    case CollectionOrderFilterType.PageAsc: {
      return [...data].sort((a, b) => (a.pageNo || 0) - (b.pageNo || 0));
    }
    case CollectionOrderFilterType.PageDesc: {
      return [...data].sort((b, a) => (a.pageNo || 0) - (b.pageNo || 0));
    }
    case CollectionOrderFilterType.TimeAsc: {
      return [...data].sort((a, b) => (a.time || 0) - (b.time || 0));
    }
    case CollectionOrderFilterType.TimeDesc: {
      return [...data].sort((b, a) => (a.time || 0) - (b.time || 0));
    }
  }
};
