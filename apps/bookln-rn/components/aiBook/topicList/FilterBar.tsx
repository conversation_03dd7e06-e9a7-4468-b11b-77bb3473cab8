import { Jgl<PERSON><PERSON><PERSON>, Jgl<PERSON>ouchable, JglView, JglXStack } from '@jgl/ui-v4';
import { useWindowDimensions } from '@jgl/utils';
import { Check, ChevronDown } from '@tamagui/lucide-icons';
import { useRef, useState } from 'react';
import { TouchableWithoutFeedback } from 'react-native';
import {
  AiBookDropDownMenu,
  AiBookDropDownMenuRef,
} from '../common/AiBookDropDownMenu';

export type GroupData = {
  label: string;
  value: string;
}[];
type FilterBarProps = {
  groups: GroupData[];
  onChange: (groups: string[]) => void;
};

const height = 56;
export const FilterBar = ({ groups, onChange }: FilterBarProps) => {
  const [allSelected, setAllSelected] = useState<
    { label: string; value: string }[]
  >(groups.map((item) => item[0] || { label: '', value: '' }));
  const [clickGroupIndex, setClickGroupIndex] = useState<number>(-1);
  const currentMenuOptions = groups[clickGroupIndex] || [];
  const currentSelectMenu = allSelected[clickGroupIndex] || {
    label: '',
    value: '',
  };
  const windowHeight = useWindowDimensions().height;

  const dropDownMenuRef = useRef<AiBookDropDownMenuRef>(null);

  const handleChange = (option: { label: string; value: string }) => {
    dropDownMenuRef.current?.closeMenu();
    const newAllSelected = allSelected.map((val, i) => {
      return i === clickGroupIndex ? option : val;
    });

    setAllSelected(newAllSelected);
    onChange(newAllSelected.map((item) => item.value));
  };

  return (
    <JglView zIndex={1000}>
      <JglXStack spaceX={'$4'} p={'$2'} h={height}>
        {allSelected.map((selectedItem, index) => {
          return (
            <JglXStack key={index} flex={1} bg='#F8F8F8' borderRadius={6}>
              <JglTouchable
                onPress={() => {
                  setClickGroupIndex(index);
                  dropDownMenuRef.current?.toggleMenu();
                }}
                justifyContent='space-between'
                alignItems='center'
                flexDirection='row'
                flex={1}
                paddingHorizontal={'$1'}
              >
                <JglText fontSize={16} mr={'$2'} color='$gray12'>
                  {selectedItem.label}
                </JglText>
                <ChevronDown size={16} />
              </JglTouchable>
            </JglXStack>
          );
        })}
      </JglXStack>

      <AiBookDropDownMenu
        ref={dropDownMenuRef}
        topOffset={height}
        maskContainerBgColor='transparent'
        menuContainerStyle={{
          backgroundColor: 'transparent',
        }}
      >
        <JglView flex={1}>
          <JglView backgroundColor='white'>
            {currentMenuOptions.map((option) => {
              return (
                <JglTouchable
                  key={option.label + option.value}
                  backgroundColor='#F8F8F8'
                  onPress={() => handleChange(option)}
                >
                  <JglXStack
                    justifyContent='space-between'
                    alignItems='center'
                    flexDirection='row'
                    flex={1}
                    paddingHorizontal={'$1'}
                    h={40}
                  >
                    <JglText fontSize={16} mr={'$2'} color='$gray12'>
                      {option.label}
                    </JglText>
                    {option.value === currentSelectMenu.value ? (
                      <Check size={16} />
                    ) : null}
                  </JglXStack>
                </JglTouchable>
              );
            })}
          </JglView>
          <TouchableWithoutFeedback
            onPress={() => {
              dropDownMenuRef.current?.closeMenu();
            }}
          >
            <JglView
              h={windowHeight}
              style={{ backgroundColor: 'rgba(0,0,0,0.4)' }}
            />
          </TouchableWithoutFeedback>
        </JglView>
      </AiBookDropDownMenu>
    </JglView>
  );
};
