import { JglSafeArea, <PERSON>g<PERSON><PERSON><PERSON>w, JglYStack } from '@jgl/ui-v4';
import { router } from '@jgl/utils';
import { useAtomValue, useSetAtom } from 'jotai';
import { Check, X } from 'lucide-react-native';
import { useEffect, useMemo, useState } from 'react';
import { FlatList } from 'react-native';
import { atomMap } from '../../../atom';
import { AiBookTopicItem, AiTopicType } from '../../../hooks/aiBook';
import { routerMap } from '../../../utils/routerMap';
import { EmptyListFooter } from '../common/EmptyListFooter';
import {
  CollectionFilterType,
  CollectionOrderFilterType,
  contentFilters,
  handleOrder,
  levelFilters,
  orderFilters,
  topicContentFilters,
  TopicFilterType,
  TopicLevelType,
} from './AiBookTopicList.config';
import { FilterBar } from './FilterBar';
import { TopicListItem } from './TopicListItem';

type Props = {
  /**
   * collection: 收藏
   * wrong: 错题
   * topics: 题集
   */
  type: 'collection' | 'wrong' | 'topics';
};

const percent = (value?: number) => {
  const percentValue = (value || 0) * 100;
  return `${percentValue}%`;
};

export const AiBookTopicList = ({ type }: Props) => {
  const mockData = useAtomValue(atomMap.aiBookMockData);
  const { collectionTopics, wrongTopics, topics } = mockData;
  const setAiBookInfo = useSetAtom(atomMap.aiBookInfo);
  const setAiBookTakePhotoResult = useSetAtom(atomMap.aiBookTakePhotoResult);
  const originalDatasource = useMemo(() => {
    if (type === 'collection') {
      return [...collectionTopics].sort(
        (b, a) => (a.time || 0) - (b.time || 0),
      );
    } else if (type === 'wrong') {
      return [...wrongTopics].sort((b, a) => (a.time || 0) - (b.time || 0));
    } else {
      return topics;
    }
  }, [collectionTopics, topics, type, wrongTopics]);

  useEffect(() => {
    setDataSource(originalDatasource);
  }, [originalDatasource]);

  const [dataSource, setDataSource] =
    useState<AiBookTopicItem[]>(originalDatasource);

  const filterGroups = useMemo(() => {
    if (type === 'collection') {
      const filters = [contentFilters, orderFilters];
      const handler = (value: string[]) => {
        const [contentFilter, orderFilter] = value;
        const order = orderFilter as CollectionOrderFilterType;
        switch (contentFilter) {
          case CollectionFilterType.All: {
            return handleOrder(order, originalDatasource);
          }
          case CollectionFilterType.Knowledge: {
            const filterData = originalDatasource.filter(
              (item) => item.type === AiTopicType.Knowledge,
            );
            return handleOrder(order, filterData);
          }
          case CollectionFilterType.Topic: {
            const filterData = originalDatasource.filter(
              (item) => item.type !== AiTopicType.Knowledge,
            );
            return handleOrder(order, filterData);
          }
          default:
            return originalDatasource;
        }
      };
      return {
        filters,
        handler,
      };
    } else if (type === 'wrong') {
      const filters = [orderFilters];
      const handler = (value: string[]) => {
        const [orderFilter] = value;
        const order = orderFilter as CollectionOrderFilterType;
        return handleOrder(order, originalDatasource);
      };
      return {
        handler,
        filters,
      };
    } else {
      const handlerLevel = (value: TopicLevelType, data: AiBookTopicItem[]) => {
        if (value === TopicLevelType.All) {
          return data;
        } else {
          return data.filter((item) => (item.level as string) === value);
        }
      };

      const filters = [topicContentFilters, levelFilters];
      const handler = (value: string[]) => {
        const [contentFilter, levelFilter] = value;
        const level = levelFilter as TopicLevelType;

        switch (contentFilter) {
          case TopicFilterType.All: {
            return handlerLevel(level, originalDatasource);
          }
          case TopicFilterType.Normal: {
            const filterData = originalDatasource.filter((item) => {
              return (
                item.type !== AiTopicType.Listen &&
                item.type !== AiTopicType.Write
              );
            });
            return handlerLevel(level, filterData);
          }
          default: {
            const filterData = originalDatasource.filter(
              (item) => item.type === contentFilter,
            );
            return handlerLevel(level, filterData);
          }
        }
      };
      return {
        filters,
        handler,
      };
    }
  }, [originalDatasource, type]);

  const handleFilterChange = (values: string[]) => {
    const result = filterGroups.handler(values);
    setDataSource(result);
  };

  const handlePressItem = (topic: AiBookTopicItem) => {
    if (type === 'wrong') {
      setAiBookTakePhotoResult((prev) => {
        return {
          ...prev,
          showResultPages: new Set(prev.showResultPages.add(topic.pageId || 0)),
        };
      });
    }

    setAiBookInfo((prev) => {
      return {
        ...prev,
        pageId: topic.pageId,
        topicId: topic.itemId,
      };
    });
    router.push(routerMap.AiBookDetail);
  };

  const renderAnswerRect = (item: AiBookTopicItem) => {
    if (type !== 'wrong') return null;
    const rects = item.takePhotoWrongResult?.recResult || [];
    return rects.map((rect, index) => (
      <JglView
        style={{ minWidth: 0, minHeight: 0 }}
        pos='absolute'
        key={index}
        left={percent(rect?.left || 0 + (rect?.width || 0))}
        top={percent(rect?.top)}
        width={24}
        height={24}
      >
        {rect?.isRight ? (
          <Check size={24} color={'green'} />
        ) : (
          <X size={24} color={'red'} />
        )}
      </JglView>
    ));
  };

  const renderItem = ({ item }: { item: AiBookTopicItem }) => {
    let imageUrl = item.itemUrl ?? '';
    let rect;
    let overImageNode;
    let showTag = true;
    let maxHeight: number | undefined = 300;
    if (type === 'wrong') {
      const {
        imageUrl: url,
        left = 0,
        top = 0,
        width = 0,
        height = 0,
      } = item.takePhotoWrongResult || {};
      overImageNode = renderAnswerRect(item);
      imageUrl = url ?? '';
      rect = {
        top,
        left,
        width,
        height,
      };
      showTag = false;
      maxHeight = undefined;
    } else if (type === 'topics') {
      showTag = false;
    }
    return (
      <TopicListItem
        topic={item}
        onPress={() => handlePressItem(item)}
        imageUrl={imageUrl}
        rect={rect}
        contentOverImage={overImageNode}
        showTag={showTag}
        maxHeight={maxHeight}
      />
    );
  };

  return (
    <JglSafeArea>
      <JglYStack>
        <FilterBar
          groups={filterGroups.filters as any[]}
          onChange={handleFilterChange}
        />
        <FlatList
          data={dataSource}
          renderItem={renderItem}
          extraData={dataSource.length}
          keyExtractor={(item) => item.itemId?.toString() || ''}
          ListFooterComponent={<EmptyListFooter />}
        />
      </JglYStack>
    </JglSafeArea>
  );
};
