import {
  JglImage,
  JglSafeArea,
  JglText,
  JglTouchable,
  JglView,
  JglXStack,
  JglYStack,
} from '@jgl/ui-v4';
import { useRouterParams } from '@jgl/utils';
import { FlashList } from '@shopify/flash-list';
import { useCallback } from 'react';
import type { BookDTO } from '../api/dto';
import { readablePrice } from '../utils/PriceUtils';
import { navigateToWebBook } from '../utils/WebViewHelper';

/**
 * 扫码选择书单
 */
export const ScanChooseBookList = () => {
  const { bookListString = '[]' } = useRouterParams();

  const bookList = JSON.parse(bookListString) as Array<BookDTO>;

  const renderItem = useCallback(({ item }: { item: BookDTO }) => {
    const { thumbnails, name, description, author, publisher, authVal, authType } = item;
    let authStatus = '免费';
    let authStatusColor = '';
    switch (authType) {
      case 1: {
        authStatus = '学习卡';
        authStatusColor = '#4E76FF';
        break;
      }
      case 4: {
        authStatus = readablePrice({ price: authVal });
        authStatusColor = '#fa3b3b';
        break;
      }
      case 0: {
        authStatus = '免费';
        authStatusColor = '#01C293';
        break;
      }
      default: {
        // 默认和免费一样
        authStatus = '免费';
        authStatusColor = '#01C293';
        break;
      }
    }

    const onPress = () => {
      const { id, idSign } = item;
      navigateToWebBook({
        bookId: `${id}`,
        idSign,
      });
    };

    return (
      <JglTouchable onPress={onPress} padding={15} backgroundColor="white">
        <JglXStack width="100%">
          <JglImage
            width={88}
            height={128}
            mr={15}
            borderRadius={6}
            source={thumbnails ?? require('../assets/images/img_cover.png')}
            resizeMode={'stretch'}
            defaultImage={require('../assets/images/img_cover.png')}
          />
          <JglYStack flex={1} justifyContent="space-between">
            <JglYStack flex={1}>
              <JglText maxLines={2} fontSize={16} color="#333333">
                {name}
              </JglText>
              <JglText fontSize={14} color="#999999" maxLines={1} mt={5}>
                {`${author}/${publisher}`}
              </JglText>
              <JglText fontSize={14} color="#999999" maxLines={2} mt={5}>
                {description}
              </JglText>
            </JglYStack>
            <JglText fontSize={14} color={authStatusColor} mt={5} alignSelf="flex-end">
              {authStatus}
            </JglText>
          </JglYStack>
        </JglXStack>
      </JglTouchable>
    );
  }, []);

  const keyExtractor = useCallback((item: BookDTO) => {
    return item.id.toString();
  }, []);

  const renderSeparator = useCallback(() => {
    return <JglView height={10} />;
  }, []);

  return (
    <JglSafeArea>
      <FlashList
        data={bookList}
        keyExtractor={keyExtractor}
        ItemSeparatorComponent={renderSeparator}
        contentContainerStyle={{ paddingTop: 10 }}
        renderItem={renderItem}
      />
    </JglSafeArea>
  );
};
