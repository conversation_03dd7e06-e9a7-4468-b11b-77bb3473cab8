# frozen_string_literal: true

# https://gems.ruby-china.com 老是提示下面的错误，改为 https://rubygems.org 先测试看看
# Your bundle is locked to i18n (1.14.6), but that version could not be found in
# any of the sources listed in your Gemfile. If you haven't changed sources, that
# means the author of i18n (1.14.6) has removed it. You'll need to update your
# bundle to a version other than i18n (1.14.6) that hasn't been removed in order
# to install.

source "https://rubygems.org"
# source "https://gems.ruby-china.com"

git_source(:github) {|repo_name| "https://github.com/#{repo_name}" }

gem 'cocoapods', '~> 1.11', '>= 1.11.2'
# https://github.com/fastlane/fastlane/issues/21975#issuecomment-2075034563
gem "fastlane", "2.220.0"
gem "xcode-install", "2.6.6"
gem "graphicsmagick", "1.0.6"
gem 'cocoapods-project-hmap'
gem 'pry'
gem "xcodeproj", "1.21.0"
gem "activesupport", "= 7.0.8"
plugins_path = File.join(File.dirname(__FILE__), 'fastlane', 'Pluginfile')
eval_gemfile(plugins_path) if File.exist?(plugins_path)