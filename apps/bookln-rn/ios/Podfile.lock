PODS:
  - AliyunOSSiOS (2.10.22)
  - ASN1Decoder (1.10.0)
  - boost (1.76.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - DoubleConversion (1.1.6)
  - EASClient (0.6.0):
    - ExpoModulesCore
  - EXApplication (5.3.0):
    - ExpoModulesCore
  - EXAV (13.4.1):
    - ExpoModulesCore
    - ReactCommon/turbomodule/core
  - EXBarCodeScanner (12.7.0):
    - EXImageLoader
    - ExpoModulesCore
    - ZXingObjC/OneD
    - ZXingObjC/PDF417
  - EXCamera (13.6.0):
    - ExpoModulesCore
  - EXConstants (14.4.2):
    - ExpoModulesCore
  - EXFileSystem (15.4.5):
    - ExpoModulesCore
  - EXFont (11.4.0):
    - ExpoModulesCore
  - Exify (0.2.7):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - EXImageLoader (4.4.0):
    - ExpoModulesCore
    - React-Core
  - EXJSONUtils (0.7.1)
  - EXManifests (0.7.2):
    - ExpoModulesCore
  - Expo (49.0.21):
    - ExpoModulesCore
  - expo-dev-client (2.4.12):
    - EXManifests
    - expo-dev-launcher
    - expo-dev-menu
    - expo-dev-menu-interface
    - EXUpdatesInterface
  - expo-dev-launcher (2.4.14):
    - EXManifests
    - expo-dev-launcher/Main (= 2.4.14)
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-RCTAppDelegate
  - expo-dev-launcher/Main (2.4.14):
    - EXManifests
    - expo-dev-launcher/Unsafe
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-RCTAppDelegate
  - expo-dev-launcher/Unsafe (2.4.14):
    - EXManifests
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-RCTAppDelegate
  - expo-dev-menu (3.2.2):
    - expo-dev-menu/Main (= 3.2.2)
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - expo-dev-menu-interface (1.3.0)
  - expo-dev-menu/Main (3.2.2):
    - EXManifests
    - expo-dev-menu-interface
    - expo-dev-menu/Vendored
    - ExpoModulesCore
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - expo-dev-menu/SafeAreaView (3.2.2):
    - ExpoModulesCore
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - expo-dev-menu/Vendored (3.2.2):
    - expo-dev-menu/SafeAreaView
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - ExpoHead (0.0.15):
    - ExpoModulesCore
  - ExpoImage (1.8.1):
    - ExpoModulesCore
    - SDWebImage (~> 5.17.0)
    - SDWebImageAVIFCoder (~> 0.10.1)
    - SDWebImageSVGCoder (~> 1.7.0)
    - SDWebImageWebPCoder (~> 0.13.0)
  - ExpoImageManipulator (11.6.0):
    - EXImageLoader
    - ExpoModulesCore
  - ExpoImagePicker (14.5.0):
    - ExpoModulesCore
  - ExpoKeepAwake (12.3.0):
    - ExpoModulesCore
  - ExpoLinearGradient (12.5.0):
    - ExpoModulesCore
  - ExpoLocalization (14.3.0):
    - ExpoModulesCore
  - ExpoModulesCore (1.5.12):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - ReactCommon/turbomodule/core
  - ExpoScreenOrientation (6.2.0):
    - ExpoModulesCore
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - ExpoSystemUI (2.4.0):
    - ExpoModulesCore
  - ExpoWebBrowser (12.3.2):
    - ExpoModulesCore
  - EXSplashScreen (0.20.5):
    - ExpoModulesCore
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - EXStructuredHeaders (3.3.0)
  - EXUpdates (0.18.19):
    - ASN1Decoder (~> 1.8)
    - EASClient
    - EXManifests
    - ExpoModulesCore
    - EXStructuredHeaders
    - EXUpdatesInterface
    - RCT-Folly (= 2021.07.22.00)
    - ReachabilitySwift
    - React-Core
  - EXUpdatesInterface (0.10.1)
  - FBLazyVector (0.72.6)
  - FBReactNativeSpec (0.72.6):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.72.6)
    - RCTTypeSafety (= 0.72.6)
    - React-Core (= 0.72.6)
    - React-jsi (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
  - fmt (6.2.1)
  - glog (0.3.5)
  - hermes-engine (0.72.6):
    - hermes-engine/Pre-built (= 0.72.6)
  - hermes-engine/Pre-built (0.72.6)
  - ImageColors (2.3.0):
    - ExpoModulesCore
  - libaom (3.0.0):
    - libvmaf (>= 2.2.0)
  - libavif (0.11.1):
    - libavif/libaom (= 0.11.1)
  - libavif/core (0.11.1)
  - libavif/libaom (0.11.1):
    - libaom (>= 2.0.0)
    - libavif/core
  - libevent (2.1.12)
  - libvmaf (2.3.1)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - lottie-ios (4.3.4)
  - lottie-react-native (6.4.0):
    - lottie-ios (~> 4.3.3)
    - React-Core
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.72.6)
  - RCTTypeSafety (0.72.6):
    - FBLazyVector (= 0.72.6)
    - RCTRequired (= 0.72.6)
    - React-Core (= 0.72.6)
  - ReachabilitySwift (5.2.4)
  - React (0.72.6):
    - React-Core (= 0.72.6)
    - React-Core/DevSupport (= 0.72.6)
    - React-Core/RCTWebSocket (= 0.72.6)
    - React-RCTActionSheet (= 0.72.6)
    - React-RCTAnimation (= 0.72.6)
    - React-RCTBlob (= 0.72.6)
    - React-RCTImage (= 0.72.6)
    - React-RCTLinking (= 0.72.6)
    - React-RCTNetwork (= 0.72.6)
    - React-RCTSettings (= 0.72.6)
    - React-RCTText (= 0.72.6)
    - React-RCTVibration (= 0.72.6)
  - React-callinvoker (0.72.6)
  - React-Codegen (0.72.6):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.6)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.6)
    - React-Core/RCTWebSocket (= 0.72.6)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.6)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.6)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.6):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.6)
    - React-Codegen (= 0.72.6)
    - React-Core/CoreModulesHeaders (= 0.72.6)
    - React-jsi (= 0.72.6)
    - React-RCTBlob
    - React-RCTImage (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.6):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.6)
    - React-debug (= 0.72.6)
    - React-jsi (= 0.72.6)
    - React-jsinspector (= 0.72.6)
    - React-logger (= 0.72.6)
    - React-perflogger (= 0.72.6)
    - React-runtimeexecutor (= 0.72.6)
  - React-debug (0.72.6)
  - React-hermes (0.72.6):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.72.6)
    - React-jsi
    - React-jsiexecutor (= 0.72.6)
    - React-jsinspector (= 0.72.6)
    - React-perflogger (= 0.72.6)
  - React-jsi (0.72.6):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.6):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.6)
    - React-jsi (= 0.72.6)
    - React-perflogger (= 0.72.6)
  - React-jsinspector (0.72.6)
  - React-logger (0.72.6):
    - glog
  - react-native-alioss (0.2.5):
    - AliyunOSSiOS
    - React
    - React-Core
  - react-native-blob-util (0.19.4):
    - React-Core
  - react-native-blur (4.3.2):
    - React-Core
  - react-native-cameraroll (7.0.0):
    - React-Core
  - react-native-chivox (0.0.4):
    - React-Core
  - react-native-context-menu-view (1.19.0):
    - React
  - react-native-graceful-exit (0.1.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-netinfo (11.4.1):
    - React-Core
  - react-native-pager-view (6.0.2):
    - React-Core
  - react-native-pdf (6.7.1):
    - React-Core
  - react-native-safe-area-context (4.6.3):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - ReactCommon/turbomodule/core
  - react-native-skia (0.1.234):
    - RCT-Folly (= 2021.07.22.00)
    - React
    - React-callinvoker
    - React-Core
  - react-native-user-agent (2.3.1):
    - React
  - react-native-view-shot (3.8.0):
    - React-Core
  - react-native-voice (3.2.4):
    - React-Core
  - react-native-webview (13.6.3):
    - React-Core
  - React-NativeModulesApple (0.72.6):
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.6)
  - React-RCTActionSheet (0.72.6):
    - React-Core/RCTActionSheetHeaders (= 0.72.6)
  - React-RCTAnimation (0.72.6):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.6)
    - React-Codegen (= 0.72.6)
    - React-Core/RCTAnimationHeaders (= 0.72.6)
    - React-jsi (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
  - React-RCTAppDelegate (0.72.6):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.6):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.6)
    - React-Core/RCTBlobHeaders (= 0.72.6)
    - React-Core/RCTWebSocket (= 0.72.6)
    - React-jsi (= 0.72.6)
    - React-RCTNetwork (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
  - React-RCTImage (0.72.6):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.6)
    - React-Codegen (= 0.72.6)
    - React-Core/RCTImageHeaders (= 0.72.6)
    - React-jsi (= 0.72.6)
    - React-RCTNetwork (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
  - React-RCTLinking (0.72.6):
    - React-Codegen (= 0.72.6)
    - React-Core/RCTLinkingHeaders (= 0.72.6)
    - React-jsi (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
  - React-RCTNetwork (0.72.6):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.6)
    - React-Codegen (= 0.72.6)
    - React-Core/RCTNetworkHeaders (= 0.72.6)
    - React-jsi (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
  - React-RCTSettings (0.72.6):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.6)
    - React-Codegen (= 0.72.6)
    - React-Core/RCTSettingsHeaders (= 0.72.6)
    - React-jsi (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
  - React-RCTText (0.72.6):
    - React-Core/RCTTextHeaders (= 0.72.6)
  - React-RCTVibration (0.72.6):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.6)
    - React-Core/RCTVibrationHeaders (= 0.72.6)
    - React-jsi (= 0.72.6)
    - ReactCommon/turbomodule/core (= 0.72.6)
  - React-rncore (0.72.6)
  - React-runtimeexecutor (0.72.6):
    - React-jsi (= 0.72.6)
  - React-runtimescheduler (0.72.6):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.6):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.6):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.6)
    - React-cxxreact (= 0.72.6)
    - React-jsi (= 0.72.6)
    - React-logger (= 0.72.6)
    - React-perflogger (= 0.72.6)
  - ReactCommon/turbomodule/core (0.72.6):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.6)
    - React-cxxreact (= 0.72.6)
    - React-jsi (= 0.72.6)
    - React-logger (= 0.72.6)
    - React-perflogger (= 0.72.6)
  - rn-expo-updates-helper (0.1.13):
    - React-Core
  - rn-iflytek (0.1.6):
    - React-Core
  - rn-suntone (0.0.8):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - rn-umeng (1.0.16):
    - React
    - UMCCommonLog
    - UMCommon
    - UMDevice
  - RNAudioRecorderPlayer (3.6.12):
    - React-Core
  - RNCAsyncStorage (1.18.2):
    - React-Core
  - RNCClipboard (1.12.1):
    - React-Core
  - RNDeviceInfo (10.12.1):
    - React-Core
  - RNFlashList (1.6.3):
    - React-Core
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.13.4):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNIap (12.16.2):
    - React-Core
  - RNPermissions (3.10.1):
    - React-Core
  - RNReactNativeHapticFeedback (2.2.0):
    - React-Core
  - RNReanimated (3.6.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - ReactCommon/turbomodule/core
  - RNScreens (3.27.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNSentry (6.15.1):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-hermes
    - Sentry/HybridSDK (= 8.52.1)
  - RNShare (10.0.2):
    - React-Core
  - RNSound (0.11.2):
    - React-Core
    - RNSound/Core (= 0.11.2)
  - RNSound/Core (0.11.2):
    - React-Core
  - RNSVG (13.14.0):
    - React-Core
  - RTNWechat (1.0.21):
    - React-Core
    - WechatOpenSDK
  - SDWebImage (5.17.0):
    - SDWebImage/Core (= 5.17.0)
  - SDWebImage/Core (5.17.0)
  - SDWebImageAVIFCoder (0.10.1):
    - libavif (>= 0.11.0)
    - SDWebImage (~> 5.10)
  - SDWebImageSVGCoder (1.7.0):
    - SDWebImage/Core (~> 5.6)
  - SDWebImageWebPCoder (0.13.0):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - Sentry/HybridSDK (8.52.1)
  - SocketRocket (0.6.1)
  - ToastHybrid (2.5.0):
    - React-Core
  - UMCCommonLog (2.0.2)
  - UMCommon (7.5.2):
    - UMDevice
  - UMDevice (3.4.0)
  - WechatOpenSDK (2.0.4)
  - Yoga (1.14.0)
  - ZXingObjC/Core (3.6.9)
  - ZXingObjC/OneD (3.6.9):
    - ZXingObjC/Core
  - ZXingObjC/PDF417 (3.6.9):
    - ZXingObjC/Core

DEPENDENCIES:
  - boost (from `../../../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../../../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EASClient (from `../../../node_modules/expo-eas-client/ios`)
  - EXApplication (from `../../../node_modules/expo-application/ios`)
  - EXAV (from `../../../node_modules/expo-av/ios`)
  - EXBarCodeScanner (from `../../../node_modules/expo-barcode-scanner/ios`)
  - EXCamera (from `../../../node_modules/expo-camera/ios`)
  - EXConstants (from `../../../node_modules/expo-constants/ios`)
  - EXFileSystem (from `../../../node_modules/expo-file-system/ios`)
  - EXFont (from `../../../node_modules/expo-font/ios`)
  - "Exify (from `../../../node_modules/@lodev09/react-native-exify`)"
  - EXImageLoader (from `../../../node_modules/expo-image-loader/ios`)
  - EXJSONUtils (from `../../../node_modules/expo-json-utils/ios`)
  - EXManifests (from `../../../node_modules/expo-manifests/ios`)
  - Expo (from `../../../node_modules/expo`)
  - expo-dev-client (from `../../../node_modules/expo-dev-client/ios`)
  - expo-dev-launcher (from `../../../node_modules/expo-dev-launcher`)
  - expo-dev-menu (from `../../../node_modules/expo-dev-menu`)
  - expo-dev-menu-interface (from `../../../node_modules/expo-dev-menu-interface/ios`)
  - ExpoHead (from `../../../node_modules/expo-head/ios`)
  - ExpoImage (from `../../../node_modules/expo-image/ios`)
  - ExpoImageManipulator (from `../../../node_modules/expo-image-manipulator/ios`)
  - ExpoImagePicker (from `../../../node_modules/expo-image-picker/ios`)
  - ExpoKeepAwake (from `../../../node_modules/expo-keep-awake/ios`)
  - ExpoLinearGradient (from `../../../node_modules/expo-linear-gradient/ios`)
  - ExpoLocalization (from `../../../node_modules/expo-localization/ios`)
  - ExpoModulesCore (from `../../../node_modules/expo-modules-core`)
  - ExpoScreenOrientation (from `../../../node_modules/expo-screen-orientation/ios`)
  - ExpoSystemUI (from `../../../node_modules/expo-system-ui/ios`)
  - ExpoWebBrowser (from `../../../node_modules/expo-web-browser/ios`)
  - EXSplashScreen (from `../../../node_modules/expo-splash-screen/ios`)
  - EXStructuredHeaders (from `../../../node_modules/expo-structured-headers/ios`)
  - EXUpdates (from `../../../node_modules/expo-updates/ios`)
  - EXUpdatesInterface (from `../../../node_modules/expo-updates-interface/ios`)
  - FBLazyVector (from `../../../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../../../node_modules/react-native/React/FBReactNativeSpec`)
  - glog (from `../../../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../../../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - ImageColors (from `../../../node_modules/react-native-image-colors/ios`)
  - libevent (~> 2.1.12)
  - lottie-react-native (from `../../../node_modules/lottie-react-native`)
  - RCT-Folly (from `../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../../../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../../../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../../../node_modules/react-native/`)
  - React-callinvoker (from `../../../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../../../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../../../node_modules/react-native/`)
  - React-CoreModules (from `../../../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../../../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../../../node_modules/react-native/ReactCommon/react/debug`)
  - React-hermes (from `../../../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../../../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../../../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../../../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../../../node_modules/react-native/ReactCommon/logger`)
  - react-native-alioss (from `../../../node_modules/rn-alioss`)
  - react-native-blob-util (from `../../../node_modules/react-native-blob-util`)
  - "react-native-blur (from `../../../node_modules/@react-native-community/blur`)"
  - "react-native-cameraroll (from `../../../node_modules/@react-native-camera-roll/camera-roll`)"
  - "react-native-chivox (from `../../../node_modules/@yunti/react-native-chivox`)"
  - react-native-context-menu-view (from `../../../node_modules/react-native-context-menu-view`)
  - "react-native-graceful-exit (from `../../../node_modules/@sleiv/react-native-graceful-exit`)"
  - "react-native-netinfo (from `../../../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../../../node_modules/react-native-pager-view`)
  - react-native-pdf (from `../../../node_modules/react-native-pdf`)
  - react-native-safe-area-context (from `../../../node_modules/react-native-safe-area-context`)
  - "react-native-skia (from `../../../node_modules/@shopify/react-native-skia`)"
  - react-native-user-agent (from `../../../node_modules/react-native-user-agent`)
  - react-native-view-shot (from `../../../node_modules/react-native-view-shot`)
  - "react-native-voice (from `../../../node_modules/@react-native-voice/voice`)"
  - react-native-webview (from `../../../node_modules/react-native-webview`)
  - React-NativeModulesApple (from `../../../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../../../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../../../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../../../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../../../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../../../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../../../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../../../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../../../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../../../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../../../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../../../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../../../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../../../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../../../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../../../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../../../node_modules/react-native/ReactCommon`)
  - "rn-expo-updates-helper (from `../../../node_modules/@yunti-private/rn-expo-updates-helper`)"
  - "rn-iflytek (from `../../../node_modules/@yunti-private/rn-iflytek`)"
  - "rn-suntone (from `../../../node_modules/@yunti-private/rn-suntone`)"
  - "rn-umeng (from `../../../node_modules/@yunti-private/rn-umeng`)"
  - RNAudioRecorderPlayer (from `../../../node_modules/react-native-audio-recorder-player`)
  - "RNCAsyncStorage (from `../../../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../../../node_modules/@react-native-clipboard/clipboard`)"
  - RNDeviceInfo (from `../../../node_modules/react-native-device-info`)
  - "RNFlashList (from `../../../node_modules/@shopify/flash-list`)"
  - RNFS (from `../../../node_modules/react-native-fs`)
  - RNGestureHandler (from `../../../node_modules/react-native-gesture-handler`)
  - RNIap (from `../../../node_modules/react-native-iap`)
  - RNPermissions (from `../../../node_modules/react-native-permissions`)
  - RNReactNativeHapticFeedback (from `../../../node_modules/react-native-haptic-feedback`)
  - RNReanimated (from `../../../node_modules/react-native-reanimated`)
  - RNScreens (from `../../../node_modules/react-native-screens`)
  - "RNSentry (from `../../../node_modules/@sentry/react-native`)"
  - RNShare (from `../../../node_modules/react-native-share`)
  - RNSound (from `../../../node_modules/react-native-sound`)
  - RNSVG (from `../../../node_modules/react-native-svg`)
  - RTNWechat (from `../../../node_modules/native-wechat`)
  - ToastHybrid (from `../../../node_modules/react-native-toast-hybrid`)
  - Yoga (from `../../../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - AliyunOSSiOS
    - ASN1Decoder
    - fmt
    - libaom
    - libavif
    - libevent
    - libvmaf
    - libwebp
    - lottie-ios
    - ReachabilitySwift
    - SDWebImage
    - SDWebImageAVIFCoder
    - SDWebImageSVGCoder
    - SDWebImageWebPCoder
    - Sentry
    - SocketRocket
    - UMCCommonLog
    - UMCommon
    - UMDevice
    - WechatOpenSDK
    - ZXingObjC

EXTERNAL SOURCES:
  boost:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../../../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EASClient:
    :path: "../../../node_modules/expo-eas-client/ios"
  EXApplication:
    :path: "../../../node_modules/expo-application/ios"
  EXAV:
    :path: "../../../node_modules/expo-av/ios"
  EXBarCodeScanner:
    :path: "../../../node_modules/expo-barcode-scanner/ios"
  EXCamera:
    :path: "../../../node_modules/expo-camera/ios"
  EXConstants:
    :path: "../../../node_modules/expo-constants/ios"
  EXFileSystem:
    :path: "../../../node_modules/expo-file-system/ios"
  EXFont:
    :path: "../../../node_modules/expo-font/ios"
  Exify:
    :path: "../../../node_modules/@lodev09/react-native-exify"
  EXImageLoader:
    :path: "../../../node_modules/expo-image-loader/ios"
  EXJSONUtils:
    :path: "../../../node_modules/expo-json-utils/ios"
  EXManifests:
    :path: "../../../node_modules/expo-manifests/ios"
  Expo:
    :path: "../../../node_modules/expo"
  expo-dev-client:
    :path: "../../../node_modules/expo-dev-client/ios"
  expo-dev-launcher:
    :path: "../../../node_modules/expo-dev-launcher"
  expo-dev-menu:
    :path: "../../../node_modules/expo-dev-menu"
  expo-dev-menu-interface:
    :path: "../../../node_modules/expo-dev-menu-interface/ios"
  ExpoHead:
    :path: "../../../node_modules/expo-head/ios"
  ExpoImage:
    :path: "../../../node_modules/expo-image/ios"
  ExpoImageManipulator:
    :path: "../../../node_modules/expo-image-manipulator/ios"
  ExpoImagePicker:
    :path: "../../../node_modules/expo-image-picker/ios"
  ExpoKeepAwake:
    :path: "../../../node_modules/expo-keep-awake/ios"
  ExpoLinearGradient:
    :path: "../../../node_modules/expo-linear-gradient/ios"
  ExpoLocalization:
    :path: "../../../node_modules/expo-localization/ios"
  ExpoModulesCore:
    :path: "../../../node_modules/expo-modules-core"
  ExpoScreenOrientation:
    :path: "../../../node_modules/expo-screen-orientation/ios"
  ExpoSystemUI:
    :path: "../../../node_modules/expo-system-ui/ios"
  ExpoWebBrowser:
    :path: "../../../node_modules/expo-web-browser/ios"
  EXSplashScreen:
    :path: "../../../node_modules/expo-splash-screen/ios"
  EXStructuredHeaders:
    :path: "../../../node_modules/expo-structured-headers/ios"
  EXUpdates:
    :path: "../../../node_modules/expo-updates/ios"
  EXUpdatesInterface:
    :path: "../../../node_modules/expo-updates-interface/ios"
  FBLazyVector:
    :path: "../../../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../../../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../../../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2023-08-07-RNv0.72.4-813b2def12bc9df02654b3e3653ae4a68d0572e0
  ImageColors:
    :path: "../../../node_modules/react-native-image-colors/ios"
  lottie-react-native:
    :path: "../../../node_modules/lottie-react-native"
  RCT-Folly:
    :podspec: "../../../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../../../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../../../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../../../node_modules/react-native/"
  React-callinvoker:
    :path: "../../../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../../../node_modules/react-native/"
  React-CoreModules:
    :path: "../../../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../../../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../../../node_modules/react-native/ReactCommon/react/debug"
  React-hermes:
    :path: "../../../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../../../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../../../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../../../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../../../node_modules/react-native/ReactCommon/logger"
  react-native-alioss:
    :path: "../../../node_modules/rn-alioss"
  react-native-blob-util:
    :path: "../../../node_modules/react-native-blob-util"
  react-native-blur:
    :path: "../../../node_modules/@react-native-community/blur"
  react-native-cameraroll:
    :path: "../../../node_modules/@react-native-camera-roll/camera-roll"
  react-native-chivox:
    :path: "../../../node_modules/@yunti/react-native-chivox"
  react-native-context-menu-view:
    :path: "../../../node_modules/react-native-context-menu-view"
  react-native-graceful-exit:
    :path: "../../../node_modules/@sleiv/react-native-graceful-exit"
  react-native-netinfo:
    :path: "../../../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../../../node_modules/react-native-pager-view"
  react-native-pdf:
    :path: "../../../node_modules/react-native-pdf"
  react-native-safe-area-context:
    :path: "../../../node_modules/react-native-safe-area-context"
  react-native-skia:
    :path: "../../../node_modules/@shopify/react-native-skia"
  react-native-user-agent:
    :path: "../../../node_modules/react-native-user-agent"
  react-native-view-shot:
    :path: "../../../node_modules/react-native-view-shot"
  react-native-voice:
    :path: "../../../node_modules/@react-native-voice/voice"
  react-native-webview:
    :path: "../../../node_modules/react-native-webview"
  React-NativeModulesApple:
    :path: "../../../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../../../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../../../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../../../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../../../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../../../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../../../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../../../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../../../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../../../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../../../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../../../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../../../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../../../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../../../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../../../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../../../node_modules/react-native/ReactCommon"
  rn-expo-updates-helper:
    :path: "../../../node_modules/@yunti-private/rn-expo-updates-helper"
  rn-iflytek:
    :path: "../../../node_modules/@yunti-private/rn-iflytek"
  rn-suntone:
    :path: "../../../node_modules/@yunti-private/rn-suntone"
  rn-umeng:
    :path: "../../../node_modules/@yunti-private/rn-umeng"
  RNAudioRecorderPlayer:
    :path: "../../../node_modules/react-native-audio-recorder-player"
  RNCAsyncStorage:
    :path: "../../../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../../../node_modules/@react-native-clipboard/clipboard"
  RNDeviceInfo:
    :path: "../../../node_modules/react-native-device-info"
  RNFlashList:
    :path: "../../../node_modules/@shopify/flash-list"
  RNFS:
    :path: "../../../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../../../node_modules/react-native-gesture-handler"
  RNIap:
    :path: "../../../node_modules/react-native-iap"
  RNPermissions:
    :path: "../../../node_modules/react-native-permissions"
  RNReactNativeHapticFeedback:
    :path: "../../../node_modules/react-native-haptic-feedback"
  RNReanimated:
    :path: "../../../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../../../node_modules/react-native-screens"
  RNSentry:
    :path: "../../../node_modules/@sentry/react-native"
  RNShare:
    :path: "../../../node_modules/react-native-share"
  RNSound:
    :path: "../../../node_modules/react-native-sound"
  RNSVG:
    :path: "../../../node_modules/react-native-svg"
  RTNWechat:
    :path: "../../../node_modules/native-wechat"
  ToastHybrid:
    :path: "../../../node_modules/react-native-toast-hybrid"
  Yoga:
    :path: "../../../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AliyunOSSiOS: b46648fd78909a567e3743fe94183748a407b175
  ASN1Decoder: 91cb1d781b5a178ea7375b2f1519e2bdaaa4c427
  boost: 7dcd2de282d72e344012f7d6564d024930a6a440
  BVLinearGradient: cb006ba232a1f3e4f341bb62c42d1098c284da70
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  EASClient: 847b2cedcc7f85900ebd54c53531b88aa41873d1
  EXApplication: a38b05670c49d81d0fe0c8b9dc119d568000f3b9
  EXAV: d1f0ef70837bc21032001aa2c7894f86e2079ae1
  EXBarCodeScanner: 5764b032e2d61d524bd68589a827e63381af014a
  EXCamera: 8e1442a475a80432936a47c755740838e838a16f
  EXConstants: e7d8d1bec9a20242b4f92a9d66967c3904e0dcd0
  EXFileSystem: 1aeed803248e2b62c5cde8b8d8c6cb1525fc40c1
  EXFont: aa39b3f790e2b3188986ac5e8684cf6003c00a18
  Exify: c1e35b35f1877874b0935f16534dfea0bf26d53c
  EXImageLoader: 0bf290da061ebd43c1dc57a481bb83a210eee16c
  EXJSONUtils: 6802be4282d42b97c51682468ddc1026a06f8276
  EXManifests: bb8592a34f5339c860b6c4bc71ad583d490374fb
  Expo: 999421a55388ebad4009aeada631419d79d65793
  expo-dev-client: b381837e34633ddee99e5ed96a0dfb4fd66f991e
  expo-dev-launcher: 626b40905b359cd831d8e3919467003e76f14763
  expo-dev-menu: 7a85d78757d228e04c35b7883013ce7dbc86a78e
  expo-dev-menu-interface: 22ea9639725e26dfebcb16ea631ddec660827374
  ExpoHead: 379df400e984bd1f0ed908e9903320bbaab55019
  ExpoImage: 0ccc20b3775d1852f4bad6ba273807d5016d5280
  ExpoImageManipulator: 278079dc1d2b776d677882671830af3502315e2a
  ExpoImagePicker: 5b79d3ea005ec953a21e41c28b4c8312f101ea0a
  ExpoKeepAwake: 8ab1087501f5ccb91146447756b787575b13f13e
  ExpoLinearGradient: 836004bed26986acf9c015634bd6046e569cd908
  ExpoLocalization: 7c2a89d41bba493cb4220bc8d1e30ba4f0219f93
  ExpoModulesCore: 7a0ab2761761977aa79ccbac0a56887b98cebd5b
  ExpoScreenOrientation: 627c23d29e481d31cff252c4355d4a119221eaca
  ExpoSystemUI: 8bd8809b1de7bf6a3a7f019306180a4b9b1c855a
  ExpoWebBrowser: 025c51f93c6a04beb169388877918de64ccae171
  EXSplashScreen: 84a7d9a965a2203e138528326fc3a511818e3fd1
  EXStructuredHeaders: 324cc3130571d2696357fafd8be7fd9a0b5fdf6e
  EXUpdates: 46fc021f5368f542dd0a409a1fd88543b76fb1e0
  EXUpdatesInterface: 82ed48d417cdcd376c12ca1c2ce390d35500bed6
  FBLazyVector: 748c0ef74f2bf4b36cfcccf37916806940a64c32
  FBReactNativeSpec: edf711966f1606f688736450f0648d5441a0a984
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  hermes-engine: 8057e75cfc1437b178ac86c8654b24e7fead7f60
  ImageColors: 4eff2e3e9c3192904109a860de3b46a99ec3ab80
  libaom: 144606b1da4b5915a1054383c3a4459ccdb3c661
  libavif: 84bbb62fb232c3018d6f1bab79beea87e35de7b7
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  libvmaf: 27f523f1e63c694d14d534cd0fddd2fab0ae8711
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  lottie-ios: 3d98679b41fa6fd6aff2352b3953dbd3df8a397e
  lottie-react-native: 01cea7245d38dd4791fc9d8ee0a3c4392bdc5047
  RCT-Folly: 8dc08ca5a393b48b1c523ab6220dfdcc0fe000ad
  RCTRequired: 28469809442eb4eb5528462705f7d852948c8a74
  RCTTypeSafety: e9c6c409fca2cc584e5b086862d562540cb38d29
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  React: 769f469909b18edfe934f0539fffb319c4c61043
  React-callinvoker: e48ce12c83706401251921896576710d81e54763
  React-Codegen: 09c4a6d0a6ba9f7d008ac3a5e7c202899178ce20
  React-Core: 4d03a504696413bc97ef8a9085f642555867eed8
  React-CoreModules: 93be55ffed8b41ed664b51072498a23d9261410b
  React-cxxreact: ac5508f2887a7eeb93dd9675e2cfc2b20b379936
  React-debug: 238501490155574ae9f3f8dd1c74330eba30133e
  React-hermes: 2dc01922de59f0e21230aedc51d5adef7d7f76f6
  React-jsi: 11012bf34366812063bd16ca739ca219af26680f
  React-jsiexecutor: 5b6dd84e7461ae0bd999922cafe1fe3f98ced073
  React-jsinspector: 194e32c6aab382d88713ad3dd0025c5f5c4ee072
  React-logger: 39440454dfd719978689203a9d18b94e98de09eb
  react-native-alioss: f32d6ae9c61418d033320b56c7172be24ef59852
  react-native-blob-util: 25a51bfa8a5003655255920d45fb21ee3ff3ed2a
  react-native-blur: c91c10c67be1f096415e63925640baa59e2132d2
  react-native-cameraroll: 34bae3865ee158ca5b5ae61bb33f6438aa06868e
  react-native-chivox: 694affe93fc26b3af1f44aed836d1a1f7196323e
  react-native-context-menu-view: 3a8fb510448efa9d477f645dafa889ef1c78daaa
  react-native-graceful-exit: c7d1b8b6e4e79881e8afb14adadce467e0bde639
  react-native-netinfo: cec9c4e86083cb5b6aba0e0711f563e2fbbff187
  react-native-pager-view: 11b995ca068c6b27ccdb477b0f8d5f163cda37ea
  react-native-pdf: b0380c1ad48f30943665cd783c4c305e341c0822
  react-native-safe-area-context: f998d7c030d9c89b0a745f8466f1f988478d822e
  react-native-skia: 6fc0ee3e0dd1201d9907ec2a03fee6ffdb1c0c63
  react-native-user-agent: 1085b87cfc237fea6440a217f9d9593a0ae0e833
  react-native-view-shot: d1a701eb0719c6dccbd20b4bb43b1069f304cb70
  react-native-voice: 908a0eba96c8c3d643e4f98b7232c6557d0a6f9c
  react-native-webview: 508a7967877321541cc7ff68740f2c294243415c
  React-NativeModulesApple: 90508a0d94b0b66bb2ba14bc3bef65a00f5a8efb
  React-perflogger: e3596db7e753f51766bceadc061936ef1472edc3
  React-RCTActionSheet: 17ab132c748b4471012abbcdcf5befe860660485
  React-RCTAnimation: c8bbaab62be5817d2a31c36d5f2571e3f7dcf099
  React-RCTAppDelegate: c06dfd41e63ef630220dc1892f77c277bba84a98
  React-RCTBlob: 56f3c13b80c8b415ad1f81881fcab44904abe07c
  React-RCTImage: 670a3486b532292649b1aef3ffddd0b495a5cee4
  React-RCTLinking: bd7ab853144aed463903237e615fd91d11b4f659
  React-RCTNetwork: be86a621f3e4724758f23ad1fdce32474ab3d829
  React-RCTSettings: 4f3a29a6d23ffa639db9701bc29af43f30781058
  React-RCTText: adde32164a243103aaba0b1dc7b0a2599733873e
  React-RCTVibration: 6bd85328388ac2e82ae0ca11afe48ad5555b483a
  React-rncore: b53388d8e4f95d455af5f26425edd3a56a82b15f
  React-runtimeexecutor: 57d85d942862b08f6d15441a0badff2542fd233c
  React-runtimescheduler: 8d48e9f0cf62099f501fcd52234240006fa12c8f
  React-utils: ee4e6bf16d6882500500d517d36f9035874c5f6a
  ReactCommon: 2060ee7b68e71d23849968f6406550ba2df71bdb
  rn-expo-updates-helper: 8063c06786262ae242b5fedee857f8ac0c3a1b51
  rn-iflytek: 0f8e9bc0634a3bbd674c26d3840a3e2dba911e2a
  rn-suntone: 8cbb2a91cfd00d844fd9209f922e07427d534b0c
  rn-umeng: f966ae5e8b5d4d545db50b7862d512a4fec4c59f
  RNAudioRecorderPlayer: 11df0c7b614e9767ef24d896465c3a758c592de7
  RNCAsyncStorage: d74501eeac0371fb58b3643704451a11b760af80
  RNCClipboard: 9d33327413fe87aa0c8ef83e1b9a939d7c4f535d
  RNDeviceInfo: cdade9630e4ff935b0eb1ec024b3c8f65db192ae
  RNFlashList: 1076a3fb7c4608a8cdf265f0783592b8fc41b6a7
  RNFS: 89de7d7f4c0f6bafa05343c578f61118c8282ed8
  RNGestureHandler: f3902a2694346e4836112a2ef5524b3dcf408678
  RNIap: f94647b3a3dbd5fa08ad7ed847eb588aa5fe95fe
  RNPermissions: 3891f23b684cda9f85f36f7ff54b9c9ac3021fac
  RNReactNativeHapticFeedback: a6fb5b7a981683bf58af43e3fb827d4b7ed87f83
  RNReanimated: b09dd08f7b1e1f7bfdfc8a32947da1cb279a03d6
  RNScreens: c04e015e917ce64d0983974d8efb8fa1dc1ddc56
  RNSentry: ca915093992ceb02c6ac1a5566e150dca974e161
  RNShare: 01c717e457489779d16293d2afef36995aadd001
  RNSound: 314cc5226453ef4a3314a196c65e8a65e5106a7b
  RNSVG: 4cab00c621b328a4a2fedaaedc15d8822216723e
  RTNWechat: 8b977a74f9b8c98618d718bb1fd956f36389b131
  SDWebImage: 750adf017a315a280c60fde706ab1e552a3ae4e9
  SDWebImageAVIFCoder: 8348fef6d0ec69e129c66c9fe4d74fbfbf366112
  SDWebImageSVGCoder: 15a300a97ec1c8ac958f009c02220ac0402e936c
  SDWebImageWebPCoder: af09429398d99d524cae2fe00f6f0f6e491ed102
  Sentry: 2cbbe3592f30050c60e916c63c7f5a2fa584005e
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  ToastHybrid: 671637d31367096dd0850f06e78e6e1dcf2f8bfd
  UMCCommonLog: bea707e50c85cef4b0eb47cc5c7226bb843245ca
  UMCommon: 72513a01ebca2dead52f2112b4d7c6196dbbe412
  UMDevice: dcdf7ec167387837559d149fbc7d793d984faf82
  WechatOpenSDK: 290989072e87b79d52225a01bb4e5e58f6f88593
  Yoga: b76f1acfda8212aa16b7e26bcce3983230c82603
  ZXingObjC: 8898711ab495761b2dbbdec76d90164a6d7e14c5

PODFILE CHECKSUM: 2284c470bd540a6eb2c94bbf3c40e79e553c3344

COCOAPODS: 1.12.1
