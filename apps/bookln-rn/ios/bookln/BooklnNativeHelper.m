//
//  BooklnNativeHelper.m
//  bookln
//
//  Created by leejun<PERSON> on 2024/5/7.
//

#import "BooklnNativeHelper.h"
#import "BooklnLaunchCount.h"
@implementation BooklnNativeHelper
RCT_EXPORT_MODULE();


RCT_EXPORT_METHOD(readApptextJsonWithCallback:(RCTResponseSenderBlock)callback) {
  // TODO: jaybo 先改为只读配置文件，不从宏里面读取
  [self copyAppExtFileOnFirstLaunchOfCurrentBuild];

  NSString *jsonStringFromFile = [self loadJsonStringFromConfigFile:[self fileNameAppExt]];
  callback(@[[NSNull null], jsonStringFromFile]);
}

RCT_EXPORT_METHOD(readBaseInfoJsonWithCallback:(RCTResponseSenderBlock)callback) {
  // TODO: jaybo 先改为只读配置文件，不从宏里面读取
  NSString *jsonStringFromFile = [self loadJsonStringFromConfigFile:[self fileNameBaseInfo]];
  callback(@[[NSNull null], jsonStringFromFile]);
}

RCT_EXPORT_METHOD(syncAppextJson:(NSString *)appextJson
                  resolve:(RCTPromiseResolveBlock)resolve
                  reject:(RCTPromiseRejectBlock)reject) {
  NSString *path = [[NSBundle mainBundle] pathForResource:[self fileNameAppExt] ofType:@"json"];
  BOOL result = [appextJson writeToFile:path atomically:YES encoding:NSUTF8StringEncoding error:nil];
  
  NSString *libPath = [NSSearchPathForDirectoriesInDomains(NSLibraryDirectory, NSUserDomainMask, YES).lastObject stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.json", [self fileNameAppExt]]];
  [appextJson writeToFile:libPath atomically:YES encoding:NSUTF8StringEncoding error:nil];
  resolve(NULL);
}

RCT_EXPORT_METHOD(readNotificationFromAppGroupWithCallback:(RCTResponseSenderBlock)callback) {
  NSString *appGroup = [self getAppGroupName];
  NSString *key = @"notification";
  NSUserDefaults *userDefault = [[NSUserDefaults alloc] initWithSuiteName:appGroup];
  NSArray *notifications = [userDefault valueForKey:key];
  if (!notifications) {
    notifications = @[];
  }
  callback(@[[NSNull null], notifications]);
}

RCT_EXPORT_METHOD(clearLocalNotification) {
  NSString *appGroup = [self getAppGroupName]; ;
  NSString *key = @"notification";
  NSUserDefaults *userDefault = [[NSUserDefaults alloc] initWithSuiteName:appGroup];
  [userDefault setValue:@[] forKey:key];
  [userDefault synchronize];
}

- (NSString *)getAppGroupName {
  NSString *bundleId = [[NSBundle mainBundle] infoDictionary][@"CFBundleIdentifier"];
  NSString *appGroup = [@"group." stringByAppendingString:bundleId];
  return appGroup;
}

#pragma mark - Public
- (nullable NSDictionary<NSString *, id> *)readBaseInfoFile {
  NSString *jsonStringFromFile = [self loadJsonStringFromConfigFile:[self fileNameBaseInfo]];
  if (jsonStringFromFile) {
    NSData *objectData = [jsonStringFromFile dataUsingEncoding:NSUTF8StringEncoding];
    if (objectData) {
      NSDictionary *json = [NSJSONSerialization JSONObjectWithData:objectData
                                                           options:NSJSONReadingMutableContainers
                                                             error:nil];
      return json;
    }
  }

  return nil;
}

#pragma mark - Private

- (NSString *)fileNameBaseInfo {
  return @"baseInfo";
}

- (NSString *)fileNameAppExt {
  return @"appext";
}

- (void)copyAppExtFileOnFirstLaunchOfCurrentBuild {
  static dispatch_once_t copyAppExtFileToken;
  dispatch_once(&copyAppExtFileToken, ^{
    if ([[BooklnLaunchCount new] currentBuild] != 1) {
      return;
    }

    NSString *fileName = [self fileNameAppExt];
    NSString *bundlePath = [[NSBundle mainBundle] pathForResource:fileName ofType:@"json"];
    NSString *appExtJsonString = [NSString stringWithContentsOfFile:bundlePath encoding:NSUTF8StringEncoding error:nil];

    NSString *libPath = [NSSearchPathForDirectoriesInDomains(NSLibraryDirectory, NSUserDomainMask, YES).lastObject stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.json", fileName]];
    [appExtJsonString writeToFile:libPath atomically:YES encoding:NSUTF8StringEncoding error:nil];
  });
}

- (nullable NSString *)loadJsonStringFromConfigFile:(NSString *)fileName {
  NSString *pathComponent = [NSString stringWithFormat:@"%@.json", fileName];
  NSString *libPath = [NSSearchPathForDirectoriesInDomains(NSLibraryDirectory, NSUserDomainMask, YES).lastObject stringByAppendingPathComponent:pathComponent];
  NSFileManager *fm = [NSFileManager defaultManager];
  if ([fm fileExistsAtPath:libPath]) {
    NSString *jsonString = [NSString stringWithContentsOfFile:libPath encoding:NSUTF8StringEncoding error:nil];
    if (jsonString) {
      return jsonString;
    }
  }

  NSString *path = [[NSBundle mainBundle] pathForResource:fileName ofType:@"json"];
  NSString *jsonString = [NSString stringWithContentsOfFile:path encoding:NSUTF8StringEncoding error:nil];
  return jsonString;
}
@end
