//
//  BooklnLaunchCount.m
//  bookln
//
//  Created by le<PERSON><PERSON><PERSON> on 2024/5/7.
//  Copyright © 2024 Yunti. All rights reserved.
//

#import "BooklnLaunchCount.h"

@implementation BooklnLaunchCount
- (NSInteger)currentBuild {
  NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
  return [defaults integerForKey:[self keyCurrentBuild]];
}

- (NSInteger)total {
  NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
  return [defaults integerForKey:[self keyTotal]];
}

- (void)increase {
  NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];

  NSInteger increasedCurrentBuildCount = [self currentBuild] + 1;
  [defaults setInteger:increasedCurrentBuildCount forKey:[self keyCurrentBuild]];

  NSInteger increasedTotalCount = [self total] + 1;
  [defaults setInteger:increasedTotalCount forKey:[self keyTotal]];

  [defaults synchronize];
}


#pragma mark - Private
- (NSString *)keyTotal {
  return [self userDefaultKeyWithDescription:@"totalLaunchCount"];
}

- (NSString *)keyCurrentBuild {
  NSString *buildNumber = [[NSBundle mainBundle].infoDictionary objectForKey:@"CFBundleVersion"];
  NSString *description = [NSString stringWithFormat:@"launchCountOfBuild%@", buildNumber];
  return [self userDefaultKeyWithDescription:description];
}

- (NSString *)userDefaultKeyWithDescription:(NSString *)description {
  NSString *bundleId = [NSBundle mainBundle].bundleIdentifier;
  return [NSString stringWithFormat:@"%@.%@", bundleId, description];
}
@end
