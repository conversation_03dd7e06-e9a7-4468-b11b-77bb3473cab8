require 'net/http'
require 'uri'
require 'json'
require "open-uri"
require 'fastimage'
require 'fileutils'
require '../fastlane/custom_config'

APP_ID_BOOKLN = '2'
APP_ID_DEMO = '10'
APP_ID_SHUJIAJIA = '8'
APP_ID_ANGXIUWAIYU = '11'
APP_ID_LIFANGSHU = '29'
APP_ID_WAIYU = '13'
APP_ID_RONGDEJI = '46'
APP_ID_DEMO_APP = '48'
APP_ID_BOOKLN_BOOK = '59'
APP_ID_HHTK = '65'
APP_ID_HHTK1 = '67'
APP_ID_JINGGULU = '92'
APP_ID_JINGGULU_APP = '105'

FAMILY_IPHONE = 'TARGETED_DEVICE_FAMILY = 1;'
FAMILY_UNIVERSAL = 'TARGETED_DEVICE_FAMILY = "1,2";'

# https://app-prepub.bookln.cn/appservice/miniappinfo.do?appid=9&type=1

def switch_app(app_id, is_inhouse, commit_hash = 'dev', base_host, previous_vid, build_number)
  configuration = fetch_config(app_id, previous_vid)

  unless base_host.nil?
    uri = URI.parse(base_host)
    if uri.kind_of?(URI::HTTP) or uri.kind_of?(URI::HTTPS)
      configuration = configuration.clone
      unless configuration['appExt'].nil?
        configuration['appExt']['baseHost'] = base_host  
      end 
    end
  end
  
  save_config_to_files(configuration)

  base_info = configuration
  xcodeproj_path = "./bookln.xcodeproj"

  app_store_bundle_id = base_info["iOSBundleId"]

  # 服务器返回版本号：v1.0
  latest_app_version = base_info["miniAPPVersion"].split("v")[1]

  device_family, orientations, require_full_screen = get_ipad_options(app_id)

  targets = ['bookln']
  targets.each do |target|
    increment_version_number_in_plist(
      version_number: latest_app_version,
      target: target
    )
    increment_build_number_in_plist(
      build_number: build_number,
      target: target
    )
  end

  # update_info_plist(
  #   xcodeproj: xcodeproj_path,
  #   plist_path: "./jgl/Info.plist",
  #   scheme: "jgl",
  #   block: lambda { |plist|
  #     plist["CFBundleDisplayName"] = base_info["appName"]
  #     # TODO: cenfeng - 暂时注释
  #     # plist["MiSDKAppID"] = base_info["iOSPushAppId"]
  #     # plist["MiSDKAppKey"] = base_info["iOSPushAppKey"]
  #     # plist["MiSDKRun"] = 'online'
  #     # plist["UISupportedInterfaceOrientations"] = orientations
  #     # plist["UIRequiresFullScreen"] = require_full_screen

  #     wechat_app_id = base_info["miniAppWxAppId"]
  #     url_scheme_wechat = plist["CFBundleURLTypes"].find{ |scheme| scheme["CFBundleURLName"] == "weixin"}
  #     if wechat_app_id.nil?
  #       url_scheme_wechat[:CFBundleURLSchemes] = ['bookln-unknow-wechat-app-id']
  #     else
  #       url_scheme_wechat[:CFBundleURLSchemes] = [wechat_app_id]
  #     end

  #     url_scheme_booklnsaas = plist["CFBundleURLTypes"].find{ |scheme| scheme["CFBundleURLName"] == "jgl"}
  #     url_scheme_booklnsaas[:CFBundleURLSchemes] = ["jgl#{app_id}"]
  #   }
  # )

  set_device_family(device_family, '../bookln.xcodeproj/project.pbxproj')

  app_identifier = is_inhouse ? base_info["iOSTestBundleId"] : app_store_bundle_id
  update_app_identifier(
    xcodeproj: xcodeproj_path,
    plist_path: "bookln/Info.plist",
    app_identifier: app_identifier
  )
  # update_app_identifier(
  #   xcodeproj: xcodeproj_path,
  #   plist_path: "BooklnSaaSNotificationExtension/Info.plist",
  #   app_identifier: "#{app_identifier}.notification"
  # )

  # update_app_group_identifiers(
  #   entitlements_file: "jgl/jgl.entitlements",
  #   app_group_identifiers: ["group.#{app_identifier}"]
  # )
  # update_app_group_identifiers(
  #   entitlements_file: "BooklnSaaSNotificationExtension/BooklnSaaSNotificationExtension.entitlements",
  #   app_group_identifiers: ["group.#{app_identifier}"]
  # )

  team_id = is_inhouse ? CustomConfig::ENTERPRISE_TEAM_ID : base_info["iOSTeamId"]
  update_project_team(
    path: xcodeproj_path,
    teamid: team_id
  )

  vid = base_info['miniAPPvid']
  # template_id = get_template_id_from_code
  # update_settings_bundle_values(xcodeproj_path, vid, template_id, commit_hash)

  # replace_app_icon(configuration)
  # replace_launch_screen(app_id, configuration)
  # replace_login_screen_app_icon

  if is_inhouse
    add_badge(
      no_badge: true,
      shield: "inhouse-#{template_id}-green"
    )
  end

  return configuration
end

def fetch_config(app_id, previous_vid)
  url_of_current_version = "https://app-prepub.bookln.cn/appservice/miniappinfo.do?appid=#{app_id}&type=1"
  url_of_previous_version = "https://app-prepub.bookln.cn/appservice/historyMiniappinfo.do?appid=#{app_id}&type=1&vid=#{previous_vid}"

  url = previous_vid == 0 ? url_of_current_version : url_of_previous_version
  uri = URI.parse(url)
  puts("Fetching configuration from #{uri.to_s}...")
  request = Net::HTTP::Get.new(uri.to_s)
  response = Net::HTTP.start(uri.host, uri.port, :use_ssl => uri.scheme == 'https') { |https|
    https.request(request)
  }

  responseBody = JSON.parse(response.body)
  configuration = responseBody['data']
  
  puts("Configuration fetched: \n")
  puts "#{JSON.pretty_generate(response)}"
  
  return configuration
end

def save_config_to_files(configuration)
  replace_appext_json_file(configuration)
  replace_base_info_json_file(configuration)
end

def replace_appext_json_file(configuration)
  appExt = configuration['appExt']
  File.open("../bookln/appext.json", "w") do |file|
    file.write(appExt.to_json)
  end
end

def replace_base_info_json_file(configuration)
  configuration = configuration.clone
  configuration.delete('appExt')
  File.open("../bookln/baseInfo.json", "w") do |file|
    file.write(configuration.to_json)
  end
end

def get_ipad_options(app_id)
  ipad_portrait_ids = [APP_ID_SHUJIAJIA, APP_ID_DEMO_APP]
  ipad_ids = [APP_ID_DEMO, APP_ID_BOOKLN_BOOK, APP_ID_BOOKLN,APP_ID_JINGGULU_APP]

  orientation_p = 'UIInterfaceOrientationPortrait'
  orientation_p_upside_down = 'UIInterfaceOrientationPortraitUpsideDown'
  orientation_l_left = 'UIInterfaceOrientationLandscapeLeft'
  orientation_l_right = 'UIInterfaceOrientationLandscapeRight'

  orientations = [
    orientation_p, 
    orientation_p_upside_down,
    orientation_l_left,
    orientation_l_right
  ]

  if ipad_portrait_ids.include?(app_id)
    device_family = FAMILY_UNIVERSAL
    require_full_screen = true
  elsif ipad_ids.include?(app_id)
    device_family = FAMILY_UNIVERSAL
    require_full_screen = false
  else
    device_family = FAMILY_IPHONE
    require_full_screen = true
  end

  return device_family, orientations, require_full_screen
end

def set_device_family(device_family, pbxproj_path)
  contents = File.read(pbxproj_path)

  if device_family == FAMILY_IPHONE
    new_contents = contents.gsub(/#{FAMILY_UNIVERSAL}/, device_family)
  elsif device_family == FAMILY_UNIVERSAL
    new_contents = contents.gsub(/#{FAMILY_IPHONE}/, device_family)  
  end

  File.open(pbxproj_path, "w") {|file| file.puts new_contents }
end


def replace_app_icon(configuration)
  temp_icon_file_name = 'AppIcon.png'
  temp_icon_file_path = "./#{temp_icon_file_name}"

  base_info = configuration
  app_icon_url = base_info['appIcon']

  File.open(temp_icon_file_path, 'wb') do |fo|
    fo.write open(app_icon_url).read
  end

  command_for_removing_alpha = "convert #{temp_icon_file_path} -background white -alpha remove -alpha off #{temp_icon_file_path}"
  system(command_for_removing_alpha)

  appicon(
    appicon_image_file: "./fastlane/#{temp_icon_file_name}",
    appicon_devices: [:ipad, :iphone, :ios_marketing],
    appicon_path: 'bookln/Images.xcassets' # output path
  )
end


def replace_launch_screen(app_id, configuration)
  launch_image_url = configuration['startPageImgPath']

  # ios/bookln/Images.xcassets/ic_launch.imageset/<EMAIL>
  ic_launch_name = '<EMAIL>'
  ic_launch_path = "#{__dir__}/../bookln/Images.xcassets/ic_launch.imageset/#{ic_launch_name}"

  # download launch image
  File.open(ic_launch_path, 'wb') do |fo|
    fo.write open(launch_image_url).read
  end

  width, height = FastImage.size(ic_launch_path)
  puts("launch image size: #{width}x#{height}")

  centered_app_ids = [
    APP_ID_SHUJIAJIA, 
    APP_ID_DEMO_APP, 
    APP_ID_DEMO, 
    APP_ID_RONGDEJI, 
    APP_ID_WAIYU,
    APP_ID_HHTK1,
    APP_ID_BOOKLN
  ]

  special_app_ids = [APP_ID_BOOKLN_BOOK, APP_ID_BOOKLN]

  if special_app_ids.include?(app_id)
    xib_template_name = "#{app_id}.storyboard"
  elsif centered_app_ids.include?(app_id)
    xib_template_name = 'centered.storyboard'
  else
    is_full_screen_image = width == 750 && height == 1334
    xib_template_name = is_full_screen_image ? 'full.storyboard' : 'centered.storyboard'
  end
  
  xib_template_path = "#{__dir__}/../fastlane/launchscreens/#{xib_template_name}"
  launch_screen_xib_path = "#{__dir__}/../bookln/LaunchScreen.storyboard"

  FileUtils.cp(xib_template_path, launch_screen_xib_path)
  
  File.open(ic_launch_path, 'wb') do |fo|
    fo.write open(launch_image_url).read
  end
end


def replace_login_screen_app_icon
  # ./packages/assets/images/<EMAIL>
  big_icon_path = "#{__dir__}/../bookln/Images.xcassets/AppIcon.appiconset/AppIcon-1024x1024.png"
  target_icon_dir = "#{__dir__}/../../../assets/images/"

  convert_command_path = find_executable('convert')
  command2 = "#{convert_command_path} #{big_icon_path} -resize 140x140 #{target_icon_dir}<EMAIL>"
  system(command2)
  command3 = "#{convert_command_path} #{big_icon_path} -resize 210x210 #{target_icon_dir}<EMAIL>"
  system(command3)
end


def get_app_store_version(bundle_id)
  url = "https://itunes.apple.com/lookup?bundleId=#{bundle_id}"
  uri = URI.parse(url)
  puts("\nGetting app version from App Store...\n#{url}\n\n")

  request = Net::HTTP::Post.new(uri.to_s)
  request['Content-Type'] = 'application/json'
  
  response = Net::HTTP.start(uri.host, uri.port, :use_ssl => uri.scheme == 'https') { |https|
    https.request(request)
  }

  responseBody = JSON.parse(response.body)
  unless responseBody.nil?
    unless responseBody['results'].nil?
      unless responseBody['results'][0].nil?
        unless responseBody['results'][0]['version'].nil?
          app_store_version = responseBody['results'][0]['version']
          puts("\n\nApp Store version is #{app_store_version}")
          return app_store_version
        end
      end
    end
  end

  puts("\n\nApp #{bundle_id} is not found on App Store.")
  return app_store_version
end


def generate_latest_version(bundle_id)
  app_store_version = get_app_store_version(bundle_id)
  version = app_store_version.nil? ? '' : app_store_version
  version_parts = version.strip.split('.')
  major = version_parts.length > 0 ? version_parts[0].strip : '0'
  increased_major = "#{major.length > 0 ? major.to_i + 1 : '0'}"
  latest_version = "#{increased_major}.0"
  return latest_version
end


def update_settings_bundle_values(xcodeproj_path, vid = '?', template_id = '?', commit_hash = '?')
  # version, build, vid, template id, commit hash
  update_settings_bundle(
    xcodeproj: xcodeproj_path,
    key: 'version',
    value: ':version'
  )
  update_settings_bundle(
    xcodeproj: xcodeproj_path,
    key: 'build',
    value: ':build'
  )
  update_settings_bundle(
    xcodeproj: xcodeproj_path,
    key: 'vid',
    value: "#{vid}"
  )
  update_settings_bundle(
    xcodeproj: xcodeproj_path,
    key: 'template-id',
    value: template_id
  )
  unless commit_hash.nil?
    update_settings_bundle(
      xcodeproj: xcodeproj_path,
      key: 'commit-hash',
      value: commit_hash
    )
  end
end


# def get_template_id_from_code
#   # /Users/<USER>/rd/jgl/src/redux/modules/templateId.js
#   template_id_file_path = '../../src/redux/modules/templateId.ts'
#   contents = File.read(template_id_file_path)
#   template_id = contents[/\d+/]
#   return template_id
# end

# TODO: 打包成功后把bundle里面的文件删除了？
