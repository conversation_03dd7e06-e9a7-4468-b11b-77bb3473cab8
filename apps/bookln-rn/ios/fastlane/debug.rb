require 'net/http'
require 'uri'
require 'json'
require 'yaml'


# puts(Time.now.strftime('%Y%m%d%H%M%S'))
# puts(Time.now.strftime('%Y%m%d%H%M%S')[2..-1])


# type = 'alpha'

# if type.eql?('alpha')
#   puts('alpha')
# elsif type == 'beta'
#   puts('beta')
# else
#   puts('app-store')
# end



# def upload_to_fir

#   command = "fir publish /Users/<USER>/Downloads/FLMain.ipa -c \"debug\" --token=\"8630290c222c857c1df2a1dd2c8a6fac\""


#   # I, [2018-02-01T21:43:49.468970 #11714]  INFO -- : Published succeed: http://fir.im/7nmk
#   fir_output = %x(#{command})
  

#   fir_output_lines = fir_output.split("\n")
#   fir_output_lines.each do |line|
#   parts = line.split("INFO -- : Published succeed: ")
#     if parts.length == 2
#       return parts[1]
#     end
#   end
# end

# a = upload_to_fir()
# puts(a)


def get_app_store_version(bundle_id)
  url = "https://itunes.apple.com/lookup?bundleId=#{bundle_id}"
  uri = URI.parse(url)
  puts("\nGetting app version from App Store...\n#{url}\n\n")

  request = Net::HTTP::Post.new(uri.to_s)
  request['Content-Type'] = 'application/json'
  
  response = Net::HTTP.start(uri.host, uri.port, :use_ssl => uri.scheme == 'https') { |https|
    https.request(request)
  }

  responseBody = JSON.parse(response.body)
  app_store_version = responseBody['results'][0]['version']
  puts("App Store version is #{app_store_version.nil? ? 'not found.' : app_store_version}")
  return app_store_version
end


def generate_latest_version(app_store_version)
  version = app_store_version.nil? ? '' : app_store_version
  
  version_parts = version.strip.split('.')
  major = version_parts.length > 0 ? version_parts[0].strip : '1'
  minor = version_parts.length > 1 ? version_parts[1].strip : '0'
  patch = version_parts.length > 2 ? version_parts[2].strip : ''

  increased_patch = "#{patch.length > 0 ? patch.to_i + 1 : '0'}"
  latest_version = "#{major}.#{minor}.#{increased_patch}"
  return latest_version
end


def reset_app_version(bundle_id)
  app_store_version = get_app_store_version(bundle_id)
  latest_version = generate_latest_version(app_store_version)
  system("cd ..; agvtool new-marketing-version #{latest_version}")
end


# reset_app_version('cn.bookln.saas.sjj8')


# puts(generate_latest_version('1.0'))
# puts(generate_latest_version('1.0.0'))
# puts(generate_latest_version('1.0.9'))
# puts(generate_latest_version(' 1 . 0 .   9'))
# puts(generate_latest_version(nil))
# puts(generate_latest_version(''))
# puts(generate_latest_version(' '))
# puts(generate_latest_version('   '))


# if 'http://fir.im/bookln'.start_with?('http')
#   puts('yeah')
# else
#   puts('...')
# end

# if ['Cat', 'Dog', 'Bird'].include?('Bird')
#   puts(app_id_shujiajia)
# elsif ['Cat', 'Dog', 'Bird'].include?('Dog')
#   puts('b')
# else
#   puts('c')
# end


url = 'https://app-daily.bookln.cn/appconfservice/querybykey.do?confKey=ios_ci_config'
uri = URI.parse(url)
request = Net::HTTP::Get.new(uri.to_s)
response = Net::HTTP.start(uri.host, uri.port, :use_ssl => uri.scheme == 'https') { |https|
  https.request(request)
}

responseBody = JSON.parse(response.body)
data = responseBody['data']
yaml_config = data['result']

content = YAML.load(yaml_config)
puts "#{content['sessions']['<EMAIL>']}"