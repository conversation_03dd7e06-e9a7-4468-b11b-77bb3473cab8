
require 'net/http'
require 'uri'
require 'json'


def ding_talk_send_text_message(message, is_debug)
  # {
  #     "msgtype": "text",
  #     "text": {
  #     "content": "我就是我, 是不一样的烟火"
  # },
  #     "at": {
  #     "atMobiles": [
  #     "156xxxx8827",
  #     "189xxxx8325"
  # ],
  #     "isAtAll": false
  # }
  # }

  debug_uri = URI.parse('https://oapi.dingtalk.com/robot/send?access_token=314ac9ca8f3a4bb710e626948e10da54fb68da7271c545be85120a3dbdfeb116')
  saas_uri = URI.parse('https://oapi.dingtalk.com/robot/send?access_token=b5212e289515276626ba40ed6215e0884cfe65a16c81a81fee82bd302ee51f91')
  uri = is_debug ? debug_uri : saas_uri

  text_content = { :content => message }
  tester_wangling = 18328411127
  tester_chenxi = 18200237877
  tester_zhangqian = 18080442867
  at_testers = { :at => { :atMobiles => [tester_wangling, tester_chenxi, tester_zhangqian] }}
  parameters = { :msgtype => 'text', :text => text_content, :at => at_testers, :isAtAll => false }

  request = Net::HTTP::Post.new(uri.to_s)
  request['Content-Type'] = 'application/json'
  request.body = parameters.to_json()

  # noinspection RubyUnusedLocalVariable
  response = Net::HTTP.start(uri.host, uri.port, :use_ssl => uri.scheme == 'https') { |https|
    https.request(request)
  }
end



def ding_talk_send_markdown_message(title, message, is_debug, app_id, dev_build)
  debug_uri = URI.parse('https://oapi.dingtalk.com/robot/send?access_token=314ac9ca8f3a4bb710e626948e10da54fb68da7271c545be85120a3dbdfeb116')
  saas_uri = URI.parse('https://oapi.dingtalk.com/robot/send?access_token=b5212e289515276626ba40ed6215e0884cfe65a16c81a81fee82bd302ee51f91')
  hhtk_uri = URI.parse('https://oapi.dingtalk.com/robot/send?access_token=edb609f3bf0e2bc97d67779e364488e6e7f4f1d430f8260ac4fbd8102c6e36e0')
  bookln_uri = URI.parse('https://oapi.dingtalk.com/robot/send?access_token=ce2812312780f4cbb37f016eb38b5255c6c8a19695f1f4315aaf59d6419c8d26')
  bookln_dev_build_uri = URI.parse('https://oapi.dingtalk.com/robot/send?access_token=99942efb2deb607fa3f4c0ea0f0164d95faa4eaed60350b5f3a7cc4e19f8aba8');
  jgl_uri = URI.parse('https://oapi.dingtalk.com/robot/send?access_token=8ca15de486e44c4944ae0db8a560e0199b4d40dcaa3f6dcacea1351582883fed');
  jgl_dev_build_uri = URI.parse('https://oapi.dingtalk.com/robot/send?access_token=9214e8a65a206de4ead8828300a9169b49107e3bcd1d59dab84c282da20b02dc');

  if is_debug
    uri = debug_uri
  elsif dev_build
    if app_id == '2'
      uri = bookln_dev_build_uri
    elsif app_id == '92'
      uri = jgl_dev_build_uri
    end
  else
    if app_id == '65'
      uri = hhtk_uri
    elsif app_id == '67'
      uri = hhtk_uri
    elsif app_id == '2'
      uri = bookln_uri
    elsif app_id == '92'
      uri = jgl_uri
    else
      uri = saas_uri
    end
  end

  puts "ding_talk_send_markdown_message 来了 uri = #{uri}"

  markdown_content = { :title => title, :text => message }
  parameters = { :msgtype => 'markdown', :markdown => markdown_content}

  request = Net::HTTP::Post.new(uri.to_s)
  request['Content-Type'] = 'application/json'
  request.body = parameters.to_json()

  response = Net::HTTP.start(uri.host, uri.port, :use_ssl => uri.scheme == 'https') { |https|
    https.request(request)
  }
  puts "ding_talk_send_markdown_message 请求结果 response = #{response}"
end
