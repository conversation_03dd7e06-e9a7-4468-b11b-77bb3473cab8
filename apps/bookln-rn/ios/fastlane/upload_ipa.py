# -*- coding: utf-8 -*-
import json
import os
import sys
import ntpath
import oss2


# ipa、plist、二维码的路径：tech/app/ios
# realm压缩包等支持文件的路径：tech/app/ios-support


def upload(file_paths, bucket, uploadDir):
    server_relative_path = "{}ios/".format(uploadDir)

    for file_path in file_paths:
        with open(file_path, 'rb')as f:
            file_name = ntpath.basename(file_path)
            server_file_path = '%s%s' % (server_relative_path, file_name)
            result = bucket.put_object(server_file_path, f)

            if result.status != 200:
                print('上传失败 %s' % file_path)
                print(result)
                print(result.resp)
                exit(1)


def check_files(file_paths):
    for file_path in file_paths:
        if not os.path.exists(file_path):
            print('%s does not exist.' % file_path)
            exit(1)




if __name__ == '__main__':
    oss_config = None
    with open('../../oss_config.json') as oss_config_file:
        oss_config = json.load(oss_config_file)
        if 'accessKeyId' not in oss_config:
            raise ValueError('No accessKeyId in oss_config.json')
        if 'accessKeySecret' not in oss_config:
            raise ValueError('No accessKeySecret in oss_config.json')
        if 'token' not in oss_config:
            raise ValueError('No token in oss_config.json')
        if 'region' not in oss_config:
            raise ValueError('No region in oss_config.json')
        if 'bucket' not in oss_config:
            raise ValueError('No bucketName in oss_config.json')
        if 'uploadDir' not in oss_config:
            raise ValueError('No uploadDir in oss_config.json')

        # 创建Bucket对象，所有Object相关的接口都可以通过Bucket对象来进行
        auth = oss2.StsAuth(oss_config['accessKeyId'], oss_config['accessKeySecret'], oss_config['token'])
        endpoint = "https://{}.aliyuncs.com".format(oss_config["region"])
        bucket = oss2.Bucket(auth, endpoint, oss_config['bucket'])

        file_paths = sys.argv[1:]
        check_files(file_paths)
        upload(file_paths, bucket, oss_config["uploadDir"])