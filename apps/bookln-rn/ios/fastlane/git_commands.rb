require '../fastlane/custom_config'


def git_current_repo_path
  # /Users/<USER>/Documents/workspace/MyAwesomeApp
  path = %x(git rev-parse --show-toplevel)
  path_without_newline = path.gsub("\n", '')
  return path_without_newline
end


def git_pull_without_editing
  system('git pull --no-edit')
end

def git_pull_submodules_to_right_commits
  # pull所有的submodule到指定到commit，但是修改submodule内容的时候，branch还是需要自己切换
  system('git submodule update --recursive')
end

def git_pull_and_push
  git_pull_without_editing
  system('git push')
end

def git_stash
  system('git stash')
end


def git_commit(file_paths, message_title, message_detail)
  if file_paths.count == 0
    return
  end

  if message_title.nil?
    return
  end

  file_paths_string = file_paths.join(' ')
  pretty_title = message_title.gsub("\"", "\\\"")
  pretty_detail = message_detail.gsub("\"", "\\\"")
  %x(git commit #{file_paths_string} -m "#{pretty_title}" -m "#{pretty_detail}")
end


def git_all_author_names(starting_short_hash)
  names_string = %x(git log --format='%an' #{starting_short_hash}.. | sort -u)
  names = names_string.split("\n")
  return names
end


def git_commit_messages_of_author(author_name, starting_short_hash)
  if author_name.nil?
    return
  end

  message_string = %x(git log --format="%ad %s" --date=format:"%m.%d-%H:%M" --author="#{author_name}" #{starting_short_hash}..)
  return message_string.split("\n")
end


def git_current_author_info
  name = %x(git config --get user.name)
  return "#{name.gsub("\n", '')}"
end


def git_head_short_hash
  short_hash = %x(git rev-parse --short HEAD)
  return "#{short_hash.gsub("\n", '')}"
end


def git_current_branch_name
  branch = %x(git rev-parse --abbrev-ref HEAD)
  return "#{branch.gsub("\n", '')}"
end


def git_find_short_hash_of_commit_containing_keyword(keyword)
  if keyword.nil?
    return
  end

  short_hash_string = %x(git log --format='%h' --all --grep "#{keyword}")
  logs = short_hash_string.split("\n")
  short_hash = logs[0]
  return short_hash
end

def git_is_clean
  status_description = %x(git status -z)
  is_clean = status_description.length == 0
  return is_clean
end

