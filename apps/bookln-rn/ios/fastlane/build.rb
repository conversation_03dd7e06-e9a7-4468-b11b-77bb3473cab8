require 'mkmf'
require 'base64'
require 'json'
require 'fileutils'
require '../fastlane/app_type'
require '../fastlane/server_type'
require '../fastlane/export_method'
require '../fastlane/custom_config'
require '../fastlane/git_commands'
require '../fastlane/ding_talk'
require '../fastlane/dynamic_config'
require '../fastlane/feishu'
require 'xcodeproj'

# DEFAULT_XCODE_PATH = "/Applications/Xcode-15.3.app"
DEFAULT_XCODE_PATH = "/Applications/Xcode.app"
DEFAULT_FASTLANE_PATH = ".jenkins/workspace/bookln-ios/apps/bookln-rn/ios/fastlane"

def build(app_id, is_inhouse, is_dev, should_send_message, submit_for_review, upload_to_test_flight, match_force, is_debug, base_host, dev_build, runtime_version, updates_url, expo_update_config, xcode_path)
  # check reqiurements
  check_requirements_and_install_if_needed

  if xcode_path.nil?
    xcode_select(DEFAULT_XCODE_PATH)
    puts "xcode_path 参数未设置，使用默认值 #{DEFAULT_XCODE_PATH}"
  elsif xcode_path == "undefined"
    xcode_select(DEFAULT_XCODE_PATH)
    puts "xcode_path 参数为 undefined，使用默认值 #{DEFAULT_XCODE_PATH}"
  else 
    xcode_select(xcode_path)
  end

  # xcversion(version: "11.3")

  is_jenkins = is_ci

  # make sure local git repo is up-to-date and clean
  git_stash
  if is_jenkins == false
    git_pull_without_editing
    git_pull_submodules_to_right_commits
  end

  # git short hash
  current_hash = git_head_short_hash

  # build number
  build_number = build_number_from_current_time(is_dev)

  # switch app and return configuration from server
  configuration = switch_app(app_id, is_inhouse, current_hash, base_host, 0, build_number)

  # generate information about output
  # BooklnSaaS-10-v1.0(1802051837).xcarchive
  # BooklnSaaS-10-inhouse-v1.0(1802051837).xcarchive
  scheme = ENV['GYM_SCHEME']
  beta_info = is_inhouse ? '-inhouse' : ''
  basic_info = "#{scheme}-#{app_id}#{beta_info}-v#{app_version}-#{build_number}"
  archive_info = basic_info
  archive_file_name = "#{archive_info}.xcarchive"
  archive_folder_name = 'builds'

  # ./ios/fastlane/builds
  output_directory = "#{__dir__}/#{archive_folder_name}"
  archive_file_path = "#{output_directory}/#{archive_file_name}"

  # create builds dir if needed
  Dir.mkdir(output_directory) unless File.exists?(output_directory)

  # delete old builds
  delete_old_build_files(output_directory)

  pod_install

  # 必须要放到 git pull 之后执行，防止更改被重置
  if runtime_version 
    puts "modify_expo_plist_runtime_version - 参数"
    puts "runtime_version - #{runtime_version}"
    update_plist(
      plist_path: "bookln/Supporting/Expo.plist",
      block: proc do |plist|
        plist[:EXUpdatesRuntimeVersion] = runtime_version
      end
    )
  end

  if updates_url 
    puts "modify_expo_plist_updates_url - 参数"
    puts "updates_url - #{updates_url}"
    update_plist(
      plist_path: "bookln/Supporting/Expo.plist",
      block: proc do |plist|
        plist[:EXUpdatesURL] = updates_url
      end
    )
  end

  if expo_update_config
    puts "修改 expoUpdateConfig.json"
    expoUpdateFilePath = "../bookln/expoUpdateConfig.json"
    # 将新的JSON内容写回文件
    File.open(expoUpdateFilePath, 'w') do |file|
      file.write(expo_update_config)
    end

    puts "expoUpdateConfig.json 文件已更新"
  end

  xcode_configuration = "Release"

  # dev_build
  if dev_build
    
    modify_xcode_project_settings()
    xcode_configuration = "Debug"
  end

  team_id = is_inhouse ? CustomConfig::ENTERPRISE_TEAM_ID : configuration['iOSTeamId']
  bundle_id = is_inhouse ? configuration['iOSTestBundleId'] : configuration['iOSBundleId']

  # set_fastlane_envs(team_id)

  # read team information
  team_info = team_info(team_id)
  apple_id = team_info['userAppleId']

  ENV['MATCH_PASSWORD'] = 'AtTheAgeOf19'

  # iosdev, app specific password
  ENV['FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD'] = 'hryh-vsik-fnmh-vskt'

  match_git_url = "https://git.bookln.cn/yuntitech_react_native/ios-certificates.git"
  match_git_branch = team_id
  match_git_user_email = '<EMAIL>'

  # 只拉对应分支，如果不存在，使用update_certificates来更新
  match_clone_branch_directly = true

  match_type_for_test = is_dev ? 'development' : (is_inhouse ? 'enterprise' : 'adhoc')

  app_identifiers = "#{bundle_id}"

  match(
    team_id: team_id,
    username: apple_id,
    app_identifier: app_identifiers,
    type: match_type_for_test,
    git_url: match_git_url,
    git_branch: match_git_branch,
    git_user_email: match_git_user_email,
    readonly: true,
    clone_branch_directly: match_clone_branch_directly
  )

  # archive .xcarchive file
  xcargs = xcargs_string(configuration, current_hash)
  gym(
    configuration: xcode_configuration,
    skip_package_ipa: true,
    xcargs: xcargs,
    archive_path: archive_file_path,
    export_team_id: team_id
  )

  # export ipa
  export_method = is_dev ? 'development' : (is_inhouse ? 'enterprise' : 'ad-hoc')
  ipa_name_for_test = dev_build ? "#{basic_info}-#{export_method}-dev.ipa" : "#{basic_info}-#{export_method}.ipa" 
  qr_code_name_for_test = dev_build ? "#{basic_info}-#{export_method}-dev.png" : "#{basic_info}-#{export_method}.png"
  default_ipa_mainifest_plist_name = 'manifest.plist'
  ipa_manifest_plist_name = dev_build ? "#{basic_info}-#{export_method}-dev.plist" : "#{basic_info}-#{export_method}.plist"
  url_prefix = "https://ajsyscdn.bookln.cn/tech/app/ios"
  gym(
    configuration: xcode_configuration,
    include_bitcode: false,
    export_team_id: team_id,
    skip_build_archive: true,
    archive_path: archive_file_path,
    output_directory: output_directory,
    output_name: ipa_name_for_test,
    export_options: {
      manifest: {
        appURL: "#{url_prefix}/#{ipa_name_for_test}",

        # TODO: 图片暂时写死
        displayImageURL: "https://cdn11.bookln.cn/1815649_75d3e87c25b63142193a2e1011105233235ef520.png?imageView2/0/w/144/h/144",
        fullSizeImageURL: "https://ajsyscdn.bookln.cn/server/img/book_app/icon/logo_1024x1024.jpg"
      }
    },
    export_method: export_method,
    xcargs: '-allowProvisioningUpdates',
    export_xcargs: '-allowProvisioningUpdates'
  )

  ipa_manifest_plist_path = "#{output_directory}/#{ipa_manifest_plist_name}"
  File.rename(
    "#{output_directory}/#{default_ipa_mainifest_plist_name}",
    ipa_manifest_plist_path
  )

  # upload
  ipa_path_for_ad_hoc = "#{output_directory}/#{ipa_name_for_test}"

  # itms-services://?action=download-manifest&url=https://ajsyscdn.bookln.cn/tech/ios/debug2.plist
  plist_url = "#{url_prefix}/#{ipa_manifest_plist_name}"
  qr_code_url = "#{url_prefix}/#{qr_code_name_for_test}"
  ipa_install_url = "itms-services://?action=download-manifest&url=#{plist_url}"

  puts "ipa_install_url #{ipa_install_url}"

  qr_code_path_for_ad_hoc = "#{output_directory}/#{qr_code_name_for_test}"
  create_qr_code_command = "pipenv run qr \"#{ipa_install_url}\" > \"#{qr_code_path_for_ad_hoc}\""
  result_create_qr_code = system(create_qr_code_command)
  if result_create_qr_code != true
    FastlaneCore::UI.user_error!('创建二维码失败')
  end

  # upload_command = "pipenv run python upload_ipa.py #{ipa_path_for_ad_hoc} #{ipa_manifest_plist_path} #{qr_code_path_for_ad_hoc}"
  upload_command = "npx tsx ../../scripts/upload-ios-ipa.mts #{ipa_path_for_ad_hoc} #{ipa_manifest_plist_path} #{qr_code_path_for_ad_hoc}"
  puts upload_command
  result_upload = system(upload_command)
  if result_upload != true
    FastlaneCore::UI.user_error!('上传失败')
  end

  # TODO: 处理上传失败

  app_name = configuration['appName']
  beta_change_log_info = is_inhouse ? ' InHouse' : ''
  dev_change_log_info = is_dev ? ' Development' : ''
  change_log = "#{app_name} iOS#{beta_change_log_info}#{dev_change_log_info} #{app_version}(#{build_number})"

  if should_send_message
    puts "send_ding_talk_message_after_success 来了哦"
    send_ding_talk_message_after_success(change_log, ipa_install_url, current_hash, qr_code_url, is_debug, app_id, dev_build)
  end

  if upload_to_test_flight && !is_inhouse
    # export app-store .ipa, upload to TestFlight
    match(
      team_id: team_id,
      username: apple_id,
      app_identifier: bundle_id,
      type: 'appstore',
      git_url: match_git_url,
      git_branch: match_git_branch,
      git_user_email: match_git_user_email,
      readonly: true,
      clone_branch_directly: match_clone_branch_directly
    )

    ipa_name_for_app_store = "#{basic_info}-appstore.ipa"
    gym(
      include_bitcode: false,
      export_team_id: team_id,
      skip_build_archive: true,
      archive_path: archive_file_path,
      output_directory: output_directory,
      output_name: ipa_name_for_app_store,
      export_method: 'app-store',
      xcargs: '-allowProvisioningUpdates',
      export_xcargs: '-allowProvisioningUpdates'
    )

    ipa_path_for_app_store = "#{output_directory}/#{ipa_name_for_app_store}"
    itc_team_id = team_info['itcTeamId']
    ENV['FASTLANE_ITC_TEAM_ID'] = itc_team_id

    prepare_metadata_and_screenshots(app_id)
    submission_information = JSON.parse(File.read('submission_information.json'))
    deliver(
      username: apple_id,
      app_identifier: bundle_id,
      ipa: ipa_path_for_app_store,
      force: true,
      skip_screenshots: true, # 截图手动维护
      submit_for_review: submit_for_review,
      reject_if_possible: true,
      automatic_release: true,
      phased_release: true,
      submission_information: submission_information,
      team_id: itc_team_id,
      dev_portal_team_id: team_id,
      # run_precheck_before_submit: true,
      precheck_include_in_app_purchases: false
    )
  end

  # discard all local changes
  system('cd ..; git checkout .')

end

def update_certificates(app_id)
  configuration = switch_app(app_id, false, nil, nil, 0, "1")

  team_id = configuration['iOSTeamId']
  bundle_id = configuration['iOSBundleId']

  team_info = team_info(team_id)
  team_name = team_info['itcTeamName']
  itc_team_id = team_info['itcTeamId']
  apple_id = team_info['userAppleId']

  match_git_url = "https://git.bookln.cn/yuntitech_react_native/ios-certificates.git"
  match_git_branch = team_id
  match_git_user_email = '<EMAIL>'

  # set_fastlane_envs(team_id)

  ENV['MATCH_PASSWORD'] = 'AtTheAgeOf19'

  app_identifiers = "#{bundle_id}"
  # 确定用户主目录
  user_home_directory_path = File.expand_path('~')
  # 确定 fastlane 路径
  fastlane_path = "#{user_home_directory_path}/#{DEFAULT_FASTLANE_PATH}"
  puts "fastlane_path: #{fastlane_path}"
  api_key = get_app_store_connect_api_key(team_id, fastlane_path)

  types = ['development', 'adhoc']
  types.each do |type|
    match(
      team_id: itc_team_id,
      team_name: team_name,
      username: apple_id,
      api_key: api_key,
      app_identifier: app_identifiers,
      type: type,
      force_for_new_devices: true,
      git_url: match_git_url,
      git_branch: team_id,
      git_user_email: match_git_user_email,
      verbose: true
    )
  end

  match(
    team_id: itc_team_id,
    team_name: team_name,
    username: apple_id,
    api_key: api_key,
    app_identifier: app_identifiers,
    type: 'appstore',
    git_url: match_git_url,
    git_branch: team_id,
    git_user_email: match_git_user_email,
    verbose: true
  )
end

def update_certificate_demo_wugensongai
  team_id = '2KT364D29M'

  team_info = team_info(team_id)
  apple_id = team_info['userAppleId']

  # iosdev, app specific password
  ENV['FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD'] = 'hryh-vsik-fnmh-vskt'

  match_git_url = "https://git.bookln.cn/yuntitech_react_native/ios-certificates.git"
  match_git_branch = team_id
  match_git_user_email = '<EMAIL>'

  ENV['MATCH_PASSWORD'] = 'AtTheAgeOf19'

  # types = ['development', 'adhoc']
  types = ['adhoc']
  types.each do |type|
    match(
      team_id: '2KT364D29M',
      username: apple_id,
      app_identifier: 'cn.bookln.demo.wugensongai',
      type: type,
      force_for_new_devices: true,
      git_url: match_git_url,
      git_branch: team_id,
      git_user_email: match_git_user_email
    )
  end
end

def prepare_metadata_and_screenshots(app_id)
  # ./ios/fastlane
  # ./ios/fastlane/metadata
  # ./ios/fastlane/metadata/zh-Hans
  # ./ios/fastlane/screenshots
  dir_root = __dir__
  dir_metadata = "#{dir_root}/metadata"
  dir_metadata_zh = "#{dir_metadata}/zh-Hans"
  dir_screenshots = "#{dir_root}/screenshots"

  # clean previous data
  [dir_metadata, dir_screenshots].each do |dir|
    FileUtils.rm_rf(dir)
  end

  [dir_metadata, dir_metadata_zh, dir_screenshots].each do |dir|
    Dir.mkdir(dir) unless File.exists?(dir)
  end

  dir_app = "#{dir_root}/app-info/#{app_id}"
  dir_app_metadata = "#{dir_app}/metadata"
  dir_app_screenshots = "#{dir_app}/screenshots"

  copy_all_files_in_dir(dir_app_metadata, dir_metadata_zh)
  copy_all_files_in_dir(dir_app_screenshots, dir_screenshots)
end

def copy_all_files_in_dir(dir, target_dir)
  if File.directory?(dir)
    Dir.foreach(dir) do |item|
      next if item == '.' or item == '..'

      full_path = "#{dir}/#{item}"
      FileUtils.cp(full_path, target_dir)
    end
  end
end

def send_ding_talk_message_after_success(app_info, ipa_install_url, current_hash, qr_code_image_url, is_debug, app_id, dev_build)
  # ### 书链图书 iOS 1.0.2(202003120920)
  # 地址：itms-services://?action=download-manifest&url=https://ajsyscdn.bookln.cn/tech/app/ios/BooklnSaaS-59-v1.0.2-202003120920-ad-hoc.plist
  # 任务：saas-develop
  # 代码：240d0863e
  # 操作：SCMTrigger
  # templateId: 178

  info_app = "## #{app_info}\n"
  info_install = "地址：#{ipa_install_url}"
  info_task = "任务：#{ENV['JOB_NAME']}"
  info_branch = "分支：#{ENV['GIT_BRANCH']}"
  info_code = "代码：#{current_hash}"
  # info_template_id = "templateId: #{get_template_id_from_code}"

  is_jenkins = is_ci

  operator_name = is_jenkins ? ENV['BUILD_USER'] : git_current_author_info
  info_operator = "操作：#{operator_name}"

  info_qr_code = "![qrcode](#{qr_code_image_url})"

  markdown_new_line = "\n\n"
  info = []
  if is_jenkins
    # https://github.com/fastlane/fastlane/issues/1348#issuecomment-268384192
    previous_successful_commit = ENV['GIT_PREVIOUS_SUCCESSFUL_COMMIT']
    if previous_successful_commit.nil?
      jenkins_change_log = ''
    else
      jenkins_change_log = changelog_from_git_commits(
        between: [ENV['GIT_PREVIOUS_SUCCESSFUL_COMMIT'], ENV['GIT_COMMIT']],
        pretty: "1. %s [%aN] (%h)\n"
      )
      jenkins_change_log = "" if jenkins_change_log.to_s.length == 0
    end

    # Jenkins打包的时候，获取到的分支是HEAD，所以去掉bransh信息
    info = [
      info_app,
      info_install,
      info_task,
      info_code,
      info_operator,
      # info_template_id,
      markdown_new_line,
      info_qr_code,
      markdown_new_line,
      jenkins_change_log
    ]
  else
    # 手动执行打包的时候，bransh可以正常获取到
    info = [
      info_app,
      info_install,
      info_task,
      info_branch,
      info_code,
      info_operator,
      # info_template_id,
      markdown_new_line,
      info_qr_code
    ]
  end

  info_string = info.join(markdown_new_line)
  if app_id == '2'
    notify_build_msg_command = "npx tsx ../../scripts/notify-build-msg.mts appId=#{app_id} platform=ios buildType=#{dev_build ? 'devBuild' : 'normalBuild'} qrCodeImageUrl=\"#{qr_code_image_url}\" messageTitle=\"#{info_app}\" downloadAddress=\"#{info_install}\" taskName=\"#{info_task}\" buildGitBranch=\"#{info_branch}\" buildGitHash=\"#{info_code}\""
    puts notify_build_msg_command
    result_notify = system(notify_build_msg_command)
    if result_notify != true
      FastlaneCore::UI.user_error!('消息推送失败')
    end
    # fei_shu_send_message(info_app, info_string, dev_build, qr_code_image_url)
  else
    ding_talk_send_markdown_message(info_app, info_string, is_debug, app_id, dev_build)
  end
end

def delete_old_build_files(output_directory)
  now = Time.now

  Dir.foreach(output_directory) do |item|
    next if item == '.' or item == '..'

    full_path = "#{output_directory}/#{item}"
    creation_date = File.ctime(full_path)

    two_days = 60 * 60 * 24 * 2
    is_file_old = now - creation_date > two_days

    if is_file_old
      if File.directory?(full_path)
        FileUtils.rm_rf(full_path)
      else
        File.delete(full_path)
      end
      puts("Deleting old file: #{full_path}\n")
    end
  end
end

def set_fastlane_envs(team_id)
  url = 'https://app-daily.bookln.cn/appconfservice/querybykey.do?confKey=ios_ci_config'
  uri = URI.parse(url)
  request = Net::HTTP::Get.new(uri.to_s)
  response = Net::HTTP.start(uri.host, uri.port, :use_ssl => uri.scheme == 'https') { |https|
    https.request(request)
  }

  responseBody = JSON.parse(response.body)
  data = responseBody['data']
  yaml_config = data['result']

  content = YAML.load(yaml_config)
  session = content['sessions'][team_id]
  application_specific_password = content['applicationSpecificPassword'][team_id]
  ENV['FASTLANE_SESSION'] = session
  ENV['FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD'] = application_specific_password
end

def team_info(team_id)
  file = File.read('./team.json')
  team_info = JSON.parse(file)
  return team_info[team_id]
end

def pod_install
  puts("Running pod install\n")

  Dir.chdir('.') {
    system("pwd")
    command = 'bundle exec pod install'
    print_important_message('CocoaPods', command)
    system("#{command}")
  }
end

def check_requirements_and_install_if_needed
  # Homebrew
  if find_executable('brew').nil?
    puts("brew is not installed.\n")
    puts("Installing brew...\n")
    system("/usr/bin/ruby -e \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/master/install)\"")
  end

  # CocoaPods
  if find_executable('pod').nil?
    puts("CocoaPods is not installed.\n")
    puts("Installing CocoaPods...\n")
    system("brew install cocoapods")
  end

  uv_command_path = find_executable('uv')
  if uv_command_path.nil?
    puts("uv not installed.\n")
    puts("Installing uv...\n")
    system('brew install uv')
  end

  update_python_env_command = 'uv sync --upgrade'
  result_update_python_env = system(update_python_env_command)
  if result_update_python_env != true
    FastlaneCore::UI.user_error!('uv sync --upgrade失败')
  end


  prepare_python_env_command = 'uv sync'
  result_python_env = system(prepare_python_env_command)
  if result_python_env != true
    FastlaneCore::UI.user_error!('uv sync失败')
  end

  convert_command_path = find_executable('convert')
  if convert_command_path.nil?
    puts("convert not installed.\n")
    puts("Installing imagemagick...\n")
    system('brew install imagemagick')
  end
end

def build_number_from_current_time(is_dev)
  # 1707131510 年月日时分
  time = Time.now.strftime('%Y%m%d%H%M')
  build_number = is_dev ? "#{time}.dev" : time
  return build_number
end

def xcargs_string(configuration, source_code_hash)
  if configuration.nil?
    return
  end

  build_date_time_interval_since_1970 = %x(date +%s)

  base_info = configuration.clone
  base_info.delete('appExt')
  base_info_base64 = Base64.strict_encode64(base_info.to_json)

  print_important_message('Generating xcargs, baseInfo json:', base_info)
  print_important_message('Generating xcargs, baseInfo base64:', base_info_base64)

  app_ext = configuration.clone['appExt']
  app_ext_base64 = Base64.strict_encode64(app_ext.to_json)

  print_important_message('Generating xcargs, appExt json:', app_ext)
  print_important_message('Generating xcargs, appExt base64:', app_ext_base64)

  # GCC_PREPROCESSOR_DEFINITIONS_NOT_USED_IN_PRECOMPS="YT_SERVER=0 YT_APP_TYPE=0 YT_BUILD_DATE=1484124262 YT_SOURCE_CODE_HASH=3bcf1df YT_APP_EXT_BASE64=d2dlZmR...zZXdmZHM= YT_BASE_INFO_BASE64=d2dlZmR...zZXdmZHM="
  string = "-allowProvisioningUpdates GCC_PREPROCESSOR_DEFINITIONS_NOT_USED_IN_PRECOMPS=\"YT_BUILD_DATE=#{build_date_time_interval_since_1970} YT_SOURCE_CODE_HASH=#{source_code_hash} YT_APP_EXT_BASE64=#{app_ext_base64} YT_BASE_INFO_BASE64=#{base_info_base64}\""
  return string
end

def print_important_message(title, message)
  # puts "\n\n#{title}:\n"
  # puts "    #{message}"
  # puts "\n\n\n\n"
end

def abort_with_important_message(message)
  pretty_message = "\n\n\n#{message}\n\n\n\n"
  abort(pretty_message)
end

def app_version
  version = %x(cd ..; agvtool what-marketing-version -terse1).gsub("\n", '')
  puts "Retrieved version: #{version}"
  return version
end

def get_developer_account_secret
  dir = './ios-secret'
  if File.directory?(dir)
    FileUtils.remove_dir(dir)
  end

  %x(git clone https://git.bookln.cn/yuntitech/ios-secret.git)
  file_path = "#{dir}/README.md"
  if File.exists?(file_path)
    secret = File.read(file_path)
    return secret.gsub("\n", '')
  else
    abort_with_important_message('获取打包密码失败')
  end
end

# 导出商店 ipa 包 并上传 testflight
def find_latest_archive_and_ipa(app_id, xcode_path, fastlane_path_param)
  # 确定用户主目录
  user_home_directory_path = File.expand_path('~')
  # puts "user_home_directory_path = #{user_home_directory_path}"
  # 确定路径，因为当前执行路径为 bookln-ios-upload-appstore-ipa-to-testflight/apps/bookln/ios/fastlane
  # 而我们需要查找的 archive 位于 bookln-ios/apps/bookln/ios/fastlane/builds
  if fastlane_path_param.nil?
    fastlane_path = "#{user_home_directory_path}/#{DEFAULT_FASTLANE_PATH}"
  elsif fastlane_path_param == "undefined"
    fastlane_path = "#{user_home_directory_path}/#{DEFAULT_FASTLANE_PATH}"
  else 
    fastlane_path = fastlane_path_param
  end
  puts "fastlane_path 路径为：#{fastlane_path}"
  fastlane_build_path = "#{fastlane_path}/builds"
  # 判断 fastlane/builds 目录是否存在
  if File.exist?(fastlane_build_path)
    # 获取最新生成的 xcarchive 文件
    recently_xc_archive_file_name = Dir.glob("#{fastlane_build_path}/*.xcarchive").max_by do |f|
      File.mtime(f)
    end

    # 判断最新生成的 xcarchive 是否存在
    if recently_xc_archive_file_name == nil || recently_xc_archive_file_name == ""
      puts 'bookln ios builds directory xcarchive file not exists'
    else
      # 提取出时间字符串，查找对应的 ipa
      # 以 / 对文件名进行拆分
      file_name_array = recently_xc_archive_file_name.split('/')
      # 取最后一个元素，去除前面的目录信息
      basic_info_with_extension = file_name_array[file_name_array.length - 1]
      # 再替换掉 .xcarchive，得到不带后缀的 basic_info
      basic_info = basic_info_with_extension.sub(".xcarchive", "")
      # basic_info 后拼接上 -appstore.ipa
      target_ipa_file_name = "#{basic_info}-appstore.ipa"
      # target_ipa_file_name 前面拼接上 fastlane/builds 路径
      target_ipa_file_path = "#{fastlane_build_path}/#{target_ipa_file_name}"
      # 判断商店 ipa 包是否已存在，如果已存在，说明已上传过
      if File.exist?(target_ipa_file_path)
        puts "#{target_ipa_file_name} exists"
      else
        puts "#{target_ipa_file_name} not exists"
        # 判断 xcarchive 文件是否存在
        if File.exist?(recently_xc_archive_file_name)
          # 确保有执行权限
          File.chmod(0777, recently_xc_archive_file_name)
          # 导出并上传 ipa
          export_app_store_ipa_then_upload(app_id, target_ipa_file_name, recently_xc_archive_file_name, fastlane_path, xcode_path)
        else
          puts "#{recently_xc_archive_file_name} not exists"
        end
      end
    end

  else
    puts 'bookln ios builds directory not exists'
  end

end

# 导出 app-store 包并上传
def export_app_store_ipa_then_upload(app_id, ipa_name, archive_file_path, fastlane_path, xcode_path)
  # Check requirements
  check_requirements_and_install_if_needed
  # 选择 xcode 路径
  if xcode_path.nil?
    xcode_select(DEFAULT_XCODE_PATH)
    puts "xcode_path 参数未设置，使用默认值 #{DEFAULT_XCODE_PATH}"
  elsif xcode_path == "undefined"
    xcode_select(DEFAULT_XCODE_PATH)
    puts "xcode_path 参数为 undefined，使用默认值 #{DEFAULT_XCODE_PATH}"
  else 
    xcode_select(xcode_path)
  end
  # git short hash
  current_hash = git_head_short_hash
  # build number
  build_number = build_number_from_current_time(false)
  # switch app and return configuration from server
  configuration = switch_app(app_id, false, current_hash, 'default', 0, build_number)

  # 取出苹果开发者账号 ID，例如 2KT364D29M
  dev_portal_team_id = configuration['iOSTeamId']
  # 取出包名
  bundle_id = configuration["iOSBundleId"]
  # 拼接出 notification 的包名
  # notification_bundle_id = "#{bundle_id}.notification"

  # read team information
  team_info = team_info(dev_portal_team_id)
  # 取出开发者账号
  apple_id = team_info['userAppleId']

  # 设置 MATCH_PASSWORD 环境变量
  ENV['MATCH_PASSWORD'] = 'AtTheAgeOf19'
  # 设置 FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD 环境变量
  ENV['FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD'] = 'hryh-vsik-fnmh-vskt'

  # Match git 仓库地址
  match_git_url = "https://git.bookln.cn/yuntitech_react_native/ios-certificates.git"
  # Match git 分支，分支都是以开发者账号 ID 作为分支命名
  match_git_branch = dev_portal_team_id
  # Match git 用户邮箱
  match_git_user_email = '<EMAIL>'
  # 只拉对应分支，如果不存在，使用update_certificates来更新
  match_clone_branch_directly = true

  # 拉取证书和配置文件
  match(
    team_id: dev_portal_team_id,
    username: apple_id,
    app_identifier: [bundle_id],
    type: 'appstore',
    git_url: match_git_url,
    git_branch: match_git_branch,
    git_user_email: match_git_user_email,
    readonly: true,
    clone_branch_directly: match_clone_branch_directly
  )

  # 跳转至 bookln-ios/apps/bookln/ios/fastlane 目录
  Dir.chdir(fastlane_path) do
    # ipa 输出目录
    output_directory = "#{fastlane_path}/builds"
    # ipa 输出名称
    output_name = ipa_name
    # 输出 app-store ipa
    gym(
      # workspace: 'BooklnSaaS.xcworkspace',
      include_bitcode: false,
      export_team_id: dev_portal_team_id,
      skip_build_archive: true,
      archive_path: archive_file_path,
      output_directory: output_directory,
      output_name: output_name,
      export_method: 'app-store',
      xcargs: '-allowProvisioningUpdates',
      export_xcargs: '-allowProvisioningUpdates'
    )

    # ipa 路径
    ipa_path_for_app_store = "#{output_directory}/#{output_name}"
    # 团队 ID，如 106075817
    itc_team_id = team_info['itcTeamId']
    # 设置 FASTLANE_ITC_TEAM_ID 环境变量
    ENV['FASTLANE_ITC_TEAM_ID'] = itc_team_id

    api_key = get_app_store_connect_api_key(dev_portal_team_id, fastlane_path)

    # 上传至 Testflight
    deliver(
      username: apple_id,
      app_identifier: bundle_id,
      api_key: api_key,
      ipa: ipa_path_for_app_store,
      force: true, # 跳过 HTML 预览文件的验证
      skip_screenshots: true, # 截图手动维护
      submit_for_review: false, # 提审手动操作
      reject_if_possible: true, # 如果前一次提交的构建处于可用状态，则 reject 这个构建
      automatic_release: false, # 手动发布
      skip_metadata: true, # 跳过元数据上传
      phased_release: true,
      team_id: itc_team_id,
      dev_portal_team_id: dev_portal_team_id,
      precheck_include_in_app_purchases: false,
    )
  end

end

def modify_xcode_project_settings()
  project_path = '../bookln.xcodeproj'
  project = Xcodeproj::Project.open(project_path)
  project.targets.each do |target|
    if target.name == 'bookln'
      # 找到 Bundle React Native code and images
      # project.objects.each do |object|
      #   object.
      # end
      target.shell_script_build_phases.each do |build_phase|
        if build_phase.display_name == 'Bundle React Native code and images'
          old_script = build_phase.shell_script 
          build_phase.shell_script = "export IS_EXPORT_DEV_BUILD=1\n\n#{old_script}" 
          puts build_phase.shell_script
        end
      end

      target.build_configurations.each do |build_configuration|
        if build_configuration.name == 'Debug'
          build_configuration.build_settings["DEBUG_INFORMATION_FORMAT"] = "dwarf-with-dsym";
        end
      end

    end
  end
end

def app_store_connect(team_id)
  file = File.read('./app_store_connect.json')
  app_connect_info = JSON.parse(file)
  return app_connect_info[team_id]
end

def get_app_store_connect_api_key(team_id, fastlane_path_param)
  app_connect_info = app_store_connect(team_id)
  key_path = app_connect_info["key_path"];
  key_id = app_connect_info["key_id"];
  issuer_id = app_connect_info["issuer_id"];

  # 确定用户主目录
  user_home_directory_path = File.expand_path('~')
  if fastlane_path_param.nil?
    fastlane_path = "#{user_home_directory_path}/#{key_path}"
  elsif fastlane_path_param == "undefined"
    fastlane_path = "#{user_home_directory_path}/#{key_path}"
  else 
    fastlane_path = fastlane_path_param
  end
  
  api_key = app_store_connect_api_key(
    key_id: key_id,
    issuer_id: issuer_id,
    key_filepath: "#{fastlane_path}/AuthKey_#{key_id}.p8",
    duration: 1200, # optional (maximum 1200)
    in_house: false # optional but may be required if using match/sigh
  )
  return api_key
end