
ENV['GYM_WORKSPACE'] = 'bookln.xcworkspace'
ENV['GYM_SCHEME'] = 'bookln'


# yunti's mac mini
ENV['MATCH_KEYCHAIN_PASSWORD'] = 'yunti'


# 解决TestFlight上传失败问题
# 不手动指定上传方式，让 fastlane 以及 Apple API 自动选择最合适的方式
# ENV['DELIVER_ITMSTRANSPORTER_ADDITIONAL_UPLOAD_PARAMETERS'] = '-t Aspera'


ENV['CRASHLYTICS_API_TOKEN'] = '9217b3261c78fd6c99502ecfb2cf0c556ddee82c'


ENV['GYM_INCLUDE_SYMBOLS'] = true
ENV['GYM_INCLUDE_BITCODE'] = false


# 默认是10秒钟，导出ipa的时候经常会出问题，特别是机器比较卡的时候
ENV['FASTLANE_XCODEBUILD_SETTINGS_TIMEOUT'] = 60
ENV['FASTLANE_XCODEBUILD_SETTINGS_RETRIES'] = 10


# you can even provide different app identifiers, Apple IDs and team names per lane:
# More information: https://github.com/fastlane/fastlane/blob/master/fastlane/docs/Appfile.md
