class ServerType
  LOCAL = 'local'
  DAILY = 'daily'
  PREPUB = 'prepub'
  PRODUCTION = 'production'

  def self.value(server_type)
    if server_type == ServerType::LOCAL
      return 0
    elsif server_type == ServerType::DAILY
      return 1
    elsif server_type == ServerType::PREPUB
      return 2
    elsif server_type == ServerType::PRODUCTION
      return 3
    end
  end

  def self.description(server_type)
    if server_type == ServerType::LOCAL
      return '本地调试'
    elsif server_type == ServerType::DAILY
      return '测试'
    elsif server_type == ServerType::PREPUB
      return '预发'
    elsif server_type == ServerType::PRODUCTION
      return '线上'
    end
  end

end