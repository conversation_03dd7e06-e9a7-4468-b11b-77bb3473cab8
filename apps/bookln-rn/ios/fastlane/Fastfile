# Customise this file, documentation can be found here:
# https://github.com/fastlane/fastlane/tree/master/fastlane/docs
# All available actions: https://docs.fastlane.tools/actions
# can also be listed using the `fastlane actions` command

# Change the syntax highlighting to Ruby
# All lines starting with a # are ignored when running `fastlane`

# If you want to automatically update fastlane if a new version is available:
# update_fastlane

# This is the minimum version number required.
# Update this, if you use features of a newer version
fastlane_version "2.41.0"


require '../fastlane/app_type'
require '../fastlane/server_type'
require '../fastlane/build'
require '../fastlane/ding_talk'
require '../fastlane/dynamic_config'
require 'json'


default_platform :ios

platform :ios do
  before_all do
    # ENV["SLACK_URL"] = "https://hooks.slack.com/services/..."
    # cocoapods
  end

  desc 'Runs all the tests'
  lane :test do
    scan
  end

  desc '书链demo'
  lane :app_demo do
    app(app_id: 10)
  end

  desc '新东方书加加'
  lane :app_sjj do
    app(app_id: 8)
  end

  desc '打包'
  desc '使用方法：bundle exec fastlane app app_id:10 is_inhouse:false is_dev:false'


  desc '参数：is_inhouse，optional，是否是测试包，如果是测试包，则使用企业账号打包，默认false。'
  desc '参数：is_dev，optional，是否开发签名的包，用于monkey测试等，默认false。'
  desc '参数：should_send_message，optional，是否打包成功后发送钉钉消息，默认true。'
  desc '参数：submit_for_review，optional，是否提交审核，默认false。'
  desc '参数：upload_to_test_flight，optional，是否上传到TestFlight，默认false。'
  desc '参数：match_force_update，Renew the provisioning profiles every time，默认true，手动执行的时候建议传false。'
  desc '参数：is_debug，optional，是否是调试模式，默认false。'
  desc '参数：base_host，optional，传入的值会替换为打包配置中的baseHost值。'
  desc '参数: dev_build, optional, 是否开启 expo-dev-client，默认false'
  desc '参数: runtime_version, optional, App 原生代码版本'
  desc '参数: updates_url, optional, 热更新服务地址'
  desc '参数: expo_update_config, optional, 热更新服务配置'
  desc '参数: xcode_path, optional, Xcode 路径'
  lane :app do |options|
    ENV["FASTLANE_XCODEBUILD_SETTINGS_TIMEOUT"] = "120"
    ENV["FASTLANE_XCODEBUILD_SETTINGS_RETRIES"] = "120"

    app_id = options[:app_id]
    if app_id.nil?
      abort("\n\napp_id不能为空\n\n")
    end

    is_inhouse = options[:is_inhouse].nil? ? false : options[:is_inhouse]
    is_dev = options[:is_dev].nil? ? false : options[:is_dev]
    should_send_message = options[:should_send_message].nil? ? true : options[:should_send_message]
    submit_for_review = options[:submit_for_review].nil? ? false : options[:submit_for_review]
    upload_to_test_flight = options[:upload_to_test_flight].nil? ? false : options[:upload_to_test_flight]
    match_force_update = options[:match_force_update].nil? ? true : options[:match_force_update]
    is_debug = options[:is_debug].nil? ? false : options[:is_debug]
    base_host = options[:base_host].nil? ? nil : options[:base_host]
    dev_build = options[:dev_build].nil? ? nil : options[:dev_build]
    runtime_version = options[:runtime_version].nil? ? nil : options[:runtime_version]
    updates_url = options[:updates_url].nil? ? nil : options[:updates_url]
    expo_update_config = options[:expo_update_config].nil? ? nil : options[:expo_update_config]
    xcode_path = options[:xcode_path].nil? ? nil : options[:xcode_path]
    build(app_id, is_inhouse, is_dev, should_send_message, submit_for_review, upload_to_test_flight, match_force_update, is_debug, base_host, dev_build, runtime_version, updates_url, expo_update_config, xcode_path)
  end

  desc "切换应用"
  desc "会切换所有配置，包括bundleId、应用名、图标等各种配置"
  desc "使用方法：bundle exec fastlane switch_app app_id:10 is_inhouse:false is_dev:false"
  desc "参数：is_inhouse，optional，是否是测试包，如果是测试包，则使用企业账号打包。"
  desc "参数：is_dev，optional，是否开发签名的包，用于monkey测试等。"
  desc '参数：base_host，optional，传入的值会替换为打包配置中的baseHost值。'
  desc '参数：previous_vid，optional，获取之前版本的配置并替换，发布热更新的时候使用。'
  lane :switch_app do |options|
    app_id = options[:app_id]
    if app_id.nil?
      abort("\n\napp_id不能为空\n\n")
      abort("使用方法：bundle exec fastlane switch_app app_id:10\n\n")
    end

    is_inhouse = options[:is_inhouse].nil? ? false : options[:is_inhouse]
    is_dev = options[:is_dev].nil? ? false : options[:is_dev]
    base_host = options[:base_host].nil? ? nil : options[:base_host]
    previous_vid = options[:previous_vid].nil? ? 0 : options[:previous_vid]
    switch_app(app_id, is_inhouse, '?', base_host, previous_vid, '1')
  end

  desc "更新证书"
  desc "更新证书目前是一个单独的任务，每一次都会强制更新，并且把新添加的设备也更新进去"
  desc "会同时更新Company账号和Enterprise账号"
  desc "Company账号下development、adhoc、appstore这三种类型都会更新"
  desc "日常打包就不再每次更新证书，以提高打包速度"
  desc "参数：app_id"
  lane :update_certificates do |options|
    app_id = options[:app_id]
    if app_id.nil?
      abort("\n\napp_id不能为空\n\n")
      abort("使用方法：bundle exec fastlane update_certificates app_id:10\n\n")
    end

    update_certificates(app_id)
  end

  desc "更新证书"
  desc "使用方法：bundle exec fastlane update_certificate_demo_wugensongai"
  lane :update_certificate_demo_wugensongai do |options|
    update_certificate_demo_wugensongai
  end

  lane :register_all_devices do |options|
    # WZC352K2HA "Beijing New Oriental Dogwood Cultural Communications Co., Ltd." (Company/Organization)
    # 9SV7S73XXB "Beijing Rongdeji Education Technology Co., Ltd." (Company/Organization)
    # VFDJ7UN26F "Beijing Yinglang Times Cultural Development Co., Ltd." (Company/Organization)
    # Z7DFPRAC33 "Beijing Zhenyu Rich International Cultural Co.,Ltd" (Company/Organization)
    # 2KT364D29M "Hangzhou Yunti Technology Co. Ltd." (Company/Organization)
    # E99ZYE7U8L "Hangzhou Yunti Technology Co. Ltd." (In-House)
    # 3QPKV2WK9T "Zhejiang University Press Co.,Ltd." (Company/Organization)

    team_xdf = 'WZC352K2HA'
    team_rdj = '9SV7S73XXB'
    team_angxiu = 'VFDJ7UN26F'
    team_waiyu = 'Z7DFPRAC33'
    team_lfs = '3QPKV2WK9T'
    team_hhtk = '358ADJARFQ'
    team_luqi = 'D5M79568QS'

    team_bookln = '2KT364D29M'
    team_bookln_inhouse = 'E99ZYE7U8L'

    [team_xdf, team_angxiu, team_rdj, team_waiyu, team_lfs, team_bookln].each do |team_id|
      register_devices(
        username: "<EMAIL>",
        team_id: team_id,
        
        # 下面列表是所有的测试机
        devices: {
          "b-6-cl" => "44e103c3a0139f4de736f808aea47e64d19f238c",
          "b-6-cx" => "4da18ca17698f4928594cc5662ac5c9044762352",
          "b-5" => "22fb9184cbcc5dcd2058a4afa27cb713dae715bd",
          "b-6-wl" => "83f9aa1c516b7400fea8fcb6987a21528386063f",
          "b-6p" => "83c10000b1c714eb0f05c5a299c2d4543a07ff16",
          "b-iPadAir" => "649f6cc63d4009a1d5b3e37dc1da56bb475f01cf",
          "b-6-wjb" => "6459b36564bfc60849a4410c971d851efc0ed3d7",
          "b-7-zq" => "1c0dc0d97e8f6c79a4280d99d3c01e2884543306",
          "b-x-cx" => "3d317da15ddaf0e497a7e070781988d45a65d228",
          "b-x-zz" => "23d063e8b8797a300be83891a8ac2969027ad919",
          "b-7p-wws" => "f0aa41d998c631b9469102e80cc9da4c7fe4bebd",
          "b-7p-wjb" => "ba974ff1636a3b620f0e0b2afcf94160271af482",
          "b-w2-wjb" => "03fa43f1f40a72fcab30c03f293c06401cdce57b",
          "b-7-zwd" => "84a23feed8ca6f927d7981cc49f963fac6d9e846",
          "b-7-kq" => "fe30f8e03037cd2033d26509c803389085d9dc26",
          "b-iPadPro97-wjb" => "f828f748dc2c24b45c1847a3a7aa2043d662a468",
          "b-7p-mayi" => "9f79d40cff2ed7171373f7b3f4c192e6713f1a82",
          "b-6-yezi" => "bccdf007b63f1ef835108e975505d844caad554e",
          "b-x-mingyang" => "ef993a86f8769cb0535686099260e4308b72f282",
          "b-xsmax-zw" => "00008020-001A38E41E9A002E",
          "b-8p-yy" => "26f02d3ff2668d568c4daa0116308a23ff1fa3df",
          "b-xs-wm" => "00008020-000209200281002E",
          "b-xr-yellow" => "00008020-001A1D303692002E",
          "b-x-qingniu" => "05c640cf771aefa42fbb22e78bbf039401218519",
          "b-x-qingyun" => "3717f3b0d5001d20af80812176620545f65aa974",
          "b-ipad-7" => "b4022a045b80c21067b1317cb051c4cd2198052b",
          "b-11-mayi" => "00008030-00010CA23A01802E",
          "b-8p-yueyue" => "9bd00a74fa29791a7a44295a22c604400b936095",
          "b-6s-fucheng" => "fe7eec2eb667df7fd495f336e861c8cc8ec9d15a",
          "b-ipadmini2-wangling" => "14d93edbf5f9387d74fa59a0b51a147d5fb7f3b1",
          "b-11max-zhouwei" => "00008030-00026C2611E2802E",
          "b-xs-wjb" => "00008020-000231D60E68002E",
          "b-yuanshun-ipad-mini2" => "a9bab14bc694ab2d088e52eedcc7495e85531a38",
          "guzi-xs" => "00008020-000E1C993EF8002E",
          "unknown-ipad" => "00008027-000C30D01AE3002E",
          "jia-6s" => "a6aaf471ca04f7ec1f650fa75abb7ec6fc8f4587",
          "yuanshun-11" => "00008020-000D59420CE9002E",
          "zhouqinyu-iphone11" => "00008030-001029241442802E",
          "ZhouShisong-iPad" => "00008020-001C189C0A51002E",
          "b-yezi-7p-1" => "bd1fda9dd9bc7fd655e6f0b88aefb862778eef5e",
          "b-yezi-7p-2" => "42bc4e8d33be857c6d22b52c3c84a383f8695084",
          "b-ipad12.9-aini" => "00008103-001C706E1E53001E",
          "李俊辉iPhone" => "f9f586cb6f0cd305e76c8536d4c52f09673526b1",
          "李俊辉的iPhone" => "ad689f37a98274088627c089d676c7d21ef4fa1f"
        }
      )
    end
  end

  desc '自动检测最新生成的 archive 包是否有 appstore 的 ipa 包，如果有则上传至 testflight'
  desc '使用方法: bundle exec fastlane auto_detect_latest_archive_then_upload_to_testflight'
  desc '参数：app_id，required'  
  desc '参数: xcode_path, optional, Xcode 路径'
  desc '参数: fastlane_path, optional, fastlane 路径'
  lane :auto_detect_latest_archive_then_upload_to_testflight do |options|
    app_id = options[:app_id]
    xcode_path = options[:xcode_path].nil? ? nil : options[:xcode_path]
    fastlane_path = options[:fastlane_path].nil? ? nil : options[:fastlane_path]
      if app_id.nil?
        abort("\n\napp_id不能为空\n\n")
      end
    find_latest_archive_and_ipa(app_id, xcode_path, fastlane_path)
  end

  desc '测试用 修改 Xcode build phase 和 build settings，确保 dev build 可以打出 jsbundle'
  lane :modify_xcode_project_settings do
    modify_xcode_project_settings()
  end


  desc '更新 Expo.plist runtime_version'
  lane :modify_expo_plist_runtime_version do |options|
    runtime_version = options[:runtime_version]
    puts "modify_expo_plist_runtime_version - 参数"
    puts "runtime_version - #{runtime_version}"

    update_plist(
      plist_path: "bookln/Supporting/Expo.plist",
      block: proc do |plist|
        plist[:EXUpdatesRuntimeVersion] = runtime_version
      end
    )
  end

    desc '更新 Expo.plist updates_url'
    lane :modify_expo_plist_updates_url do |options|
      updates_url = options[:updates_url]
      puts "modify_expo_plist_updates_url - 参数"
      puts "updates_url - #{updates_url}"

      update_plist(
        plist_path: "bookln/Supporting/Expo.plist",
        block: proc do |plist|
          plist[:EXUpdatesURL] = updates_url
        end
      )
    end

  after_all do |lane|
    # This block is called, only if the executed lane was successful

    # slack(
    #   message: "Successfully deployed new App Update."
    # )
  end

  error do |lane, exception|
    if is_ci
      ding_talk_send_text_message("😱 #{exception.to_s}", false)
    end
  end
end

