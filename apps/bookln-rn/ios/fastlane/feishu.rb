require 'net/http'
require 'json'
require 'open-uri'
require 'openssl'
require 'base64'

def fei_shu_send_message(title, message, dev_build, qr_code_image_url)
  bookln_uri = URI.parse('https://open.feishu.cn/open-apis/bot/v2/hook/685e4f9b-1cc1-4ffa-872b-ae8261b25a9d');
  bookln_dev_build_uri = URI.parse('https://open.feishu.cn/open-apis/bot/v2/hook/fa20fab1-c741-417e-b6d8-f052ffda5583');

  if dev_build
    uri = bookln_dev_build_uri
  elsif (uri = bookln_uri)
  end

  puts "fei_shu_send_message 来了 uri = #{uri}"

  # 1.下载图片到本地
  local_path = "./ios_qr_code_image_url.jpg"
  download_image(qr_code_image_url, local_path)

  # 2.获取 token
  # TODO: 直接写死是否有风险？
  access_token = get_tenant_access_token("cli_a5d40c656dac100c", "dCUzoTfmcwj1YlW9FWR76gApbC5zG4xE")

  # 3.上传图片，获取图片 key
  qr_code_image_key = upload_image(access_token, local_path)

  puts qr_code_image_key

  # 获取当前时间戳
  timestamp_seconds = Time.now.to_i

  puts "当前时间戳 #{timestamp_seconds} #{Time.now}"

  request = Net::HTTP::Post.new(uri.to_s)
  request['Content-Type'] = 'application/json'
  fei_shu_robot_request_body = {
    "msg_type" => "post",
    "content" => {
      "post"=> {
        "zh_cn" => {
          "title" => title,
          "content" => [
            [
              {
                "tag" => "text",
                "text" => message
              },
              {
                "tag" => "img",
                "image_key" => qr_code_image_key
              }
            ]
          ]
        }
      }
    }
  }
  puts fei_shu_robot_request_body.to_json()
  request.body = fei_shu_robot_request_body.to_json()

  response = Net::HTTP.start(uri.host, uri.port, :use_ssl => uri.scheme == 'https') { |https|
    https.request(request)
  }

  puts "fei_shu_send_message 请求结果 response = #{response.body}"
end

# 获取 tenant_access_token
# https://open.feishu.cn/document/server-docs/authentication-management/access-token/tenant_access_token_internal
def get_tenant_access_token(app_id, app_secret)
  uri = URI('https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal')
  
  http = Net::HTTP.new(uri.host, uri.port)
  http.use_ssl = true

  request = Net::HTTP::Post.new(uri.path, {'Content-Type' => 'application/json; charset=utf-8'})
  request.body = { app_id: app_id, app_secret: app_secret }.to_json

  response = http.request(request)
  data = JSON.parse(response.body)

  if data['code'] == 0
    data['tenant_access_token']
  else
    raise "Failed to get tenant access token. Error: #{data['msg']}"
  end
end

# 下载图片
def download_image(url, local_path)
  File.open(local_path, 'wb') do |file|
    open(url, 'rb') do |remote_file|
      file.write(remote_file.read)
    end
  end
end

# 上传图片
def upload_image(access_token, local_path)
  uri = URI('https://open.feishu.cn/open-apis/im/v1/images')
  
  http = Net::HTTP.new(uri.host, uri.port)
  http.use_ssl = true

  request = Net::HTTP::Post.new(uri.path, {'Authorization' => "Bearer #{access_token}", 'Content-Type' => 'multipart/form-data'})
  form_data = [['image_type', 'message'], ['image', File.new(local_path)]]
  request.set_form form_data, 'multipart/form-data'

  response = http.request(request)
  data = JSON.parse(response.body)

  puts "上传图片结果 #{data}"

  if data['code'] == 0
    data['data']['image_key']
  else
    raise "Failed to upload image. Error: #{data} #{data['msg']}"
  end
end



# 手动测试时使用
# fei_shu_send_message("测试飞书推送 iOS 打包机器人消息", "itms: 我是消息内容", true, "https://yuntisyscdn.bookln.cn/tech/app/ios/languagern-92-v1.0-202311231003-ad-hoc-dev.png")