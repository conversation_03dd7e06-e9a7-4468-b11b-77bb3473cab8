<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="18122" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_7" orientation="portrait" appearance="dark"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="18093"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="428" height="926"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xtX-27-Ujo">
                                <rect key="frame" x="0.0" y="764.66666666666663" width="428" height="127.33333333333337"/>
                                <subviews>
                                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_log" translatesAutoresizingMaskIntoConstraints="NO" id="XhJ-sZ-vt4">
                                        <rect key="frame" x="151" y="41.666666666666742" width="126" height="44"/>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="XhJ-sZ-vt4" firstAttribute="centerY" secondItem="xtX-27-Ujo" secondAttribute="centerY" id="JJV-Yx-eRX"/>
                                    <constraint firstItem="XhJ-sZ-vt4" firstAttribute="centerX" secondItem="xtX-27-Ujo" secondAttribute="centerX" id="uY5-RN-n9s"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="y84-LI-Xiz">
                                <rect key="frame" x="0.0" y="44" width="428" height="720.66666666666663"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Bcu-3y-fUS"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="xtX-27-Ujo" firstAttribute="leading" secondItem="Bcu-3y-fUS" secondAttribute="leading" id="2Tw-PM-GRQ"/>
                            <constraint firstItem="Bcu-3y-fUS" firstAttribute="bottom" secondItem="xtX-27-Ujo" secondAttribute="bottom" id="Ee8-3p-1MQ"/>
                            <constraint firstItem="xtX-27-Ujo" firstAttribute="top" secondItem="y84-LI-Xiz" secondAttribute="bottom" id="LBc-Qf-b7q"/>
                            <constraint firstItem="y84-LI-Xiz" firstAttribute="top" secondItem="Bcu-3y-fUS" secondAttribute="top" id="Lh6-1y-0hw"/>
                            <constraint firstItem="Bcu-3y-fUS" firstAttribute="trailing" secondItem="xtX-27-Ujo" secondAttribute="trailing" id="SJ9-vk-nEZ"/>
                            <constraint firstItem="y84-LI-Xiz" firstAttribute="leading" secondItem="Bcu-3y-fUS" secondAttribute="leading" id="bui-mh-QRX"/>
                            <constraint firstItem="Bcu-3y-fUS" firstAttribute="trailing" secondItem="y84-LI-Xiz" secondAttribute="trailing" id="e9s-Fx-dHE"/>
                            <constraint firstItem="xtX-27-Ujo" firstAttribute="height" secondItem="Bcu-3y-fUS" secondAttribute="height" multiplier="0.15" id="vHN-em-8e5"/>
                        </constraints>
                    </view>
                    <extendedEdge key="edgesForExtendedLayout" bottom="YES"/>
                    <nil key="simulatedTopBarMetrics"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="51.86915887850467" y="374.51403887688986"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_log" width="126" height="44"/>
    </resources>
</document>