<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="15705" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="ipad12_9rounded" orientation="portrait" layout="fullscreen" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="15706"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="834" height="1194"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="img_logo" translatesAutoresizingMaskIntoConstraints="NO" id="eNc-ca-smg">
                                <rect key="frame" x="361.5" y="1074" width="111" height="50"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" verticalHuggingPriority="1000" verticalCompressionResistancePriority="1000" image="img_qdy" translatesAutoresizingMaskIntoConstraints="NO" id="sun-2N-rPt">
                                <rect key="frame" x="313.5" y="401.5" width="207" height="124"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" red="0.062745098039215685" green="0.68235294117647061" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="sun-2N-rPt" firstAttribute="centerX" secondItem="Bcu-3y-fUS" secondAttribute="centerX" id="Erf-av-3U2"/>
                            <constraint firstItem="sun-2N-rPt" firstAttribute="centerY" secondItem="Bcu-3y-fUS" secondAttribute="centerY" multiplier="0.78979" id="LnE-TZ-H0j"/>
                            <constraint firstItem="eNc-ca-smg" firstAttribute="centerX" secondItem="Bcu-3y-fUS" secondAttribute="centerX" id="PBM-NB-06R"/>
                            <constraint firstItem="Bcu-3y-fUS" firstAttribute="bottom" secondItem="eNc-ca-smg" secondAttribute="bottom" constant="50" id="rmN-AV-KAV"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="Bcu-3y-fUS"/>
                    </view>
                    <extendedEdge key="edgesForExtendedLayout" bottom="YES"/>
                    <nil key="simulatedTopBarMetrics"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52" y="374.6305418719212"/>
        </scene>
    </scenes>
    <resources>
        <image name="img_logo" width="111" height="50"/>
        <image name="img_qdy" width="207" height="124"/>
    </resources>
</document>
