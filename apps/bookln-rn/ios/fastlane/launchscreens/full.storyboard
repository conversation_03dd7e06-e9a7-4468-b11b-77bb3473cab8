<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="14109" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina5_9" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_launch" translatesAutoresizingMaskIntoConstraints="NO" id="XhJ-sZ-vt4">
                                <rect key="frame" x="0.0" y="24" width="375" height="754"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="XhJ-sZ-vt4" firstAttribute="leading" secondItem="Bcu-3y-fUS" secondAttribute="leading" id="5bt-em-MfU"/>
                            <constraint firstItem="Bcu-3y-fUS" firstAttribute="bottom" secondItem="XhJ-sZ-vt4" secondAttribute="bottom" id="Eih-bm-eow"/>
                            <constraint firstItem="XhJ-sZ-vt4" firstAttribute="top" secondItem="Bcu-3y-fUS" secondAttribute="top" constant="-20" id="dC9-Ag-qmy"/>
                            <constraint firstItem="Bcu-3y-fUS" firstAttribute="trailing" secondItem="XhJ-sZ-vt4" secondAttribute="trailing" id="sg5-Rc-98G"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="Bcu-3y-fUS"/>
                    </view>
                    <extendedEdge key="edgesForExtendedLayout" bottom="YES"/>
                    <nil key="simulatedTopBarMetrics"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="52" y="374.66266866566718"/>
        </scene>
    </scenes>
    <resources>
        <image name="ic_launch" width="375" height="532"/>
    </resources>
</document>
