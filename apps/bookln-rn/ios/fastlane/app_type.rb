
class AppType
  DEV = 'dev'
  ALPHA = 'alpha'
  STORE = 'store'

  def self.value(app_type)
    if app_type == AppType::DEV
      return 0
    elsif app_type == AppType::ALPHA
      return 1
    elsif app_type == AppType::STORE
      return 2
    end
  end

  def self.description(app_type)
    if app_type == AppType::DEV
      return '开发调试'
    elsif app_type == AppType::ALPHA
      return '内部测试'
    elsif app_type == AppType::STORE
      return '线上'
    end
  end

end