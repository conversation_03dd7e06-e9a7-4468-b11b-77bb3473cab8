import requests
import sys
if __name__ == '__main__':
    '''
    可以指定渠道生成channel.txt文件，参数格式：huawei,xiaomi,oppo,...(英文逗号分隔)
    如：python3 generate-channel-txt.py huawei,xiaomi
    如果未指定参数则走接口获取渠道数据
    '''
    channels = sys.argv[1] if len(sys.argv) > 1 else ""
    if len(channels) > 0:
        channels = channels.replace(',', '\n')
    else:
        r = requests.get(
            'https://wxmp.bookln.cn/appVersion/getChannels.do')
        if r.status_code == 200 and r.json()['success'] == True:
            data = r.json()['data']
            channels = data.replace(',', '\n')
    with open("channel.txt", "w") as f:
        f.write(channels)
