<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/rounded_corner"
    android:paddingHorizontal="16dp"
    tools:ignore="ExtraText">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="13dp">

    <TextView
        android:id="@+id/dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="#1C2024"
        android:text="提示"
        android:layout_marginBottom="8dp"/>
        <ImageView
            android:id="@+id/close_button"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_gravity="end"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:src="@drawable/ic_close"
            android:clickable="true"
            android:focusable="true"/>
    </RelativeLayout>

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textColor="#80838D"
        android:lineHeight="22dp"
        android:id="@+id/tv_content"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="16dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/negative_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@null"
            android:textColor="#4E76FF"
            android:layout_marginRight="6dp"
            android:text="取消"
            android:layout_marginEnd="4dp"/>

        <Button
            android:id="@+id/positive_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="#FFFFFF"
            android:layout_marginLeft="6dp"
            android:background="@drawable/upgrade_dialog_button"
            android:text="确定"
            android:layout_marginStart="4dp"/>
    </LinearLayout>
</LinearLayout>