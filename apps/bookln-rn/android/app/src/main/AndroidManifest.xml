<manifest xmlns:android="http://schemas.android.com/apk/res/android">


  <uses-permission android:name="android.permission.INTERNET"/>
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
  <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
  <uses-permission android:name="android.permission.VIBRATE"/>
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
  <uses-permission android:name="android.permission.RECORD_AUDIO" />
  <uses-permission android:name="android.permission.CAMERA" />
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
  <uses-permission android:name="android.permission.READ_PHONE_STATE" />
  <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
  <queries>
    <intent>
      <action android:name="android.intent.action.VIEW"/>
      <category android:name="android.intent.category.BROWSABLE"/>
      <data android:scheme="https"/>
    </intent>
  </queries>
  <queries>
    <package android:name="com.tencent.mm" />

    <package android:name="com.facebook.katana" />

    <package android:name="com.tencent.mobileqq" />

    <package android:name="com.tencent.tim" />

    <package android:name="com.tencent.minihd.qq" />

    <package android:name="com.tencent.qqlite" />
  </queries>
  <!-- android:networkSecurityConfig="@xml/network_security_config" -->
  <application 
    android:name=".MainApplication" 
    android:label="@string/app_name" 
    android:icon="@mipmap/app_logo"
    android:allowBackup="false"
    android:theme="@style/AppTheme"
  >

    <meta-data android:name="expo.modules.updates.CODE_SIGNING_METADATA" android:value="{&quot;keyid&quot;:&quot;main&quot;,&quot;alg&quot;:&quot;rsa-v1_5-sha256&quot;}"/>
    <meta-data android:name="expo.modules.updates.EXPO_SDK_VERSION" android:value="49.0.0"/>
    <meta-data android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS" android:value="0"/>
    <meta-data android:name="expo.modules.updates.EXPO_RELEASE_CHANNEL" android:value="@string/expo_release_channel"/>
    <meta-data android:name="expo.modules.updates.ENABLED" android:value="true"/>
    <meta-data android:name="expo.modules.updates.EXPO_RUNTIME_VERSION" android:value="@string/expo_runtime_version"/>
    <meta-data android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH" android:value="NEVER"/>
    <meta-data android:name="expo.modules.updates.EXPO_UPDATE_URL" android:value="https://apphotfix.zhizhuma.com/api/manifest"/>
    <activity android:name=".MainActivity" android:label="@string/app_name" android:configChanges="keyboard|keyboardHidden|orientation|screenSize|uiMode" android:launchMode="singleTask" android:windowSoftInputMode="adjustResize" android:theme="@style/Theme.App.SplashScreen" android:exported="true" android:screenOrientation="portrait">
      <intent-filter>
        <action android:name="android.intent.action.MAIN"/>
        <category android:name="android.intent.category.LAUNCHER"/>
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <category android:name="android.intent.category.BROWSABLE"/>
        <data android:scheme="com.yunti.zzm"/>
        <data android:scheme="exp+bookln"/>
      </intent-filter>
    </activity>
    <activity android:name="com.facebook.react.devsupport.DevSettingsActivity" android:exported="false"/>
    <activity
        android:name=".wxapi.WXEntryActivity"
        android:exported="true"
        android:theme="@android:style/Theme.Translucent" />
    <activity
        android:name=".wxapi.WXPayEntryActivity"
        android:exported="true"
        android:label="@string/app_name"
        android:launchMode="singleTask"
        android:taskAffinity="com.yunti.zzm"
        android:theme="@android:style/Theme.Translucent" />
    <provider android:name=".utils.DownloadFileProvider" android:authorities="${applicationId}.provider" android:grantUriPermissions="true">
      <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/file_paths" />
    </provider>
  </application>
</manifest>