package com.yunti.zzm.utils;

import static com.yunti.zzm.utils.BooklnNativeHelper.REQ_CODE_UNKNOWN_APP_SOURCES;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.AlertDialog;
import android.app.Dialog;
import android.app.DownloadManager;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.FileProvider;

import com.afollestad.materialdialogs.DialogAction;
import com.afollestad.materialdialogs.MaterialDialog;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReadableMap;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import com.yunti.zzm.MainApplication;
import com.yunti.zzm.R;


/**
 * 工具类
 */

public class Utils {

    private static final String ANDROID_DATA = "/Android/data";

    private static final String PREFRENCE_COMMON = "PREFRENCE_COMMON";

    /**
     * 获取/Android/data的根路径
     */
    public static String getRootExternalStorage() {
        return Environment.getExternalStorageDirectory().getAbsolutePath() + ANDROID_DATA;
    }


    public static String getMetaValue(Context context, String key) throws Exception {
        if (context == null) {
            return "";
        }
        if (TextUtils.isEmpty(key)) {
            return "";
        }

        ApplicationInfo appInfo = context.getPackageManager().getApplicationInfo(context.getPackageName(), PackageManager.GET_META_DATA);

        if (appInfo != null && appInfo.metaData != null && appInfo.metaData.containsKey(key)) {

            return appInfo.metaData.getString(key);
        } else {
            return "";
        }
    }

    public static Integer getAppId(Context context) throws PackageManager.NameNotFoundException {
        if (context == null) {
            return null;
        }
        ApplicationInfo appInfo = context.getPackageManager().getApplicationInfo(context.getPackageName(), PackageManager.GET_META_DATA);

        if (appInfo != null && appInfo.metaData != null) {

            return appInfo.metaData.getInt("appId");
        } else {
            return null;
        }
    }


    public static String colorToRGBA(int color) {
        return String.format(Locale.CHINA, "rgba(%d,%d,%d,1)", Color.red(color), Color.green(color), Color.blue(color));
    }

    public static String format(String format, Object... args) {
        return String.format(Locale.CHINA, format, args);
    }


    public static void encrypt(byte[] bytes, int offset, int length) {
        modifyBytes(bytes, offset, length);
    }

    public static void decrypt(byte[] bytes, int offset, int length) {
        modifyBytes(bytes, offset, length);
    }

    private static void modifyBytes(byte[] bytes, int offset, int length) {
        if (bytes == null) {
            return;
        }
        for (int n = offset; n < offset + length; n++) {
            bytes[n] = (byte) (bytes[n] ^ getKeyInt());
        }
    }

    private static final char key = 'y';

    private static final String KEY_CUR_VERSION_FIRST_APP_EXT = "KEY_CUR_VERSION_FIRST_APP_EXT";
    private static final String KEY_DOWNLOAD_APK_ID = "KEY_DOWNLOAD_APK_ID";

    public static SharedPreferences getSharedPref(Context context) {
        return context.getSharedPreferences(PREFRENCE_COMMON, Context.MODE_PRIVATE);
    }

    public static boolean isCurrentVersionFirstAppExt(Context context) {
        return getSharedPref(context).getBoolean(getKeyCurVersionFirstAppExt(getVersionName()), true);
    }

    public static void setCurrentVersionFirstAppExt(Context context,boolean isFirst) {
        getSharedPref(context).edit().putBoolean(getKeyCurVersionFirstAppExt(getVersionName()), isFirst).apply();
    }

    public static long getDownloadApkId(Context context) {
        int versionCode = getVersionCode(context);
        return getSharedPref(context)
                .getLong(format("%s_%d", KEY_DOWNLOAD_APK_ID, versionCode), 0);
    }

    public static void setDownloadApkId(Context context, long downloadApkId) {
        int versionCode = getVersionCode(context);
        getSharedPref(context).edit()
                .putLong(format("%s_%d", KEY_DOWNLOAD_APK_ID, versionCode), downloadApkId).apply();
    }


    private static String getVersionName() {
        Context context = MainApplication.getApp();
        try {
            PackageInfo pInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), 0);
            return pInfo.versionName;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "v1.0";
    }

    public static String strFromReadableMap(ReadableMap readableMap, String key) {
        return readableMap.hasKey(key) ? readableMap.getString(key) : "";
    }


    private static String getKeyCurVersionFirstAppExt(String versionName) {
        return Utils.format("%s_%s", KEY_CUR_VERSION_FIRST_APP_EXT, versionName);
    }

    /**
     * dp转px
     */
    public static float dp2px(float dp) {
        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dp, MainApplication.getApp().getResources().getDisplayMetrics());
    }


    private static String getExternalCachesDirectory(Context context) {
        File externalCachesDirectory = context.getExternalCacheDir();
        if (externalCachesDirectory != null) {
            return externalCachesDirectory.getAbsolutePath();
        } else {
            return String.format(Locale.CHINA, "%s/Android/data/%s/cache",
                    getExternalStorageDirectoryPath(), context.getPackageName());
        }
    }


    private static String getExternalStorageDirectoryPath() {
        return Environment.getExternalStorageDirectory().getAbsolutePath();
    }

    public static void installApk(String apkPath, final Context context, String url) {
        if (!canInstall(context)) {
            return;
        }
        Intent install = new Intent(Intent.ACTION_VIEW);
        install.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        File apkFile = new File(apkPath);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            try {
                install.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                String authority = context.getPackageName() + ".provider";
                Uri contentUri = FileProvider.getUriForFile(context, authority, apkFile);
                install.setDataAndType(contentUri, "application/vnd.android.package-archive");
            } catch (Exception exception) {
                if (url != null) {
                    final Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                    context.startActivity(intent);
                }
            }

        } else {
            install.setDataAndType(Uri.fromFile(apkFile), "application/vnd.android.package-archive");
        }
        context.startActivity(install);
    }

    public static boolean immediateInstallApkIfDownloaded(Context context) {
        long dmId = Utils.getDownloadApkId(context);
        return immediateInstallApkIfDownloaded(context, dmId);
    }

    public static boolean immediateInstallApkIfDownloaded(Context context, long dmId) {
        Map<String, Object> result = getDMQueryResultById(dmId, context);
        if (result.size() > 0) {
            int status = (int) result.get("status");
            switch (status) {
                //下载成功
                case DownloadManager.STATUS_SUCCESSFUL:
                    String localUri = (String) result.get("localUri");
                    String uri = (String) result.get("uri");
                    File apkFile = new File(Uri.parse(localUri).getPath());
                    //文件存在
                    if (apkFile.exists()) {
                        installApk(apkFile.getAbsolutePath(), context, uri);
                        return true;
                    }
                    return false;
                //下载中、等待下载、暂停下载
                case DownloadManager.STATUS_PENDING:
                case DownloadManager.STATUS_RUNNING:
                case DownloadManager.STATUS_PAUSED:
                    return true;
                default:
                    return false;
            }
        }
        return false;
    }

    @SuppressLint("Range")
    public static Map<String, Object> getDMQueryResultById(long dmID, Context context) {
        DownloadManager.Query query = new DownloadManager.Query();
        query.setFilterById(dmID);
        DownloadManager dm = (DownloadManager) context.getSystemService(Context.DOWNLOAD_SERVICE);
        Cursor c = null;
        Map<String, Object> result = new HashMap<>();
        try {
            c = dm.query(query);
            if (c != null && c.moveToNext()) {
                result.put("status", c.getInt(c.getColumnIndex(DownloadManager.COLUMN_STATUS)));
                result.put("localUri", c.getString(c.getColumnIndex(DownloadManager.COLUMN_LOCAL_URI)));
                result.put("uri", c.getString(c.getColumnIndex(DownloadManager.COLUMN_URI)));
                result.put("progress", c.getInt(c
                        .getColumnIndex(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR)));
                result.put("total", c.getInt(c.getColumnIndex(DownloadManager.COLUMN_TOTAL_SIZE_BYTES)));
            }
        } catch (Exception e) {
            //ignore
        } finally {
            if (c != null) {
                c.close();
            }
        }
        return result;
    }

    @SuppressLint("ResourceType")
    private static boolean canInstall(final Context context) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            return true;
        }
        boolean canInstall = context.getPackageManager().canRequestPackageInstalls();
        if (canInstall) {
            return true;
        }
        if (context instanceof ReactApplicationContext) {
            final Activity activity = ((ReactApplicationContext) context).getCurrentActivity();
            if (activity != null) {
                String appName = "App";
                try {
                    ApplicationInfo applicationInfo = context.getApplicationInfo();
                    int stringId = applicationInfo.labelRes;
                    appName = stringId == 0 ? applicationInfo.nonLocalizedLabel.toString() : context.getString(stringId);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                Dialog dialog = new Dialog(activity) ;
                LinearLayout customView = (LinearLayout) LayoutInflater.from(activity).inflate(R.layout.custom_upgrade_alert_dialog,null);
                if (customView != null) {
                    Button positiveButton = customView.findViewById(R.id.positive_button);
                    Button negativeButton = customView.findViewById(R.id.negative_button);
                    TextView tvContent = customView.findViewById(R.id.tv_content);
                    String content = "为了正常升级“" + appName + "“,请允许“" + appName + "“安装未知来源应用,本功能只限用于版本升级";
                    tvContent.setText(content);
                    ImageView closeButton = customView.findViewById(R.id.close_button);
                    closeButton.setOnClickListener(v->{
                        dialog.dismiss();
                    });
                    positiveButton.setOnClickListener(v -> {
                        openSettingUnknown(context);
                        dialog.dismiss();
                    });

                    negativeButton.setOnClickListener(v -> dialog.dismiss());
                }
                dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                dialog.setContentView(customView);
                dialog.show();

            } else {
                openSettingUnknown(context);
            }
        } else {
            openSettingUnknown(context);
        }
        return false;

    }

    private static void openSettingUnknown(Context context) {
        //启动授权页面
        Intent intent = new Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES,
                Uri.parse("package:" + context.getPackageName()));
        Activity activity = null;
        if (context instanceof ReactApplicationContext) {
            activity = ((ReactApplicationContext) context).getCurrentActivity();
        }
        if (activity != null) {
            activity.startActivityForResult(intent, REQ_CODE_UNKNOWN_APP_SOURCES);
        } else {
            context.startActivity(intent);
        }
    }


    private static PackageInfo getPackageInfo(Context context) {
        PackageManager packageManager = context.getPackageManager();
        PackageInfo packageInfo = null;
        try {
            packageInfo = packageManager.getPackageInfo(context.getPackageName(), 0);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return packageInfo;
    }

    private static int getVersionCode(Context context) {
        PackageInfo packageInfo = getPackageInfo(context);
        return packageInfo != null ? packageInfo.versionCode : 0;
    }

    public static String getTopActivityPackage(Context context) {
        ActivityManager mActivityManager = (ActivityManager) context.getApplicationContext().getSystemService(Context.ACTIVITY_SERVICE);
        List runningTaskInfos = mActivityManager.getRunningTasks(1);
        if (runningTaskInfos != null && runningTaskInfos.size() > 0) {
            ActivityManager.RunningTaskInfo info = (ActivityManager.RunningTaskInfo) runningTaskInfos.get(0);
            return info.topActivity.getClassName();
        }
        return "";
    }

    private static int getKeyInt() {
        return 235;
    }


}
