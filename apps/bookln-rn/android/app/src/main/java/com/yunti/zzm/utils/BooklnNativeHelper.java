package com.yunti.zzm.utils;

import android.app.Activity;
import android.app.DownloadManager;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;

import androidx.annotation.NonNull;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ActivityEventListener;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.WritableMap;
import com.tencent.vasdolly.helper.ChannelReaderUtil;

import org.apache.commons.io.IOUtils;

import java.io.InputStream;
import java.util.Map;

import com.yunti.zzm.MainApplication;

public class BooklnNativeHelper extends ReactContextBaseJavaModule {

    private static final String NAME = "BooklnNativeHelper";

    private static final String CONFIG_NAME = "config.json";

    static final int REQ_CODE_UNKNOWN_APP_SOURCES = 2019;

    public BooklnNativeHelper(final ReactApplicationContext reactContext) {
        super(reactContext);
         reactContext.addActivityEventListener(new ActivityEventListener() {
            @Override
            public void onActivityResult(Activity activity, int requestCode, int resultCode, Intent data) {
                if(requestCode == REQ_CODE_UNKNOWN_APP_SOURCES && resultCode == -1){
                    Utils.immediateInstallApkIfDownloaded(reactContext);
                }
            }
            @Override
            public void onNewIntent(Intent intent) {

            }
        });
    }
    @NonNull
    @Override
    public String getName() {
        return NAME;
    }

    /**
     *  获取当前的渠道
     * @param promise
     */
    @ReactMethod
    public void getCurrentChannel(Promise promise){
        String channel = ChannelReaderUtil.getChannel(getReactApplicationContext());
        promise.resolve(channel);
    }

    @ReactMethod
    public void isMarketInstalled(String brand, Promise promise) {
        Activity currentActivity = getCurrentActivity();
        if (currentActivity == null) {
            promise.reject("", "activity为null");
            return;
        }
        boolean isInstall = MarketTools.getTools().isMarketInstalled(currentActivity, brand);
        promise.resolve(isInstall);
    }

    @ReactMethod
    public void openAndroidAppStore(String brand, Promise promise) {
        Activity currentActivity = getCurrentActivity();
        if (currentActivity == null) {
            promise.reject("", "activity为null");
            return;
        }
        boolean isInstall = MarketTools.getTools().openAndroidAppStore(currentActivity, brand);
        promise.resolve(isInstall);
    }

    @ReactMethod
    public void downloadAndInstallApk(ReadableMap params, Callback callback) {
        if (Utils.immediateInstallApkIfDownloaded(getReactApplicationContext())) {
            callback.invoke(DownloadManager.STATUS_SUCCESSFUL);
            return;
        }
        if (params.hasKey("url")) {
            Uri uri = Uri.parse(params.getString("url"));
            DownloadManager.Request req = new DownloadManager.Request(uri);
            req.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE);
            if (params.hasKey("title")) {
                req.setTitle(params.getString("title"));
            }
            if (params.hasKey("path")) {
                req.setDestinationUri(Uri.parse("file://" + params.getString("path")));
            }
            Context appCtx = getReactApplicationContext();
            DownloadManager dm = (DownloadManager) appCtx.getSystemService(Context.DOWNLOAD_SERVICE);
            long downloadManagerId = dm.enqueue(req);
            appCtx.registerReceiver(
                    new DownloadApkBroadcastReceiver(downloadManagerId, callback, getReactApplicationContext()),
                    new IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE));
            Utils.setDownloadApkId(appCtx, downloadManagerId);
        } else {
            callback.invoke(DownloadManager.STATUS_FAILED);
        }
    }

    @ReactMethod
    public void goSystemHome() {
        Intent intent = new Intent(Intent.ACTION_MAIN);
        intent.addCategory(Intent.CATEGORY_HOME);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        MainApplication.getApp().startActivity(intent);
    }

    @ReactMethod
    public void queryApkDownloadStatusAndReportProgress(Promise promise) {
        Context context = getReactApplicationContext();
        long dmId = Utils.getDownloadApkId(context);
        Map<String, Object> result = Utils.getDMQueryResultById(dmId, context);
        if (result.size() > 0) {
            int status = (int) result.get("status");
            switch (status) {
                // 下载成功、下载中、等待下载、暂停下载
                case DownloadManager.STATUS_SUCCESSFUL:
                case DownloadManager.STATUS_PENDING:
                case DownloadManager.STATUS_RUNNING:
                case DownloadManager.STATUS_PAUSED:
                    WritableMap args = Arguments.createMap();
                    args.putInt("status", status);
                    args.putInt("progress", (Integer) result.get("progress"));
                    args.putInt("total", (Integer) result.get("total"));
                    args.putString("localUri", (String) result.get("localUri"));
                    promise.resolve(args);
                    break;
                default:
                    promise.reject(new Exception(Utils.format("error status is %d ", status)));
                    break;
            }
        } else {
            promise.reject(new Exception("the apk in the download does not exist"));
        }
    }

    @ReactMethod
    public void readBaseInfoJsonWithCallback(Callback callback) {
        if (getReactApplicationContext() != null) {
            InputStream inputStream = null;
            try {
                inputStream = getReactApplicationContext().getAssets().open(CONFIG_NAME);
                String value = IOUtils.toString(inputStream,"utf-8") ;
                callback.invoke(value);
            } catch (Exception e) {
                e.printStackTrace();
                callback.invoke(e.getMessage(), null);
            } finally {
                IOUtils.closeQuietly(inputStream);
            }
        }
    }
}
