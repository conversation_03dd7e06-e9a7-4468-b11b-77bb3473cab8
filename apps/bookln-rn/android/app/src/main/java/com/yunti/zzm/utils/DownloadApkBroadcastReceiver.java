package com.yunti.zzm.utils;


import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.facebook.react.bridge.Callback;

public class DownloadApkBroadcastReceiver extends BroadcastReceiver {

    private final long mDownloadManagerId;
    private Callback mCallback;
    private final Context mContext;

    public DownloadApkBroadcastReceiver(long downloadManagerId, Callback callback, Context context) {
        mDownloadManagerId = downloadManagerId;
        mCallback = callback;
        mContext = context;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (DownloadManager.ACTION_DOWNLOAD_COMPLETE.equals(action)) {
            long id = intent.getExtras().getLong(DownloadManager.EXTRA_DOWNLOAD_ID);
            if (id == this.mDownloadManagerId) {
                boolean result = Utils.immediateInstallApkIfDownloaded(mContext, mDownloadManagerId);
                if (this.mCallback != null) {
                    this.mCallback.invoke(result ? DownloadManager.STATUS_SUCCESSFUL : DownloadManager.STATUS_FAILED);
                    this.mCallback = null;
                }
            }
        }
    }
}
