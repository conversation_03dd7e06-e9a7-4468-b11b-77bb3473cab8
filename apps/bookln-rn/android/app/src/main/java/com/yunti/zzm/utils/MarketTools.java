package com.yunti.zzm.utils;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.text.TextUtils;

import androidx.annotation.NonNull;

public class MarketTools {

    private static MarketTools tools;
    private static final String schemaUrl = "market://details?id=";

    public static MarketTools getTools() {
        if (null == tools) {
            tools = new MarketTools();
        }
        return tools;
    }


    public boolean openAndroidAppStore(Context mContext, String deviceBrand) {
        //根据厂商获取对应市场的包名
        String brandName = deviceBrand.toUpperCase();//大写
        if (TextUtils.isEmpty(brandName)) {
            return false;
        }
        String[] marketPackageName = getBrandName(brandName);
        if (marketPackageName == null || marketPackageName.length == 0) {
            //如果不是手机品牌内的，弹出所有的
            return startMarket(mContext, mContext.getPackageName(), null);
        } else {
            if (marketPackageName.length == 1) {
                return startMarket(mContext, mContext.getPackageName(), marketPackageName[0]);
            } else {
                String oppoMarketPackageName = null;
                for (String name : marketPackageName) {
                    if (isInstalled(name, mContext)) {
                        oppoMarketPackageName = name;
                    }
                }
                if (oppoMarketPackageName == null) {
                    return false;
                } else {
                    return startMarket(mContext, mContext.getPackageName(), oppoMarketPackageName);
                }
            }

        }

    }


    public boolean startMarket(Context mContext, String packageName, String marketPackageName) {
        return openMarket(mContext, packageName, marketPackageName);
    }


    private boolean openMarket(Context mContext, String packageName, String marketPackageName) {
        try {
            Uri uri = Uri.parse(schemaUrl + packageName);
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            if (marketPackageName != null) {
                intent.setPackage(marketPackageName);
            }
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            mContext.startActivity(intent);
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    /****
     * 检查APP是否安装
     */
    private boolean isInstalled(@NonNull String packageName, Context context) {
        if ("".equals(packageName) || packageName.length() <= 0) {
            return false;
        }
        PackageInfo packageInfo;
        try {
            packageInfo = context.getPackageManager().getPackageInfo(packageName, 0);
        } catch (PackageManager.NameNotFoundException e) {
            packageInfo = null;
        }
        if (packageInfo == null) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 是否安装了手机品牌的应用市场
     */
    public boolean isMarketInstalled(Context context, String deviceBrand) {
        String brandName = deviceBrand.toUpperCase();//大写
        if (TextUtils.isEmpty(brandName)) {
            return false;
        }
        String[] marketPackageNames = getBrandName(brandName);
        if (marketPackageNames.length == 0) {
            return false;
        }
        if (BRAND.OPPO_BRAND.equals(brandName)) {
            boolean isInstall = false;
            for (String marketPackageName : marketPackageNames) {
                if (isInstalled(marketPackageName, context)) {
                    isInstall = true;
                }
            }
            return isInstall;
        } else {
            String marketPackageName = marketPackageNames[0];
            return isInstalled(marketPackageName, context);
        }
    }


    /***
     * 检测是否是应用宝
     */
    public boolean isCheckInstallYYB(Context mContext) {
        boolean installed = isInstalled(PACKAGE_NAME.TENCENT_PACKAGE_NAME[0], mContext);
        return installed;
    }


    private String[] getBrandName(String brandName) {
        if (BRAND.HUAWEI_BRAND.equals(brandName)) {
            //华为
            return PACKAGE_NAME.HUAWEI_PACKAGE_NAME;
        } else if (BRAND.OPPO_BRAND.equals(brandName)) {
            //oppo
            return PACKAGE_NAME.OPPO_PACKAGE_NAME;
        } else if (BRAND.VIVO_BRAND.equals(brandName)) {
            //vivo
            return PACKAGE_NAME.VIVO_PACKAGE_NAME;
        } else if (BRAND.XIAOMI_BRAND.equals(brandName)) {
            //小米
            return PACKAGE_NAME.XIAOMI_PACKAGE_NAME;
        } else if (BRAND.QH360_BRAND.equals(brandName)) {
            //360
            return PACKAGE_NAME.QH360_PACKAGE_NAME;
        } else if (BRAND.HONOR_BRAND.equals(brandName)) {
            //荣耀
            return PACKAGE_NAME.HUAWEI_PACKAGE_NAME;
        } else if (BRAND.YINGYONGBAO_BRAND.equals(brandName)) {
            //应用宝
            return PACKAGE_NAME.TENCENT_PACKAGE_NAME;
        }
        return new String[]{};
    }

    /**
     * 获取手机厂商
     */
    public String getDeviceBrand() {
        return android.os.Build.BRAND;
    }

    public static class BRAND {
        public static final String HUAWEI_BRAND = "HUAWEI";//HUAWEI_PACKAGE_NAME
        public static final String HONOR_BRAND = "HONOR";//HUAWEI_PACKAGE_NAME
        public static final String OPPO_BRAND = "OPPO";//OPPO_PACKAGE_NAME
        public static final String VIVO_BRAND = "VIVO";//VIVO_PACKAGE_NAME
        public static final String XIAOMI_BRAND = "XIAOMI";//XIAOMI_PACKAGE_NAME
        public static final String QH360_BRAND = "360";//QH360_PACKAGE_NAME
        public static final String YINGYONGBAO_BRAND = "YINGYONGBAO";//QH360_PACKAGE_NAME
    }


    public static class PACKAGE_NAME {
        public static final String[] OPPO_PACKAGE_NAME = new String[]{"com.oppo.market", "com.heytap.market"};//oppo
        public static final String[] VIVO_PACKAGE_NAME = new String[]{"com.bbk.appstore"};//vivo
        public static final String[] HUAWEI_PACKAGE_NAME = new String[]{"com.huawei.appmarket"};//华为
        public static final String[] QH360_PACKAGE_NAME = new String[]{"com.qihoo.appstore"};//360
        public static final String[] XIAOMI_PACKAGE_NAME = new String[]{"com.xiaomi.market"};//小米
        public static final String[] TENCENT_PACKAGE_NAME = new String[]{"com.tencent.android.qqdownloader"};//应用宝
    }

}
