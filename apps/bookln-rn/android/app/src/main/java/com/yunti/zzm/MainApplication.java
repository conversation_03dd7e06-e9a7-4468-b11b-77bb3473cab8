package com.yunti.zzm;

import android.app.Application;
import android.content.res.Configuration;
import android.util.Log;

import androidx.annotation.NonNull;

import com.facebook.react.PackageList;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactInstanceEventListener;
import com.facebook.react.ReactInstanceManager;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.config.ReactFeatureFlags;
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint;
import com.facebook.react.defaults.DefaultReactNativeHost;
import com.facebook.soloader.SoLoader;
import com.yunti.expoupdateshelper.ExpoUpdatesHelperModule;
import com.yunti.expoupdateshelper.ExpoUpdatesHelperUtil;
import com.tencent.vasdolly.helper.ChannelReaderUtil;
import com.umeng.commonsdk.UMConfigure;

import org.json.JSONException;

import expo.modules.ApplicationLifecycleDispatcher;
import expo.modules.ReactNativeHostWrapper;
import expo.modules.updates.UpdatesController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MainApplication extends Application implements ReactApplication {

    public static MainApplication instance;

    private final ReactNativeHost mReactNativeHost =
            new ReactNativeHostWrapper(this, new DefaultReactNativeHost(this) {
                @Override
                public boolean getUseDeveloperSupport() {
                    return BuildConfig.DEBUG;
                }

                @Override
                protected List<ReactPackage> getPackages() {
                    @SuppressWarnings("UnnecessaryLocalVariable")
                    List<ReactPackage> packages = new PackageList(this).getPackages();
                    packages.add(new BooklnReactPackage());
                    // Packages that cannot be autolinked yet can be added manually here, for example:
                    // packages.add(new MyReactNativePackage());
                    return packages;
                }

                @Override
                protected String getJSMainModuleName() {
                    return ".expo/.virtual-metro-entry";
                }

                @Override
                protected boolean isNewArchEnabled() {
                    return BuildConfig.IS_NEW_ARCHITECTURE_ENABLED;
                }

                @Override
                protected Boolean isHermesEnabled() {
                    return BuildConfig.IS_HERMES_ENABLED;
                }
            });

    public static MainApplication getApp() {
        return (MainApplication) instance;
    }

    @Override
    public ReactNativeHost getReactNativeHost() {
        return mReactNativeHost;
    }

    /**
     * 读取并设置 Expo 热更新配置
     */
    public void readAndOverrideExpoUpdateConfiguration() {
        Log.d("bookln", "onReactContextInitialized 来了");
        // 注入热更新配置到 Expo Updates
        ExpoUpdatesHelperUtil helperInstance = new ExpoUpdatesHelperUtil(MainApplication.this);
        try {
            Map<String, Object> updatesConfig = helperInstance.getExpoUpdateConfig();
            Map<String, Object> configurationForExpoAndroid = helperInstance.transformToEXUpdatesConfigurationWithExpoUpdateConfig(updatesConfig);
            Log.d("bookln", "configurationForExpoAndroid" + configurationForExpoAndroid);
            UpdatesController.overrideConfiguration(MainApplication.this, configurationForExpoAndroid);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;
        // UMConfigure.preInit(this,"677786347e5e6a4eebd0fba7", ChannelReaderUtil.getChannel(this));
        SoLoader.init(this, /* native exopackage */ false);
        if (!BuildConfig.REACT_NATIVE_UNSTABLE_USE_RUNTIME_SCHEDULER_ALWAYS) {
            ReactFeatureFlags.unstable_useRuntimeSchedulerAlways = false;
        }
        if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
            // If you opted-in for the New Architecture, we load the native entry point for this app.
            DefaultNewArchitectureEntryPoint.load();
        }

        readAndOverrideExpoUpdateConfiguration();

        ReactNativeFlipper.initializeFlipper(this, getReactNativeHost().getReactInstanceManager());
        ApplicationLifecycleDispatcher.onApplicationCreate(this);
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        ApplicationLifecycleDispatcher.onConfigurationChanged(this, newConfig);
    }
}
