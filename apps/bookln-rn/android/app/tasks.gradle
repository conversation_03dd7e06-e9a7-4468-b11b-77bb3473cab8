import groovy.json.JsonOutput
import groovy.json.JsonSlurper

def configJson
def appId = 2
def useLogoResize = 144
def usePreviousVid = 0

static void makeJsonFile(fileName, content) {
    makeFile(fileName, JsonOutput.toJson(content))
}

static void makeFile(fileName, content) {
    File file = new File(fileName)
    if (!file.getParentFile().exists()) {
        file.getParentFile().mkdirs()
    }
    def writer = new FileWriter(fileName)
    writer.write(content)
    writer.flush()
    writer.close()
}

task generateConfig {
    if (project.hasProperty('logoResize')) {
        useLogoResize = Integer.parseInt(logoResize)
    }
    if (project.hasProperty('previousVid')) {
        usePreviousVid = Integer.parseInt(previousVid)
    }

    def urlOfCurrentVersion = String.format("https://app-prepub.bookln.cn/appservice/miniappinfo.do?appid=%d&type=2", appId)
    def urlOfPreviousVersion = String.format("https://app-prepub.bookln.cn/appservice/historyMiniappinfo.do?appid=%d&type=2&vid=%d", appId, usePreviousVid)
    def configUrl = usePreviousVid != 0 ? urlOfPreviousVersion : urlOfCurrentVersion
    //make config.gradle
    def json = new JsonSlurper().parse(new URL(configUrl), "utf-8")
    def dataJson = json.get("data")
    configJson = dataJson
    configJson.miniAPPVersion = configJson.miniAPPVersion.replace("v", "")
    //update baseHost if need
    if (project.hasProperty('baseHost')) {
        dataJson.appExt.baseHost = baseHost
    }
    //make config.json
    dataJson.appExt = ""
    makeJsonFile("${rootProject.rootDir}/app/src/main/assets/config.json", dataJson)
}


