// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = findProperty('android.buildToolsVersion') ?: '33.0.0'
        minSdkVersion = Integer.parseInt(findProperty('android.minSdkVersion') ?: '21')
        compileSdkVersion = Integer.parseInt(findProperty('android.compileSdkVersion') ?: '33')
        targetSdkVersion = Integer.parseInt(findProperty('android.targetSdkVersion') ?: '33')
        kotlinVersion = findProperty('android.kotlinVersion') ?: '1.8.10'
        frescoVersion = findProperty('expo.frescoVersion') ?: '2.5.0'

        // We use NDK 23 which has both M1 support and is the side-by-side NDK version from AGP.
        ndkVersion = "23.1.7779620"

        supportLibVersion = "28.0.0"
        androidXAnnotation = "1.1.0"
        androidXBrowser = "1.0.0"
    }
    repositories {
        google()
        mavenCentral()
        maven {url 'https://maven.aliyun.com/repository/public'}
    }
    dependencies {
        classpath('com.android.tools.build:gradle:7.4.2')
        classpath('com.facebook.react:react-native-gradle-plugin')
        classpath('com.tencent.vasdolly:plugin:3.0.6')
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
    }
}

allprojects {
    repositories {
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url(new File(['node', '--print', "require.resolve('react-native/package.json')"].execute(null, rootDir).text.trim(), '../android'))
        }
        maven {
            // Android JSC is installed from npm
            url(new File(['node', '--print', "require.resolve('jsc-android/package.json')"].execute(null, rootDir).text.trim(), '../dist'))
        }
        maven {
            url(new File(['node', '--print', "require.resolve('expo-camera/package.json')"].execute(null, rootDir).text.trim(), '../android/maven'))
        }

//        maven(
//                url( new File(["node", "--print", "require.resolve('expo-camera/package.json')"].execute(null, rootDir).text.trim(), "../android/maven") )
//        )

        google()
        mavenCentral()
        maven { url 'https://www.jitpack.io' }
        maven { url 'https://maven.aliyun.com/repository/public' }
    }
}
