rootProject.name = 'bookln'

apply from: new File(["node", "--print", "require.resolve('expo/package.json')"].execute(null, rootDir).text.trim(), "../scripts/autolinking.gradle");
useExpoModules()

apply from: new File(["node", "--print", "require.resolve('@react-native-community/cli-platform-android/package.json')"].execute(null, rootDir).text.trim(), "../native_modules.gradle");
applyNativeModulesSettingsGradle(settings)

include ':native-wechat'
project(':native-wechat').projectDir = new File(rootProject.projectDir, '../../../node_modules/native-wechat/android')

include ':expo-camera'
project(':expo-camera').projectDir = new File(rootProject.projectDir, '../../../node_modules/expo-camera/android')

include ':app'
includeBuild(new File(["node", "--print", "require.resolve('@react-native/gradle-plugin/package.json')"].execute(null, rootDir).text.trim()).getParentFile())
