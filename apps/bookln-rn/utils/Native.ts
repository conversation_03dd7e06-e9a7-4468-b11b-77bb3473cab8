import { NativeModules, Platform } from 'react-native';
import type { BaseInfo } from '../dtos/BaseInfo';

class Native {
  /** 获取android当前渠道 */
  getCurrentAndroidChannel = (): Promise<string | null> => {
    if (Platform.OS === 'android') {
      return NativeModules.BooklnNativeHelper.getCurrentChannel();
    } else {
      return Promise.resolve(null);
    }
  };

  isMarketInstalled = (brand: string): Promise<boolean> => {
    if (Platform.OS === 'android') {
      return NativeModules.BooklnNativeHelper.isMarketInstalled(brand);
    } else {
      return Promise.resolve(false);
    }
  };

  openAndroidAppStore = (brand: string): Promise<boolean> => {
    if (Platform.OS === 'android') {
      return NativeModules.BooklnNativeHelper.openAndroidAppStore(brand);
    } else {
      return Promise.resolve(false);
    }
  };

  readBaseInfo = async (): Promise<null | BaseInfo> => {
    return new Promise((resolve, reject) => {
      NativeModules.BooklnNativeHelper.readBaseInfoJsonWithCallback((jsonString: null | string) => {
        if (jsonString) {
          const baseInfo = JSON.parse(jsonString);
          resolve(baseInfo as BaseInfo);
        } else {
          resolve({});
        }
      });
    });
  };

  // TODO: leejunhui - 热更新测试 - JS 代码调用了不存在于 App 中的原生代码 (2025_03_04)
  hotfixTest = async (): Promise<boolean> => {
    return await NativeModules.BooklnNativeHelper.hotfixTest();
  };
}

export const native = new Native();
