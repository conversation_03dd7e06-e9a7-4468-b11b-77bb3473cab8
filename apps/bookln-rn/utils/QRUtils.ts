import { container } from '@jgl/container';
import { router } from '@jgl/utils';
import { showToast } from '@yunti-private/jgl-ui';
import { bookhomedetail, queryBookList } from '../api/BookServiceApi';
import { getRealMpHost } from './mpUrlHostHelper';
import { routerMap } from './routerMap';
import {
  navigateToNativeScreenIfPossible,
  navigateToWebBook,
  openWithProps,
  tryDecodeURIComponent,
} from './WebViewHelper';
import { isOfficialNormalUrl, isOfficialQrCodeUrl, stringIsUrl } from './QRUtil';
import { query } from '../api/CRCodeApi';
import type { CrCodeDTO, CRCodeResult, ResourceDTO } from '../dtos/CRCodeResultDTO';
import { crCodeTypes } from '../types';
import { mpResourceUrl } from '../api/ResourceServiceApi';

enum cRAuthErrorCodeEnum {
  /** 认证成功 */
  Success = 0,
  /** 需要登录 */
  NeedLogin = 1,
  /** 需要学习卡，会返回具体的学习卡名 */
  NeedBookAuth = 2,
  /** 资源不存在 */
  ResourceNoExist = 3,
  /** 二维码不存在或已经下线 */
  CrcodeNoExist = 4,
  /** 认证失败，未知错误 */
  Fail = 5,
  /** 需要指定用户才能访问 */
  CrcodeSpecificUser = 6,
  /** 非官方二维码 */
  UnofficialCode = 7,
  /** 需要申请访问 */
  NeedApply = 8,
  /** 需要购买后访问 */
  NeedBuy = 9,
  /** 书籍需要购买 */
  NeedBuyBook = 10,
  /** 教师专享 */
  TeacherExclusive = 11,
}

export interface QrInfo {
  cRCode: CrCodeDTO;
  isFromQr?: boolean;
  isFreeOrIsBuy?: boolean;
  callback?: () => void;
}

/**
 * 处理条形码
 * @param barCode 条形码
 */
export const treatBarCode = async (barCode: string) => {
  const response = await container.net().fetch(queryBookList({ isbn: barCode }));
  const { success, data: bookList, msg } = response;
  if (success && bookList && bookList.length > 0) {
    if (bookList.length === 1 && bookList[0]) {
      const { id, idSign } = bookList[0];
      // 一本书
      navigateToWebBook({
        bookId: `${id}`,
        idSign,
      });
    } else {
      // 多本书
      router.push(routerMap.ScanChooseBookList, {
        bookListString: JSON.stringify(bookList),
      });
    }
  } else {
    router.push(routerMap.ScanResult);
    // showToast({ title: msg || '未发现此书配套资源' });
  }
};

/**
 * 处理crCode认证
 * @param params
 */
export const crAuth = async (params: {
  crCode: string | null;
  cRCodeResult: CRCodeResult;
  isFromQr?: boolean;
}) => {
  const { crCode, cRCodeResult, isFromQr } = params;
  if (cRCodeResult.errorCode === cRAuthErrorCodeEnum.Success) {
    return true;
  } else if (cRCodeResult.errorCode === cRAuthErrorCodeEnum.NeedBookAuth) {
    // Alert.alert(
    //   '',
    //   '需要绑定学习卡后才能获取配套资源',
    //   [
    //     {
    //       text: '去绑定 ',
    //       onPress: () => {
    //         Navigation.push(componentId, {
    //           component: {
    //             name: 'cn.bookln.MyStudyCardScreen',
    //           },
    //         });
    //       },
    //       style: 'default',
    //     },
    //     {
    //       text: '暂不绑定',
    //       style: 'cancel',
    //       onPress: () => {
    //         if (callback) {
    //           callback();
    //         }
    //       },
    //     },
    //   ],
    //   {
    //     cancelable: false,
    //   }
    // );
    // return false;
  } else if (cRCodeResult.errorCode === cRAuthErrorCodeEnum.ResourceNoExist) {
    // 资源不存在
    showToast({ title: '暂未添加资源或资源已下架' });
    return false;
  } else if (cRCodeResult.errorCode === cRAuthErrorCodeEnum.CrcodeNoExist) {
    showToast({ title: '内容不存在或已经下线' });
    return false;
  } else if (cRCodeResult.errorCode === cRAuthErrorCodeEnum.Fail) {
    showToast({ title: '认证失败,未知错误' });
    return false;
  } else if (cRCodeResult.errorCode === cRAuthErrorCodeEnum.CrcodeSpecificUser) {
    showToast({ title: '需要指定用户才能访问' });
    return false;
  } else if (cRCodeResult.errorCode === cRAuthErrorCodeEnum.UnofficialCode) {
    if (crCode) {
      openWithProps({
        passProps: { url: crCode },
        isFromQr,
      });
    } else {
      showToast({ title: '二维码识别失败' });
    }
    return false;
  } else if (cRCodeResult.errorCode === cRAuthErrorCodeEnum.NeedBuy) {
    showToast({ title: '需要购买后访问！' });
    // todo 暂不处理
    return false;
  } else if (cRCodeResult.errorCode === cRAuthErrorCodeEnum.NeedBuyBook) {
    showToast({ title: '需要购买图书资源后访问！' });
    const { bookId } = cRCodeResult.crcode;
    const response = await container.net().fetch(bookhomedetail({ bookId }));
    const { success, data, msg } = response;
    if (success && data) {
      const { bookDTO } = data;
      router.push(routerMap.WebView, {
        url: `${getRealMpHost()}/book.htm?id=${bookId}&sign=${bookDTO?.idSign}`,
      });
    } else {
      showToast({ title: msg || '获取图书详情失败' });
    }
    return false;
  }
};

/**
 * 根据id获取对应的url
 * @param param
 */
export const fetchUrlOpenWebview = async (param: {
  id: number;
  idSign: string;
  idType: number;
  goWebScreenDirectly?: boolean;
}) => {
  const { id, idSign, idType, goWebScreenDirectly } = param;
  const info = mpResourceUrl({
    id,
    idSign,
    idType,
  });
  const response = await container.net().fetch(info);
  const { success, data } = response;
  if (success && data) {
    const urlOrUrlPath = data.url;
    if (urlOrUrlPath) {
      let passProps: { urlPath?: string; url?: string; goWebScreenDirectly?: boolean } = {
        urlPath: urlOrUrlPath,
        goWebScreenDirectly,
      };
      if (urlOrUrlPath.includes('http://') || urlOrUrlPath.includes('https://')) {
        if (urlOrUrlPath.includes('bookln.cn')) {
          const urlObject = new URL(urlOrUrlPath);
          const urlPath = urlOrUrlPath.replace(urlObject.origin, '');
          passProps = {
            urlPath,
          };
        } else {
          passProps = {
            url: urlOrUrlPath,
          };
        }
      }
      openWithProps({
        passProps,
        isFromQr: true,
      });
    } else {
      showToast({ title: '暂不支持查看' });
    }
  }
};

/**
 * 判断是否是多种资源类型
 * @param newRess 资源列表
 * @returns 是否是多种资源类型
 */
export const isMulitResType = (newRess: Array<ResourceDTO>) => {
  const types = new Set();
  for (let i = 0; i < newRess.length; i++) {
    types.add(newRess[i].type);
    if (types.size > 1) {
      return true;
    }
  }
  return false;
};

const treatCRCodeCaseDefault = async (param: QrInfo) => {
  const { cRCode, isFreeOrIsBuy, isFromQr, callback } = param;
  const { ress, requestParam } = cRCode;
  if (!ress || ress.length === 0) {
    showToast({ title: '暂无资源' });
    return;
  }

  const requestParamObject = JSON.parse(requestParam);

  const { code } = requestParamObject;

  if (isOfficialNormalUrl(code)) {
    let title = '';
    if (ress.length === 1) {
      title = ress?.[0]?.title || '';
    } else {
      title = '多种资源';
    }
    router.push(routerMap.WebView, { url: code, title });
    return;
  } else {
    showToast({ title: '暂无资源' });
  }

  // if (isMulitResType(ress)) {
  //   // 多种资源类型，其中有一些是不支持在资源列表展示的，过滤掉
  //   // const types = QRUtil.listSupportedTypes();
  //   // const ressToDisplayInList = ress.filter(resource =>
  //   //   types.includes(resource.type)
  //   // );
  //   const resTypeCount = ress.length;
  //   const crCodeToDisplayInList = {
  //     ...cRCode,
  //     ress,
  //   };
  //   if (resTypeCount > 1) {
  //     ResUtil.mulitResTypeNav({
  //       componentId,
  //       crCode: crCodeToDisplayInList,
  //       isFreeOrIsBuy,
  //       isFromQr,
  //     });
  //   } else {
  //     ResUtil.singleResTypeNav({
  //       componentId,
  //       entityType: ResourceDownloadInfoEntityType.book,
  //       info: crCodeToDisplayInList,
  //       isFromQr,
  //       isFreeOrIsBuy,
  //       callback,
  //     });
  //   }
  // } else {
  //   ResUtil.singleResTypeNav({
  //     componentId,
  //     entityType: ResourceDownloadInfoEntityType.book,
  //     info: cRCode,
  //     isFromQr,
  //     isFreeOrIsBuy,
  //     callback,
  //   });
  // }
};

/**
 * 处理crCode
 * @param params
 */
export const treateCrCode = async (params: QrInfo) => {
  const { cRCode } = params;
  const crCodeType = cRCode ? cRCode.type : -1;
  switch (crCodeType) {
    case crCodeTypes.virtualBook: {
      // @ts-ignore
      const { url, bookId, bookName, thumbnails } = cRCode;
      if (url) {
        router.push(routerMap.WebView, { url: url });
      } else {
        router.push(routerMap.BookScanResult, {
          bookStr: JSON.stringify({
            id: bookId,
            name: bookName,
            thumbnails: thumbnails,
          }),
        });
      }
      break;
    }
    case crCodeTypes.MISTAKE_BOOK_CHAPTER: {
      // TODO:https://mp-daily.bookln.cn/webappv2/photoModify/index.htm?bizType=95&id=6016&name=%E5%9B%BE%E4%B9%A6%E5%90%8D%E7%A7%B0&pkgId=283&pkgIdSign=a5617c&targetId=42335
      const { pkgIdSign, pkgId, cameraStatus, crName, bookId, description, id, idSign } = cRCode;
      if (pkgId && pkgIdSign) {
        if (cameraStatus === 1) {
          // id是目录的id，pkgId
          const webUrl = `/webappv2/photoModify/index.htm?id=${description}&pkgId=${pkgId}&bizType=${crCodeTypes.MISTAKE_BOOK_CHAPTER}&pkgIdSign=${pkgIdSign}&targetName=${crName}&targetId=${bookId}`;

          //TODO: 涉及到和js通信
          // handleWebMessage: (event: any, webComponentId?: string) => {
          //   if (event.nativeEvent.data === '') {
          //     return;
          //   }
          //   if (webComponentId === undefined) {
          //     return;
          //   }
          //   const message: {
          //     type: string;
          //     data: { bookId: number; bookName?: string; pkgId?: number };
          //   } = JSON.parse(event.nativeEvent.data);

          //   if (message.type === typeNavigationToModifyRecords) {
          //     Navigation.push(webComponentId, {
          //       name: 'cn.bookln.CameraRecordListScreen',
          //       passProps: {
          //         bookId: message?.data?.bookId,
          //       },
          //     });
          //   } else if (message.type === typeNavigationTakePhoto) {
          //     Navigation.push(webComponentId, {
          //       name: 'cn.bookln.CameraCorrectScreen',
          //       passProps: {
          //         bookId: message?.data?.bookId,
          //         bookName: message?.data?.bookName,
          //         pkgId: message?.data?.pkgId,
          //       },
          //     });
          //   } else if (message.type === typeNavigationOpenAlbum) {
          //     Navigation.push(webComponentId, {
          //       name: 'cn.bookln.CameraCorrectScreen',
          //       passProps: {
          //         bookId: message?.data?.bookId,
          //         bookName: message?.data?.bookName,
          //         pkgId: message?.data?.pkgId,
          //       },
          //     });
          //   } else if (message.type === typeNavigationMistakeTakePhoto) {
          //     Navigation.push(webComponentId, {
          //       name: 'cn.bookln.CameraCorrectScreen',
          //       passProps: {
          //         bookId: message?.data?.targetId,
          //         bookName: message?.data?.targetName,
          //         pkgId: message?.data?.pkgId,
          //         recognizeStatus: 1,
          //       },
          //     });
          //   }
          // },
          router.push(routerMap.WebView, { url: `${getRealMpHost()}${webUrl}` });
        } else {
          fetchUrlOpenWebview({ id, idSign, idType: 1 });
        }
      }
      break;
    }
    case crCodeTypes.resourceType:
    case crCodeTypes.catalogType: {
      if (cRCode?.accessStatus === 1) {
        // Navigation.push(componentId, {
        //   component: {
        //     name: 'cn.bookln.BookResScanResultScreen',
        //     passProps: {
        //       ...cRCode,
        //       hasAccessCallBack: () => {
        //         const newCrCode = { ...cRCode, accessStatus: 0 };
        //         const newData = { ...param, crCode: newCrCode };
        //         ResUtil.mulitResTypeNav(newData);
        //       },
        //     },
        //   },
        // });
      } else {
        // QRUtil.treatCRCodeCaseDefault(param);
      }
      break;
    }
    default:
      treatCRCodeCaseDefault(params);
      break;
  }
};

/**
 * 查询crCode
 * @param data
 */
export const qrQuery = async (params: {
  crCode: string | null;
  callback?: () => void;
  source?: 'scanQrCode' | 'other';
  isFromQr?: boolean;
}) => {
  const { crCode, callback, isFromQr, source = 'scanQrCode' } = params;
  if (!crCode || crCode === '') {
    if (callback) {
      setTimeout(callback, 3000);
    }
    return;
  }
  if (crCode.indexOf('prepub') > -1 || crCode.indexOf('_bl_preview_vt') > -1) {
    // 预发环境的二维码
  }
  const res = await container.net().fetch(query({ code: crCode, source, newbook: true }));

  //@ts-ignore
  const { data: cRCodeResult, msg, errorCode } = res;

  if (errorCode) {
    callback && setTimeout(callback, 3000);
    if (stringIsUrl(crCode) && isOfficialQrCodeUrl(crCode) === false) {
      openWithProps({
        passProps: { url: crCode },
        isFromQr: true,
      });
    } else {
      showToast({ title: msg || '解析数据失败' });
    }
    return;
  }

  if (!cRCodeResult) {
    callback && setTimeout(callback, 3000);
  } else {
    const crAuthResult = await crAuth({ crCode, cRCodeResult, isFromQr });
    if (crAuthResult) {
      if (cRCodeResult.crcode) {
        treateCrCode({
          cRCode: cRCodeResult.crcode,
          isFromQr,
          isFreeOrIsBuy: false,
          callback,
        });
      }
    } else {
      switch (cRCodeResult.errorCode) {
        case cRAuthErrorCodeEnum.NeedLogin:
        case cRAuthErrorCodeEnum.NeedBookAuth:
        case cRAuthErrorCodeEnum.UnofficialCode: {
          return;
        }
        case cRAuthErrorCodeEnum.TeacherExclusive: {
          if (cRCodeResult.errorMsg) {
            showToast({ title: cRCodeResult.errorMsg });
          }
          break;
        }
        default: {
          if (cRCodeResult.errorMsg) {
            showToast({ title: cRCodeResult.errorMsg });
          }
          break;
        }
      }
      if (callback) {
        setTimeout(callback, 3000);
      }
    }
  }
};

/**
 * 处理crCode
 * @param crCode
 */
export const treateCrCodeOfUrl = (crCode?: string) => {
  if (!crCode || crCode === '') {
    return;
  }
  navigateToNativeScreenIfPossible({
    url: crCode,
    isFromQr: true,
  });
};
