import { AppConfigCode, getBooklnServiceConfig } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { router } from '@jgl/utils';
import { showToast } from '@yunti-private/jgl-ui';
import { bookhomedetail } from '../api/BookServiceApi';
import type { ResourceDTO } from '../dtos/CRCodeResultDTO';
import { getRealMpHost } from './mpUrlHostHelper';
import { isOfficialQrCodeUrl } from './QRUtil';
import { qrQuery } from './QRUtils';
import { routerMap } from './routerMap';

interface WebScreenProps {
  title?: string;
  ress?: unknown;
  pushJson?: unknown;
  currentIndex?: number | undefined | null;
  url?: string | undefined | null;
  crIdType?: number;
  crId?: number;

  // 学习计划节id
  // 目前学习计划的通关练习是跳网页，要把这个传过去
  // jaybo 2018-08-09
  studyPlanSectionId?: number;

  /**
   * url的路径，不包含host，如：/word/translation.htm
   * 会自动拼接app-p-uid、app-p-sid这些公共参数
   */
  urlPath?: string;

  // 分享按钮
  enableShareButton?: boolean;
  shareRichText?: boolean;
  // enableShareButton为true的时候传，具体参数可参考ShareScreen
  shareData?: Record<string, unknown>;

  // 我的阅读webview 处理与 web 交互的信息
  handleWebMessage?: (e: unknown, componentId?: string) => void;
  onloadEnd?: (webview: unknown, webScreenRef: unknown) => void;
  readingWebViewCanGoback?: (isBack: boolean) => boolean;
  navigationStateChange?: (e: unknown) => void;
  needAutoSetTitle?: boolean;

  // 用于资源浏览统计
  resourceDTO?: ResourceDTO;
  entityId?: number;
  entityType?: number;
  ignoreLogin?: boolean;
  isModal?: boolean;
  willUnmount?: () => void;
  sendInitData?: { data: string; type: string };
  srcChannel?: string;
  /**
   * WebHelper 传入的参数
   */
  goWebScreenDirectly?: boolean;
  originWhitelist?: string[];
  scrollEnabled?: boolean;
  topBarHidden?: boolean;
  ignoreSafeArea?: boolean;
  allowsFullscreenVideo?: boolean;
  hiddenProgress?: boolean;
  supportLandscape?: boolean;
}

interface EPubWebScreenProps {
  bookId?: number;
}

/**
 * 解析自定义查询参数
 * @param url
 * @returns
 */
export const parseCustomQueryItems = (url: string) => {
  const scheme = url.split('://')[0];
  if (scheme === 'http' || scheme === 'https') {
    if (url.indexOf('_appbiz') !== -1) {
      const splitArray = url.split('?');
      if (splitArray.length > 1) {
        const queryItems = splitArray[1].split('&');
        const parameters = {};
        queryItems.forEach((itemString: string, index: number) => {
          const keyValue = itemString.split('=');
          parameters[keyValue[0]] = keyValue[1];
        });
        return parameters;
      }
    }
  }
  return null;
};

/**
 * 尝试解析 url
 */
export const tryDecodeURIComponent = (url: string) => {
  try {
    // 【【闪退】iOS RCTFatalException: Unhandled JS Exception: URIError: URI error】 https://www.tapd.cn/21394591/prong/stories/view/1121394591001098404
    // FIX: https://bugly.qq.com/v2/crash-reporting/crashes/b7588917d3/56044?pid=2
    const decodeUrl = decodeURIComponent(url);
    return decodeUrl;
  } catch (error) {
    return null;
  }
};

const handleQrCodeUrl = (data: {
  url: string;
  srcChannel?: string;
  callback?: (pushed: boolean) => void;
  isFromQr?: boolean;
}): boolean => {
  const { url, callback, isFromQr } = data;
  const isQrCodeUrl = isOfficialQrCodeUrl(url);
  if (isQrCodeUrl) {
    qrQuery({ crCode: url, source: 'other', isFromQr });

    if (callback) {
      callback(isQrCodeUrl);
    }
  }
  return isQrCodeUrl;
};

/**
 * 跳转web图书
 */
export const navigateToWebBook = async (params: {
  bookId: string;
  idSign?: string;
}) => {
  const { bookId, idSign: sign } = params;
  if (sign) {
    router.push(routerMap.WebView, {
      url: `${getRealMpHost()}/book.htm?id=${bookId}&sign=${sign}`,
    });
  } else {
    const response = await container.net().fetch(bookhomedetail({ bookId }));
    const { success, data, msg } = response;
    if (success && data) {
      const { id, idSign } = data.bookDTO ?? {};
      router.push(routerMap.WebView, {
        url: `${getRealMpHost()}/book.htm?id=${id}&sign=${idSign}`,
      });
    } else {
      showToast({ title: msg ?? '获取数据失败' });
    }
  }
};

/**
 * 跳转web课程
 */
export const navigateToWebLesson = (lessonId: number, idSign: string) => {
  router.push(routerMap.WebView, {
    url: `${getRealMpHost()}/lesson.htm?lessonId=${lessonId}&idSign=${idSign}`,
  });
};

export const navigateToWebMyPractice = () => {
  router.push(routerMap.WebView, {
    url: `${getRealMpHost()}/personal.htm#/myExercise`,
    title: '我的练习',
  });
};

/**
 * 跳转web我的收藏
 */
export const navigateToWebMyCollection = () => {
  router.push(routerMap.WebView, {
    url: `${getRealMpHost()}/personal.htm#/myCollection`,
    title: '我的收藏',
  });
};

/**
 * 跳转web我的课程
 */
export const navigateToWebMyCourse = () => {
  router.push(routerMap.WebView, {
    url: `${getRealMpHost()}/personal.htm#/myCourse`,
  });
};

/**
 * 跳转web点击阅读
 */
export const navigateToWebClickRead = (bookId: number) => {
  router.push(routerMap.WebView, {
    url: `${getRealMpHost()}/clickread.htm?id=${bookId}`,
  });
};

/**
 * 处理其他支持的url
 */
export const handleOtherSupportedUrl = (
  url: string,
  callback?: (pushed: boolean) => void,
): boolean => {
  const parameters = parseCustomQueryItems(url);
  let canGoToNativeScreen = false;
  if (!parameters) {
    canGoToNativeScreen = false;
  }

  console.log('handleOtherSupportedUrl', JSON.stringify(parameters));

  const type = parameters._appbiz;
  switch (type) {
    case 'bookdetail': {
      canGoToNativeScreen = true;
      navigateToWebBook({
        bookId: parameters.bookid,
      });
      break;
    }
    case 'buy': {
      // canGoToNativeScreen = true;
      // TODO: 待处理
      break;
    }
    case 'mycoupon': {
      //TODO: 待处理
      break;
    }
    case 'lesson': {
      canGoToNativeScreen = true;
      navigateToWebLesson(parameters.lessonid);
      // 跳转网页
      // const lessonId = parseInt(parameters.lessonid);
      // if (lessonId) {
      //   screen = 'cn.bookln.LessonHomeScreen';
      //   passProps = { lessonId };
      // }
      break;
    }
    case 'clickread': {
      canGoToNativeScreen = true;
      navigateToWebClickRead(parameters.bookid);
      // 跳转网页
      // const bookId = parseInt(parameters.bookid);
      // if (bookId) {
      //   screen = 'cn.bookln.ClickReadScreen';
      //   passProps = {
      //     bookId,
      //   };
      // }
      break;
    }
    case 'nativepage': {
      // const { path } = parameters;
      // const props = parameters.props ? JSON.parse(parameters.props) : null;
      // if (path) {
      //   screen = path;
      //   if (props) {
      //     passProps = props;
      //   }
      // }
      break;
    }
    case 'exchangecouoon':
      // screen = 'cn.bookln.ExchangeCouponScreen';
      break;
    case 'vipcarddetail': {
      // http://13.s.bookln.cn?_appbiz=vipcarddetail&batchId=251&srcchannel=recommend
      // screen = 'cn.bookln.VipCardDetailScreen';
      // passProps = { id: parseInt(parameters.batchId) };
      break;
    }
  }
  if (callback) {
    callback(canGoToNativeScreen);
  }

  return canGoToNativeScreen;
};

export const navigateToNativeScreenIfPossible = (data: {
  url: string;
  srcChannel?: string;
  callback?: (pushed: boolean) => void;
  ignoreLogin?: boolean;
  isFromQr?: boolean;
  goWebScreenDirectly?: boolean;
}): boolean => {
  const { url, srcChannel, callback, ignoreLogin, isFromQr, goWebScreenDirectly } = data;

  // 当需要跳转原生支付的时候走老逻辑，不直接跳转
  if (goWebScreenDirectly && !url.includes('_appbiz=buy')) {
    callback?.(false);
    return false;
  }

  const decodeUrl: string | null = tryDecodeURIComponent(url);

  if (decodeUrl === null) {
    return false;
  }

  // 1.处理二维码链接
  const isQrCodeUrlAndHandled = handleQrCodeUrl({
    url: decodeUrl,
    srcChannel,
    callback,
    isFromQr,
  });
  if (isQrCodeUrlAndHandled) {
    return true;
  }

  // 2.处理各种指定页面的链接
  const isSupportedUrlAndHandled = handleOtherSupportedUrl(decodeUrl, callback);
  return isSupportedUrlAndHandled;
};

const getQueryString = (url: string | undefined | null, queryName: string) => {
  if (!url) {
    return null;
  }
  const questionMarkIndex = url.indexOf('?');
  if (questionMarkIndex !== -1) {
    const reg = new RegExp(`(^|&)${queryName}=([^&]*)(&|$)`);
    const r = url.substr(questionMarkIndex + 1).match(reg);
    if (r != null) return decodeURI(r[2]);
  }
  return null;
};

/**
 * 跳转web页面
 */
const pushToWebScreen = async (
  props: { passProps: WebScreenProps },
  url?: string | null,
  readingWebview = false,
  epubWebView = false,
) => {
  const shareKey = getQueryString(url, 'shareKey');
  const goToWebScreen = (newProps: { passProps: WebScreenProps }) => {
    const goto = () => {
      router.push(routerMap.WebView, { url: newProps.passProps.url });
      // let screen = 'cn.bookln.WebScreen';
      // if (readingWebview) {
      //   screen = 'cn.bookln.ReadingWebScreen';
      // }
      // if (epubWebView) {
      //   screen = 'cn.bookln.EPubWebScreen';
      // }
      // Navigation.push(componentId, {
      //   name: screen,
      //   passProps: {
      //     ...newProps.passProps,
      //     ...(enableShareButton === null
      //       ? {}
      //       : {
      //           enableShareButton: enableShareButton !== 'false',
      //         }),
      //   },
      //   options: {
      //     topBar: {
      //       visible: !props.passProps.topBarHidden,
      //     },
      //   },
      // });
    };
    goto();
  };
  // url中有设置分享key，则拉取此key对应的分享信息
  if (shareKey) {
    const response = await container
      .net()
      .fetch(getBooklnServiceConfig(AppConfigCode.BooklnShareUrlConfig));
    const { success, data } = response;
    // const info = CommonFI.queryByKey('bookln_share_url_config');
    // YTApi.fetch(info, (ytResponse) => {
    if (success && data?.result) {
      const config = JSON.parse(data?.result);
      if (config[shareKey]) {
        const newPassProps = {
          ...props.passProps,
          shareData: config[shareKey],
        };
        props.passProps = newPassProps;
      }
      goToWebScreen(props);
    } else {
      goToWebScreen(props);
    }
    // });
  } else {
    goToWebScreen(props);
  }
};

export const openWithProps = (
  props: {
    passProps: WebScreenProps & EPubWebScreenProps;
    isFromQr?: boolean;
  },
  readingWebview = false,
  epubWebView = false,
) => {
  const { url, ress, currentIndex, pushJson, srcChannel } = props.passProps;
  let actualUrl: string | null = null;
  if (url) {
    actualUrl = url;
  } else if (ress != null) {
    const index = currentIndex || 0;
    const resource = ress[index];
    actualUrl =
      resource.content && resource.content.startsWith('http') ? resource.content : undefined;
  } else if (pushJson) {
    actualUrl = pushJson.msg;
  }

  if (actualUrl) {
    /*
            普通用户，跳转原生：不加载网页
            普通用户，无法跳转原生：继续加载网页
            游客，不让跳转原生
            游客，不让加载网页
            iOS游客，可以跳转原生
            iOS游客，可以加载网页
            */
    navigateToNativeScreenIfPossible({
      url: actualUrl,
      srcChannel,
      callback: (isPushed: boolean) => {
        if (!isPushed) {
          pushToWebScreen(props, actualUrl, readingWebview, epubWebView);
        }
      },
      isFromQr: props.isFromQr,
      goWebScreenDirectly: props.passProps.goWebScreenDirectly,
    });
  } else {
    pushToWebScreen(props, actualUrl, readingWebview);
  }
};
