import { generateUUID, shuffleArray } from '@jgl/utils';
import type { WordItem } from '../api/dto';

/** 显示文本字段 */
const eliminateItemFields = ['name', 'content'];

export type SplitItemDTO = {
  pid: string;
  text: string;
  matchText?: string;
  id: string;
  disappear: boolean;
  success?: boolean;
  error?: boolean;
  bgColor?: string;
  code?: string;
};

export const splitEliminateItem = (items: WordItem[]): SplitItemDTO[] => {
  const wordsItems: SplitItemDTO[] = [];

  items.forEach((item) => {
    const pid = generateUUID();
    const itemEntries = Object.entries(item);
    for (let i = 0; i < itemEntries.length; i++) {
      const [key, value = ''] = itemEntries[i] ?? '';
      const isTopic = key === eliminateItemFields[0];

      if (
        key &&
        typeof key === 'string' &&
        eliminateItemFields.includes(key) &&
        typeof value === 'string'
      ) {
        const wordItem = {
          pid,
          text: value || '',
          matchText: isTopic ? item.content : item.name,
          id: generateUUID(),
          disappear: false,
          bgColor: isTopic ? '#D4ECFF' : '#F5E5FF',
          //   code: item.code,
        };
        wordsItems.push(wordItem);
      }
    }
  });

  return shuffleArray(wordsItems);
};
