import { storage } from '@jgl/utils';
import { native } from './Native';
import { Platform } from 'react-native';

/**
 * 渠道处理。
 *
 */
export class ChannelHelper {
  private static instance?: ChannelHelper;

  private constructor() {
    //
  }

  static shared(): ChannelHelper {
    if (this.instance == null) {
      this.instance = new ChannelHelper();
    }
    return this.instance;
  }

  /**
   * 进入app保存渠道
   */
  private saveFirstChannelIfNeeded = async (channel: string): Promise<void> => {
    const firstChannel = await this.getFirstChannel();
    if (firstChannel) {
      // 已经有channel，直接使用
    } else {
      if (channel && channel.length > 0) {
        try {
          await storage.setItem(this.channelKey(), channel);
        } catch (error) {
          //
        }
      } else {
        // 把默认channel存到本地
        // 以后更新的都不算
        try {
          await storage.setItem(this.channelKey(), this.defaultChannel());
        } catch (error) {
          //
        }
      }
    }
  };

  getChannel = async (): Promise<string> => {
    const firstChannel = await this.getFirstChannel();
    if (firstChannel) {
      // channel固定之后不会变
      return firstChannel;
    } else {
      const channel = await native.getCurrentAndroidChannel();
      if (channel) {
        this.saveFirstChannelIfNeeded(channel);
        return channel;
      }
      return this.defaultChannel();
    }
  };

  private defaultChannel = (): string => {
    return Platform.OS;
  };

  private getFirstChannel = async (): Promise<string | undefined> => {
    try {
      const channel = await storage.getItem(this.channelKey());
      if (channel) {
        return channel;
      }
    } catch (error) {
      //
    }

    return undefined;
  };

  private channelKey = (): string => {
    return '@firstOpenInstallChannel';
  };
}
