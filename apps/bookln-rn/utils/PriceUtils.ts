import { envVars } from '@jgl/utils';
import { Platform } from 'react-native';

export const RMBSymbol = '￥';

export const currencyName = (): string => {
  // 为了防止被拒绝，需要把人民币替换成虚拟货币单位
  return envVars.virtualCurrency() ?? '学币';
};

export const readablePriceInfo = (info: {
  price: number | string | null | undefined;
  numberOfDecimals?: number;
  useRMBOnIOS?: boolean;
}): {
  prefix: string;
  priceString: string;
  suffix: string;
} => {
  let { price } = info;
  if (typeof price !== 'number') {
    if (price == null) {
      price = 0;
    } else {
      price = Number(price);
    }
  }

  const numberOfDecimals = info.numberOfDecimals != null ? info.numberOfDecimals : 2;
  const useRMBOnIOS = info.useRMBOnIOS || false;
  const priceString = (price / 100).toFixed(numberOfDecimals);

  if (Platform.OS === 'ios') {
    if (useRMBOnIOS) {
      return {
        prefix: RMBSymbol,
        priceString,
        suffix: '',
      };
    } else {
      return {
        prefix: '',
        priceString,
        suffix: currencyName(),
      };
    }
  } else {
    return {
      prefix: RMBSymbol,
      priceString,
      suffix: '',
    };
  }
};

/**
 * 可读的价格，iOS格式举例：599.00学币，Android格式举例：￥599.00
 * @param {价格的数字，单位是分} price
 * @param {保留几位小数} num
 */
export const readablePrice = (info: {
  price: number | string | null | undefined;
  numberOfDecimals?: number;
  useRMBOnIOS?: boolean;
}): string => {
  const priceInfo = readablePriceInfo(info);
  const { prefix, priceString, suffix } = priceInfo;
  return `${prefix}${priceString}${suffix}`;
};
