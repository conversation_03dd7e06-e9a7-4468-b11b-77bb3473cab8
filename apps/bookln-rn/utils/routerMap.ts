enum RouterPathCode {
  /**
   * 首页
   */
  Home = 'Home',
  /**
   * 绑定手机号
   */
  BindPhone = 'BindPhone',
  /**
   * 绑定手机号modal
   */
  BindPhoneModal = 'BindPhoneModal',
  /**
   * 书架
   */
  BookShelf = 'BookShelf',
  /**
   * 听力练习
   */
  ListeningPractice = 'ListeningPractice',
  /**
   * AI解释
   */
  AiExplain = 'AiExplain',
  /**
   * 扫码搜索
   */
  ScanSearch = 'ScanSearch',
  /**
   * 网页
   */
  WebView = 'WebView',
  /**
   * 反馈
   */
  Feedback = 'Feedback',
  /**
   * 常见问题
   */
  CommonProblem = 'CommonProblem',
  /**
   * 设置
   */
  Setting = 'Setting',
  /**
   * 个人信息
   */
  PersonalInfo = 'PersonalInfo',
  /**
   * 账号与安全
   */
  AccountAndSafety = 'AccountAndSafety',
  /**
   * 关于Bookln
   */
  AboutBookln = 'AboutBookln',
  /**
   * 编辑昵称
   */
  EditNick = 'EditNick',
  /**
   * 删除账号
   */
  DeleteAccount = 'DeleteAccount',
  /**
   * 扫码选书列表
   */
  ScanChooseBookList = 'ScanChooseBookList',
  /**
   * 扫码选书结果
   */
  BookScanResult = 'BookScanResult',
  /**
   * 推荐管理
   */
  RecommendManager = 'RecommendManager',
  /**
   * 口算练习首页
   */
  OralArithmetic = 'OralArithmetic',
  /**
   * 口算练习-开始练习
   */
  OralArithmeticExercise = 'OralArithmeticExercise',
  /**
   * 口算练习-结果
   */
  OralArithmeticResult = 'OralArithmeticResult',
  /**
   * 口算PK练习
   */
  OralArithmeticPKExercise = 'OralArithmeticPKExercise',
  /**
   * 口算PK结果
   */
  OralArithmeticPKResult = 'OralArithmeticPKResult',
  /**
   * 客服中心
   */
  CustomerServiceCenter = 'CustomerServiceCenter',
  /**
   * AI学习书籍详情
   */
  AiBookDetail = 'AiBookDetail',
  /**
   * 投诉举报
   */
  ComplaintReport = 'ComplaintReport',
  /**
   * 身份认证
   */
  AuthIdentity = 'AuthIdentity',

  /**
   * AI学习拍照批改
   */
  AiBookTakePhoto = 'AiBookTakePhoto',
  /**
   * AI学习拍照批改结果页
   */
  AiBookTakePhotoResult = 'AiBookTakePhotoResult',

  /**
   * AI学习本书错题
   */
  BookWrongExercise = 'BookWrongExercise',

  /**
   * 单词消消乐
   */
  WordEliminate = 'WordEliminate',

  /**
   * 本书收藏
   */
  BookCollection = 'BookCollection',

  /**
   * 本书题集
   */
  BookTopic = 'BookTopic',

  /**
   * 我的错题
   */
  MyWrongExercise = 'MyWrongExercise',

  /**
   * 我的收藏
   */
  MyCollection = 'MyCollection',

  /**
   * 扫码选书结果
   */
  ScanResult = 'ScanResult',

  /**
   * 知识点详情
   */
  AiBookKnowledgePointDetail = 'AiBookKnowledgePointDetail',

  /**
   * 我的课程
   */
  MyLesson = 'MyLesson',

  /**
   * 登录
   */
  Login = 'Login',

  /**
   * 登录-手机号登录
   */
  LoginByPhone = 'LoginByPhone',
}

export const routerMap: Record<RouterPathCode, string> = {
  [RouterPathCode.Home]: '/(tabs)/home',
  [RouterPathCode.BookShelf]: '/bookShelf',
  [RouterPathCode.ListeningPractice]: '/listeningPractice',
  [RouterPathCode.AiExplain]: '/aiExplain',
  [RouterPathCode.ScanSearch]: '/scanSearch',
  [RouterPathCode.WebView]: '/webView',
  [RouterPathCode.Feedback]: '/feedback',
  [RouterPathCode.CommonProblem]: '/commonProblem',
  [RouterPathCode.Setting]: '/setting',
  [RouterPathCode.PersonalInfo]: '/personalInfo',
  [RouterPathCode.AccountAndSafety]: '/accountAndSafety',
  [RouterPathCode.AboutBookln]: '/aboutBookln',
  [RouterPathCode.EditNick]: '/editNick',
  [RouterPathCode.DeleteAccount]: '/deleteAccount',
  [RouterPathCode.ScanChooseBookList]: '/scanChooseBookList',
  [RouterPathCode.BookScanResult]: '/bookScanResult',
  [RouterPathCode.RecommendManager]: '/recommendManager',
  [RouterPathCode.OralArithmetic]: '/oralArithmetic',
  [RouterPathCode.OralArithmeticExercise]: '/oralArithmeticExercise',
  [RouterPathCode.ComplaintReport]: '/complaintReport',
  [RouterPathCode.AuthIdentity]: '/authIdentity',
  [RouterPathCode.OralArithmeticResult]: '/oralArithmeticResult',
  [RouterPathCode.OralArithmeticPKExercise]: '/oralArithmeticPKExercise',
  [RouterPathCode.OralArithmeticPKResult]: '/oralArithmeticPKResult',
  [RouterPathCode.CustomerServiceCenter]: '/customerServiceCenter',
  [RouterPathCode.AiBookDetail]: '/aiBook/aiBookDetail',
  [RouterPathCode.AiBookTakePhoto]: '/aiBook/aiBookTakePhoto',
  [RouterPathCode.AiBookTakePhotoResult]: '/aiBook/aiBookTakePhotoResult',
  [RouterPathCode.BookWrongExercise]: '/aiBook/bookWrongExercise',
  [RouterPathCode.WordEliminate]: '/aiBook/wordEliminate',
  [RouterPathCode.BookCollection]: '/aiBook/bookCollection',
  [RouterPathCode.BookTopic]: '/aiBook/bookTopic',
  [RouterPathCode.MyWrongExercise]: '/aiBook/myWrongExercise',
  [RouterPathCode.MyCollection]: '/aiBook/myCollection',
  [RouterPathCode.ScanResult]: '/scanResult',
  [RouterPathCode.AiBookKnowledgePointDetail]: '/aiBook/aiBookKnowledgePointDetail',
  [RouterPathCode.MyLesson]: '/myLesson',
  [RouterPathCode.Login]: '/login',
  [RouterPathCode.BindPhone]: '/bindPhone',
  [RouterPathCode.BindPhoneModal]: '/(bindPhoneModal)/bindPhoneModal',
  [RouterPathCode.LoginByPhone]: '/loginByPhone',
};
