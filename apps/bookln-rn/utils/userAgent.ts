import { envVars, getAppVid } from '@jgl/utils';
import { Platform } from 'react-native';
import UserAgent from 'react-native-user-agent';

enum AppType {
  iOS = '1',
  Android = '2',
}
export const getCustomUserAgent = () => {
  const appType = Platform.OS === 'ios' ? AppType.iOS : AppType.Android;
  const originUA = UserAgent.getUserAgent();
  const realAgent = `${originUA} YuntiApp-${envVars
    .appId()
    .toString()}-${appType}-${getAppVid()}`;
  return realAgent;
};
