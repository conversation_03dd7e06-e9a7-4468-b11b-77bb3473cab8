/* eslint-disable */
import ReactNativeHapticFeedback, {
  HapticFeedbackTypes,
} from 'react-native-haptic-feedback';

const options = {
  enableVibrateFallback: false,
  ignoreAndroidSystemSettings: false,
};

/**
 * 
 * @param feedback  
 *'selection',
  'impactLight',
  'impactMedium',
  'impactHeavy',
  'rigid',
  'soft',
  'notificationSuccess',
  'notificationWarning',
  'notificationError',
 */

export const hapticFeedback = (feedback: HapticFeedbackTypes) => {
  ReactNativeHapticFeedback.trigger(feedback, options);
};
