import { router } from '@jgl/utils';
import { router as expoRouter } from 'expo-router';
import { routerMap } from './routerMap';

/**
 * 替换到首页
 */
export const replaceToHome = () => {
  // // 重置首页tab状态栏样式
  // store.set(atomMap.homeBooklnTabStatusBarStyleAtom, 'dark');
  // // 重置首页tab状态栏样式
  // store.set(atomMap.homeMineTabStatusBarStyleAtom, 'dark');
  // 替换到首页
  expoRouter.replace('/(tabs)/home');
};

/**
 * 替换到登录页面
 */
export const replaceToLoginPage = () => {
  router.replace(routerMap.Login);
};
