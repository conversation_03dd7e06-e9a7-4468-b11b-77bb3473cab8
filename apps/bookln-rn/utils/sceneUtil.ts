import { EnumActionType, OpenPageActionParams } from '@jgl/biz-func';
import { envVars, router } from '@jgl/utils';
import { LearnPkgApiQueryBannerListByTypeDTO } from '@yunti-private/api-xingdeng-boot';
import { launchMiniProgram, NativeWechatConstants } from 'native-wechat';
import { HandleNavActionParams } from '../types';
import { routerMap } from './routerMap';

export const handleNavAction = (params: HandleNavActionParams) => {
  const { sceneCode, search } = params;
  const routePath = routerMap[sceneCode];
  if (sceneCode && routePath) {
    router.push(routePath, { ...search, sceneCode });
  }
};

/**
 * 跳转到业务页面
 * @param param {path，params}
 */
export const navigateToPage = (param: {
  path: string;
  params?: Record<string, any>;
}) => {
  const { path, params } = param;
  console.log('path', path);
  router.push(path, params);
};

export type ClickBannerParams = {
  banner?: LearnPkgApiQueryBannerListByTypeDTO[number];
  /**
   * 跳转参数
   */
  navigateParams?: Record<string, string | number>;
};
export const clickBanner = (params: ClickBannerParams) => {
  //TODO: 由于需要兼容小程序配置中拍照答疑配置的actionType为2，APP这边单独处理sceneCode为PhotoQuestionAnswer的跳转逻辑
  const actionParams = JSON.parse(params?.banner?.actionParams || '{}');
  if (actionParams?.sceneCode === 'PhotoQuestionAnswer') {
    navigateToPage({
      path: 'photoQuestionAnswer',
      params: params?.navigateParams,
    });
    return;
  }
  switch (Number(params?.banner?.actionType)) {
    case EnumActionType.ACTION_TYPE_SCENE: {
      handleToScene(params);
      break;
    }
    // case EnumActionType.ACTION_VIDEO: {
    //   handleToVideoChannel(params?.banner);
    //   break;
    // }
    // case EnumActionType.ACTION_PHOTO: {
    //   handlePreviewImage(params?.banner);
    //   break;
    // }
    // case EnumActionType.ACTION_TYPE_SMALL_APP: {
    //   handleOpenSmallApp(params?.banner);
    //   break;
    // }
    // case EnumActionType.ACTION_OPEN_EMBEDDED_APP: {
    //   handleOpenEmbeddedApp(params?.banner);
    //   break;
    // }
    // case EnumActionType.ACTION_TYPE_SMALL_APP_FLOAT_LAYER: {
    //   // TODO: 🍉🍉🍉 - 后续实现
    //   break;
    // }
    case EnumActionType.ACTION_OPEN_PAGE: {
      handleOpenPage(params);
      break;
    }
    case EnumActionType.ACTION_OPEN_WEBVIEW: {
      handleOpenWebview(params?.banner);
      break;
    }
    case EnumActionType.ACTION_SWITCH_TAB: {
      handleOpenSwitchTab(params?.banner);
      break;
    }
    case EnumActionType.ACTION_TYPE_SMALL_APP: {
      const { path, originalId, page } = JSON.parse(
        params?.banner?.actionContent || '{}',
      );
      const realPath = page || path;
      if (originalId && realPath) {
        launchMiniProgram({
          userName: originalId,
          miniProgramType: NativeWechatConstants.WXMiniProgramTypeRelease,
          path: realPath,
        });
      }
      break;
    }
    // case EnumActionType.ACTION_SHORT_LINK: {
    //   handleOpenSmallAppByShortLink(params?.banner);
    //   break;
    // }
    // default: {
    //   Taro.showToast({
    //     title: '暂不支持打开，请点击右上角重启小程序后重试',
    //   });
    //   break;
    // }
  }
};
const handleToScene = async (params: ClickBannerParams) => {
  const actionParams = JSON.parse(params?.banner?.actionParams || '{}');

  if (actionParams?.sceneCode) {
    handleNavAction({
      sceneCode: actionParams?.sceneCode,
      search: {
        ...params?.navigateParams,
        ...actionParams,
      },
    });
  }
};

/**
 * 跳转原生页面
 * @param {ClickBannerParams} params
 */
const handleOpenPage = (params: ClickBannerParams) => {
  const actionContent = JSON.parse(params?.banner?.actionContent || '{}');

  navigateToPage({
    path: actionContent?.path?.substring(actionContent?.path?.lastIndexOf('/')),
    params: params?.navigateParams,
  });
};

/**
 * 跳转webview
 * @param {LearnPkgApiQueryBannerListByTypeDTO[number] | undefined} banner
 */
const handleOpenWebview = (
  banner?: LearnPkgApiQueryBannerListByTypeDTO[number] | undefined,
) => {
  const actionContent = JSON.parse(banner?.actionContent || '{}');
  navigateToPage({
    path: routerMap.webView as string,
    params: {
      url: actionContent?.path,
    },
  });
};

/**
 * 切换到tab页
 * @param {LearnPkgApiQueryBannerListByTypeDTO[number] | undefined} banner
 */
const handleOpenSwitchTab = (
  banner?: LearnPkgApiQueryBannerListByTypeDTO[number] | undefined,
) => {
  const actionParams = JSON.parse(banner?.actionContent || '{}');

  if (actionParams) {
    const { path } = actionParams as OpenPageActionParams;
    navigateToPage({
      path: path || '',
    });
  }
};

export const navigateToOrderVIPPage = (param?: {
  params?: Record<string, any>;
}) => {
  if (routerMap.orderVip) {
    const { params } = param || {};
    router.push(routerMap.orderVip, params);
  }
};
