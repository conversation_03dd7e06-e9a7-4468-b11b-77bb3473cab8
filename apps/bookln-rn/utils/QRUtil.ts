// @ts-ignore
import URL from 'url-parse';

/**
 * 判断字符串是否是URL
 */
export const stringIsUrl = (str?: string | null) => {
  if (!str || str === '') {
    return false;
  }
  return str != null && (str.startsWith('http://') || str.startsWith('https://'));
};

/**
 * 判断字符串是否是官方URL
 */
export const isOfficialNormalUrl = (url: string): boolean => {
  // eg: 'http://qr.bookln.cn/share/audio.htm?bid=123&rid=234...

  if (url) {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      const officialHosts = [
        'bookln.cn',
        'yeteam.com',
        'zhizhuma.com',
        'koudaitiku.com',
        'lifangshu.com',
        'bk6.pub',
        'qq.com',
      ];

      const urlObject = new URL(url);
      const { host } = urlObject;
      const isOfficialUrl =
        officialHosts.find((officialHost) => host.includes(officialHost)) != null;
      return isOfficialUrl;
    }
  }

  return false;
};

/**
 * 判断字符串是否是官方二维码URL
 */
export const isOfficialQrCodeUrl = (url: string): boolean => {
  // eg: 'http://qr.bookln.cn/qr.html?crcode=110000010F00000006000005EGTM1DEF'

  const isOfficialUrl = isOfficialNormalUrl(url);
  if (isOfficialUrl) {
    const urlObject = new URL(url);
    const { query } = urlObject;
    const isQrCodeValid =
      // @ts-ignore
      query.includes('?crcode=') ||
      // @ts-ignore
      query.includes('?c=') ||
      // @ts-ignore
      query.includes('&crcode=') ||
      // @ts-ignore
      query.includes('&c=');
    return isQrCodeValid;
  }

  return false;
};
