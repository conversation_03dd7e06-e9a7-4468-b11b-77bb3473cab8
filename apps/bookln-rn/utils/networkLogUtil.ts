import { isNetworkLoggingEnabledAtom, storageKeys } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { storage } from '@jgl/utils';
import { useAtom } from 'jotai';
import { useCallback, useEffect } from 'react';
import { startNetworkLogging as startLogging } from 'react-native-network-logger';

/**
 * 开启react-native网络请求log功能。
 *
 * 在调试选项页面可查看所有网络请求。
 */
export const useStartNetworkLogIfNeeded = () => {
  const { startNetworkLogging } = useStartNetworkLogging();
  useEffect(() => {
    if (__DEV__) {
      startNetworkLogging();
    } else {
      storage
        .getItem(storageKeys.enableNetworkLogOnLaunch, { env: false })
        .then((value) => {
          if (value === 'true') {
            startNetworkLogging();
          }
        });
    }
  }, [startNetworkLogging]);
};

export const useStartNetworkLogging = () => {
  const [isNetworkLoggingEnabled, setIsNetworkLoggingEnabled] = useAtom(
    isNetworkLoggingEnabledAtom,
  );

  const startNetworkLogging = useCallback(() => {
    if (isNetworkLoggingEnabled) {
      return;
    }

    setIsNetworkLoggingEnabled(true);
    startLogging({
      ignoredPatterns: [
        // POST http://192.169.1.19:8081/logs
        /http:\/\/192\./,
      ],
    });
  }, [isNetworkLoggingEnabled, setIsNetworkLoggingEnabled]);

  return { startNetworkLogging };
};

/**
 * 开发者菜单中的「Open JS debugger」无法查看网络请求，所以用log的形式来输出。
 */
export const useNetworkConsoleLog = () => {
  useEffect(() => {
    let listenerId: string | undefined;

    if (__DEV__) {
      listenerId = container.net().addListener({
        onRequest: (request) => {
          console.log(
            new Date().toLocaleTimeString(),
            `⬆️ 网络请求发起`,
            request.request.url,
          );
          console.log(request);
        },
        onResponse: (response) => {
          console.log(
            new Date().toLocaleTimeString(),
            '⬇️⬇️ 网络请求结束',
            response.request.url,
          );
          console.log(response);
        },
      });
    }

    return () => {
      if (listenerId) {
        container.net().removeListener(listenerId);
      }
    };
  }, []);
};
