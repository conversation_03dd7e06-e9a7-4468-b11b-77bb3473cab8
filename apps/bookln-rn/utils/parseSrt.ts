export interface Subtitle {
  startTime: number;
  endTime: number;
  text: string;
}

export const parseSrt = (srtContent: string): Subtitle[] => {
  const lines = srtContent.split('\n');
  const subtitles: Subtitle[] = [];
  let currentSubtitle: Partial<Subtitle> | null = null;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]?.trim() ?? '';

    if (line === '') {
      if (
        currentSubtitle &&
        currentSubtitle.startTime !== undefined &&
        currentSubtitle.endTime !== undefined &&
        currentSubtitle.text !== undefined
      ) {
        subtitles.push(currentSubtitle as Subtitle);
        currentSubtitle = null;
      }
      continue;
    }

    // 匹配序列号
    if (!Number.isNaN(Number.parseInt(line, 10)) && !currentSubtitle) {
      currentSubtitle = {};
      continue;
    }

    // 匹配时间戳
    const timeMatch = line.match(/(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})/);
    if (timeMatch && currentSubtitle && timeMatch[1] && timeMatch[2]) {
      currentSubtitle.startTime = srtTimeToMs(timeMatch[1]);
      currentSubtitle.endTime = srtTimeToMs(timeMatch[2]);
      continue;
    }

    // 匹配歌词文本
    if (
      currentSubtitle &&
      currentSubtitle.startTime !== undefined &&
      currentSubtitle.text === undefined
    ) {
      currentSubtitle.text = line;
    } else if (currentSubtitle && currentSubtitle.text !== undefined) {
      // 处理多行歌词
      currentSubtitle.text = `${currentSubtitle.text}\n${line}`;
    }
  }

  // 确保最后一个字幕也被添加
  if (
    currentSubtitle &&
    currentSubtitle.startTime !== undefined &&
    currentSubtitle.endTime !== undefined &&
    currentSubtitle.text !== undefined
  ) {
    subtitles.push(currentSubtitle as Subtitle);
  }

  return subtitles;
};

// 辅助函数：将 SRT 时间格式转换为毫秒
export const srtTimeToMs = (timeStr: string): number => {
  const parts = timeStr.split(':');
  if (parts.length !== 3) {
    throw new Error('Invalid time format');
  }

  const [h, m, sMs] = parts as [string, string, string];
  const [s, ms] = sMs.split(',');

  if (!h || !m || !s || !ms) {
    throw new Error('Invalid time format');
  }

  return (
    Number.parseInt(h, 10) * 3600000 +
    Number.parseInt(m, 10) * 60000 +
    Number.parseInt(s, 10) * 1000 +
    Number.parseInt(ms, 10)
  );
};
