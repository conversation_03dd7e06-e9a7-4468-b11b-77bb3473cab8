export type MiniAppVersion = {
  data?: AppVersionDTO;
  markets?: MarketDTO[];
};

export type AppVersionDTO = {
  // android
  id?: number;
  // 版本号
  vid?: number;
  // 版本CODE
  name?: string;
  // 更新内容
  content?: string;
  // 2=android=android;1=ios=ios
  platform?: number;
  // 下载地址
  url?: string;
  // apkhash值
  md5?: string;
  // 1=comm=不强制不卸载更新;2=unforce_uninstall=不强制要卸载更新;3=force=强制不卸载更新;4=force_uninstall=强制要卸载更新
  strategy?: number;
  // hash值
  hash?: string;
  // 渠道名
  channel?: string;
  // 0=all=全员升级;1=user=指定用户升级;2=usertype=根据用户类型升级
  scopeType?: number;
  // 升级范围:据根不同的scopetype设置不同的值.
  scope?: string;
  // app.code
  appCode?: string;
};

export type MarketDTO = {
  /**
   * 手机品牌
   */
  name?: string;
  /**
   * 是否需要更新
   */
  upgrade?: boolean;
};
