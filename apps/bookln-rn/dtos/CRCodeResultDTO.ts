export interface ResourceDTO {
  // 公告
  id: number;
  idSign: string;
  // 0=text=文字;1=vedio=视频;2=sound=音频;3=img=图片;4=url=网页;5=flash=flash;6=examItem=题目
  type: number;
  // 描述
  description?: string;
  title: string;
  // 链接
  content: string;
  // 视频等的时间
  times: number;
  // 外键id(题目)
  fkId: number;
  status: number;
  isDelete: number;
  length: number;
  thumbnails?: string;
  pv: number;
  // 认证值
  authVal: string;
  // 认证类型
  authType: number;
  // 用户认证值
  userAuthVal: number;
  crId: number;
  // 创建人ID
  createUser: number;
  // crid的签名
  crIdSign: string;
  tags: string;
  // 上层的CRID
  pcrId: number;
  // 上层的CRNAME
  pcrName: string;
  // res.id
  dirId: number;
  // 属于那本书
  bookId?: number;
  // 属于那本书
  bookName?: string;
  // 附件：url歌词等
  attachment?: string;
  // 附件名称
  attachmentName?: string;
  lyric?: string;
  // 下载地址
  downUrl?: string;
  // 是否能下载
  canDown: boolean;
  // 是否能缓存
  canShare: boolean;
  viewCount: number;
  userViewCount: number;
  // 媒体类型
  // 如果是经过保护的，也是返回真正的类型.如果mediaPlay.do的也是根据实际是mp4还是mp3u8返回
  // app端缓存的数据可能该字段为空;这个时候如果是视频默认就是mp4;音频默认就是mp3.后面根据服务端返回的这个字段更新
  mediaType?: number;
  flag: number;
  ress?: ResourceDTO[];
  isExam: number; // 服务器不返回; 自己判断; 例如课堂
  isComplete: number; // 服务器不返回; 自己判断; 例如课堂
  moduleId: number; // 服务器不返回; 自己判断; 例如课堂
  pkgIdSign?: string; // pkId的sign

  pkgId?: number;
  cameraStatus?: number; // 错题本是否存在拍照批改

  isTryNotBuy?: boolean; // 不显示下载按钮

  // 类型
  bizType?: number;

  /** 内容 */
  bizContent?: string;
  /** 官方资源限定访问，目前支持有视频，pdf，压缩包，ppt */
  endTime?: number;
}

export interface CrCodeDTO {
  id: number;
  // 二维码
  crCode: string;
  // 条形码
  barCode: string;
  // 字母表示随机码---数字表示页码
  prCode: string;
  // 资源ids的集合以逗号分开
  resIds: string;
  // 资源列表
  ress: Array<ResourceDTO>;
  bookName: string;
  description: string;
  bookPage: number;
  crName: string;
  status: number;
  isDelete: number;
  remark: string;
  bookId: number;
  createUserId: number;
  createUserName: string;
  // 扫描次数
  viewCount: number;
  // 认证类型
  authType: number;
  // 认证值
  authVal: string;
  // 认证类型
  userAuthVal: number;
  // resource.type比resource要细。比如多资源二维码分课堂
  type: number;
  qrIcon: string;
  // 缩略图
  thumbnails: string;
  // 用户数
  userViewCount: number;
  idSign: string;
  // 获取该CRCODE时请求的参数
  requestParam: string;
  tags: string;
  canDown: boolean;
  canShare: boolean;
  name?: string;
  pkgIdSign?: string;
  pkgId?: number;
  cameraStatus?: number; // 错题本是否存在拍照批改
  /**
   * 是否存在访问限定
   */
  accessStatus?: number;
}

export interface CRCodeResult {
  // 认证成功
  crcode: CrCodeDTO;
  // CRCODE_AUTH_ERROR_CODE_SUCCESS
  errorCode: number;
  errorMsg: string;
}
