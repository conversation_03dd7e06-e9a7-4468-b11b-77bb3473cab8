import type { BookDTO } from '../api/dto';

/** 点读页面信息接口 */
export interface ClickReadPage {
  id: number;
  /** 页面名称 */
  name?: string;
  /** 页码 */
  pageNo?: number;
  /** 节ID */
  sectionId?: number;
  /** 图片地址 */
  imgUrl?: string;
  /** 缩略图 */
  thumbnails?: string;
  /** 图片资源ID */
  imgResId?: number;
  /** 图片资源签名 */
  imgResSign?: string;
  /** 可点击的轨道信息列表 */
  tracks?: Array<ClickReadTrackinfo>;
  /** 权限类型 */
  authType?: number;
  /** 权限值 */
  authVal?: string;
  /** 排序 */
  orders?: number;
}

/** 点读轨道信息接口 */
export interface ClickReadTrackinfo {
  id: number;
  /** 资源URL */
  url?: string;
  /** 资源ID */
  resId?: number;
  /** 资源ID签名 */
  resSign?: string;
  /** 资源类型 */
  type?: number;
  /** 开始播放时间（毫秒） */
  ps?: number;
  /** 结束播放时间（毫秒） */
  pe?: number;
  /** 左侧距离 */
  l?: number;
  /** 顶部距离 */
  t?: number;
  /** 右侧距离 */
  r?: number;
  /** 底部距离 */
  b?: number;
  /** 原文 */
  text?: string;
  /** 翻译 */
  trans?: string;
}

/** 点读目录数据传输对象 */
export interface ClickReadCatalogDTO {
  id: number;
  /** 目录名称 */
  name?: string;
  /** 书籍ID */
  bookId?: number;
  /** 权限类型 */
  authType?: number;
  /** 权限值 */
  authVal?: string;
  /** 目录级别（1=章；2=节） */
  level?: number;
  /** 父目录ID */
  pid?: number;
  /** 页码 */
  pageNo?: number;
  /** 状态 */
  status?: number;
  /** 子节点列表 */
  sections?: Array<ClickReadCatalogDTO>;
  /** 页面列表（仅在level=section时有效） */
  pages?: Array<ClickReadPage>;
  /** 是否删除 */
  isDeleted?: number;
  /** 排序 */
  orders?: number;
}

/** 点读数据传输对象 */
export interface ClickReadDTO {
  id: number;
  /** 书籍ID */
  bookId?: number;
  /** 书籍名称 */
  bookName?: string;
  /** 权限类型（0=不需要权限；1=需要学习卡；4=需要购买） */
  authType?: number;
  /** 权限值 */
  authVal?: string;
  /** 状态 */
  status?: number;
  /** 章节目录 */
  chapters?: Array<ClickReadCatalogDTO>;
  /** 数据版本号 */
  dataVersion?: number;
  /** 资源总大小（图片+音频） */
  length?: number;
  /** 资源URL */
  url?: string;
  /** 创建人用户ID */
  createUserId?: number;
  /** 创建人用户名 */
  createUserName?: string;
  /** 页面类型（1-图片；2-PDF） */
  pageType?: number;
  /** 是否支持点读口语 */
  spoken?: boolean;
}

/** 用户服务实例数据传输对象 */
export interface UserServiceInstanceDTO {
  /** 服务实例ID */
  instanceId?: number;
  /** 服务编码 */
  code?: string;
  /** 服务实例名称 */
  name?: string;
  /** 服务图文描述 */
  description?: string;
  /** 图标URL */
  icon?: string;
  /** 服务操作类型 */
  actionType?: number;
  /** 操作内容（根据不同的code和action_type存储不同的值） */
  actionContent?: string;
}

/** 纸质书数据传输对象 */
export interface PaperBookDTO {
  id: number;
  /** 价格 */
  price?: number;
  /** 书名 */
  bookName?: string;
  /** 作者 */
  author?: string;
  /** 出版社 */
  publisher?: string;
  /** 描述 */
  description?: string;
  /** 缩略图 */
  thumbnails?: string;
  /** 销售数量 */
  saleNum?: number;
  /** 额外信息 */
  extraInfo?: string;
}

/** 电子书内容数据传输对象 */
export interface EbookContentDTO {
  /** 目录ID */
  catalogId?: number;
  /** 内容 */
  content?: string;
  /** 下一个目录ID */
  nextCatalogId?: number;
  /** 上一个目录ID */
  preCatalogId?: number;
  /** 标题 */
  title?: string;
  /** 基础href */
  baseHref?: string;
}

/** 电子书章节 */
export interface EbooksChapter {
  /** 权限类型 */
  authType?: number;
  /** 权限值 */
  authVal?: string;
  /** 书籍ID */
  bookId?: number;
  /** 超链接 */
  href?: string;
  id: number;
  /** 级别 */
  level?: number;
  /** 名称 */
  name?: string;
  /** 图片资源ID签名 */
  imgResIdSigns?: string;
  /** 图片资源ID */
  imgResIds?: string;
  /** 章节小节 */
  sections?: EbooksChapterSection[];
}

/** 电子书章节小节 */
export interface EbooksChapterSection {
  /** 权限类型 */
  authType?: number;
  /** 权限值 */
  authVal?: string;
  /** 书籍ID */
  bookId?: number;
  /** 超链接 */
  href?: string;
  id: number;
  /** 级别 */
  level?: number;
  /** 名称 */
  name?: string;
  /** 图片资源ID签名 */
  imgResIdSigns?: string;
  /** 图片资源ID */
  imgResIds?: string;
  /** 父级ID */
  pid?: number;
}

/** 电子书数据传输对象 */
export interface EbookDTO {
  id: number;
  /** 书籍ID */
  bookId?: number;
  /** 书名 */
  bookName?: string;
  /** CR ID */
  crId?: number;
  /** 权限类型 */
  authType?: number;
  /** 权限值 */
  authVal?: string;
  /** 类型 */
  type?: number;
  /** 文件资源ID */
  fileResId?: number;
  /** 解析状态 */
  analyzeStatus?: number;
  /** 在线状态 */
  onlineStatus?: number;
  /** 上线时间 */
  onlineTime?: number;
  /** 状态 */
  status?: number;
  /** 是否删除 */
  isDelete?: number;
  /** 销售数量 */
  saleNum?: number;
  /** 创建用户ID */
  createUserId?: number;
  /** 创建用户名 */
  createUserName?: string;
  /** 创建时间 */
  gmtCreate?: number;
  /** 修改时间 */
  gmtModified?: number;
  /** 章节列表 */
  chapters?: EbooksChapter[];
  /** 作者 */
  author?: string;
  /** 出版社 */
  publisher?: string;
  /** ISBN */
  isbn?: string;
  /** 缩略图 */
  thumbnails?: string;
  /** 是否可免费阅读 */
  hasFreeRead?: boolean;
  /** 电子书类型 */
  ebookType?: number;
  /** 首次上线时间 */
  firstOnlineTime?: number;
}

/** 错题包数据传输对象 */
export interface MistakePkgDTO {
  /** 分类ID */
  catId?: number;
  /** 创建时间 */
  gmtCreate?: number;
  /** 修改时间 */
  gmtModified?: number;
  id: number;
  /** 是否删除 */
  isDelete?: number;
  /** 名称 */
  name?: string;
  /** 在线状态 */
  onlineStatus?: number;
  /** 用户ID */
  puserId?: number;
  /** 来源类型 */
  sourceType?: number;
  /** 状态 */
  status?: number;
  /** 目标ID */
  targetId?: number;
  /** 目标类型 */
  targetType?: number;
  /** URL */
  url?: string;
  /** 用户ID */
  userId?: number;
  /** 用户名 */
  userName?: string;
  /** 权限类型 */
  authType?: number;
  /** 权限值 */
  authVal?: number;
}

/** 书籍主页详情数据传输对象 */
export interface BookHomeDetailDTO {
  /** 书籍基本信息 */
  bookDTO?: BookDTO;
  /** 服务列表 */
  serviceList?: Array<UserServiceInstanceDTO>;
  /** 点读信息 */
  clickRead?: ClickReadDTO;
  /** 纸质书信息 */
  paperBook?: PaperBookDTO;
  /** 电子书信息 */
  ebookDTO?: EbookDTO;
  /** 错题包信息 */
  mistakePkgDTO?: MistakePkgDTO;
  /** 照片列表 */
  photos?: Array<string | null>;
  /** 相机校正URL */
  cameraCorrectUrl?: string;
  /** 错题信息（类型未知） */
  mistakeDTO?: unknown;
}
