export interface LessonResourceDTO {
  id: number;
  catalogId: number;
  lessonId: number;
  resId: number;
  resType: number;
  orders: number;
  isDelete: number;
}

export interface ResourceDTO {
  // 公告
  id: number;
  idSign: string;
  // 0=text=文字;1=vedio=视频;2=sound=音频;3=img=图片;4=url=网页;5=flash=flash;6=examItem=题目
  type: number;
  // 描述
  description?: string;
  title: string;
  // 链接
  content: string;
  // 视频等的时间
  times: number;
  // 外键id(题目)
  fkId: number;
  status: number;
  isDelete: number;
  length: number;
  thumbnails?: string;
  pv: number;
  // 认证值
  authVal: string;
  // 认证类型
  authType: number;
  // 用户认证值
  userAuthVal: number;
  crId: number;
  // 创建人ID
  createUser: number;
  // crid的签名
  crIdSign: string;
  tags: string;
  // 上层的CRID
  pcrId: number;
  // 上层的CRNAME
  pcrName: string;
  // res.id
  dirId: number;
  // 属于那本书
  bookId?: number;
  // 属于那本书
  bookName?: string;
  // 附件：url歌词等
  attachment?: string;
  // 附件名称
  attachmentName?: string;
  lyric?: string;
  // 下载地址
  downUrl?: string;
  // 是否能下载
  canDown: boolean;
  // 是否能缓存
  canShare: boolean;
  viewCount: number;
  userViewCount: number;
  // 媒体类型
  // 如果是经过保护的，也是返回真正的类型.如果mediaPlay.do的也是根据实际是mp4还是mp3u8返回
  // app端缓存的数据可能该字段为空;这个时候如果是视频默认就是mp4;音频默认就是mp3.后面根据服务端返回的这个字段更新
  mediaType?: number;
  flag: number;
  ress?: ResourceDTO[];
  isExam: number; // 服务器不返回; 自己判断; 例如课堂
  isComplete: number; // 服务器不返回; 自己判断; 例如课堂
  moduleId: number; // 服务器不返回; 自己判断; 例如课堂
  pkgIdSign?: string; // pkId的sign

  pkgId?: number;
  cameraStatus?: number; // 错题本是否存在拍照批改

  isTryNotBuy?: boolean; // 不显示下载按钮

  // 类型
  bizType?: number;

  /** 内容 */
  bizContent?: string;
  /** 官方资源限定访问，目前支持有视频，pdf，压缩包，ppt */
  endTime?: number;
}

export interface LessonCatalogDTO {
  // 试读
  id: number;
  lessonId: number;
  name: string;
  // 8=free=ŤĮēÁúč
  authType: number;
  authVal: string;
  orders: number;
  isDelete: number;
  resourceDTOList: Array<LessonResourceDTO>;
  ress: Array<ResourceDTO>;
}

export interface LessonDTO {
  // 免费
  id: number;
  idSign: string;
  // 课程名
  name: string;
  // 描述
  description: string;
  // 讲师
  teacher: string;
  // 0=free=免费;2=need_learn_card=需要学习卡°;4=need_price=需要购买
  authType: number;
  authVal: string;
  // 缩略图
  thumbnails: string;
  // 0=offline=下线;1=online=在线
  online: number;
  // 0=unrecommend=未分发;1=recommended=已分发
  recommendStatus: number;
  viewNum: number;
  userId: number;
  userName: string;
  status: number;
  isDelete: number;
  // h5url链接
  h5url: string;
  // 章
  chapters: Array<LessonCatalogDTO>;
  organization: string;
  photo: string;
  // 0=comm=普通;1=live=直播
  type: number;
  // 直播状态live.status
  liveStatus: number;

  // 简介图片，逗号分割
  images?: string;
  /* 秒杀折扣标识 */
  discount?: boolean;
  /* 秒杀折扣价格 */
  disVal?: number;
  /* 秒杀折扣生效时间 */
  disStartTime?: number;
  /* 秒杀折扣结束时间 */
  disEndTime?: number;
  /* 剩余数量 */
  disNum?: number;
  pintuan?: boolean;
  activeVal?: number;
  hasValidPeriod?: boolean;
  buyEndTime?: number;
  hasValidBuy?: boolean;
  validType?: number;
  validVal?: number;
  validUnit?: number;
}
