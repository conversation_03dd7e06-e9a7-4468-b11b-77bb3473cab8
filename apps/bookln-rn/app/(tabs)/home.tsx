import { JglYStack } from '@jgl/ui-v4';
import { Stack, useFocusEffect } from 'expo-router';
import { useCallback } from 'react';
import { BackHandler } from 'react-native';
import { HomeContent } from '../../components/HomeContent';

/* 首页的单本教材 */
export default function HomeScreen() {
  useFocusEffect(
    useCallback(() => {
      // 拦截返回键
      const onBackPress = () => {
        // 让应用回到后台
        BackHandler.exitApp();
        return true; // 阻止返回
      };

      BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () => {
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
      };
    }, []),
  );

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <JglYStack jglClassName='flex-1 relative h-full bg-[#F0F4FF]'>
        <HomeContent />
      </JglYStack>
    </>
  );
}
