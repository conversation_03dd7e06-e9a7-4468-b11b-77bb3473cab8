import { useMount } from 'ahooks';
import { Tabs } from 'expo-router';
import Sound from 'react-native-sound';
import { useTamaguiTheme } from '../../hooks/useBooklnThemes';

export default function TabLayout() {
  const theme = useTamaguiTheme();
  const tintColor = theme?.color9.val;

  useMount(() => {
    Sound.setCategory('Playback');
  });

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: tintColor,
      }}
    >
      <Tabs.Screen
        name='home'
        options={{
          title: '首页',
          tabBarStyle: { display: 'none' },
        }}
      />
    </Tabs>
  );
}
