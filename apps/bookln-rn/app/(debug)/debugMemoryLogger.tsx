/* eslint-disable */
import type { MemoryLog } from '@yunti-private/rn-memory-logger';
import { MemoryLogger } from '@yunti-private/rn-memory-logger';
import { Stack, router } from 'expo-router';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import type { ListRenderItemInfo } from 'react-native';
import { FlatList, StyleSheet } from 'react-native';
import { Button, Paragraph, YStack } from 'tamagui';
import { showToast } from '@jgl/utils';
// import { YTProgressHUD } from 'yunti-rn-components';
// import { YTProgressHUD } from 'yunti-rn-components';

export default function DebugMemoryLoggerScreen() {
  const [flatListStatus, setFlatListStatus] = useState(true);
  const [logs, setLogs] = useState<MemoryLog[]>([]);

  useEffect(() => {
    const resultLogs = MemoryLogger.getLogs();
    console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ useEffect ~ resultLogs:', resultLogs);
    setLogs(resultLogs);
  }, []);

  const handleClearLogs = useCallback(() => {
    MemoryLogger.cleanLogs();
    setFlatListStatus(false);
    showToast({ title: '内存日志已清空' });
    setTimeout(() => {
      router.back();
    }, 500);
  }, []);

  const renderItem = useCallback((info: ListRenderItemInfo<MemoryLog>) => {
    const readableTime = dayjs(info.item.time).format('YYYY-MM-DD HH:mm:ss.SSS');
    return (
      <Paragraph style={styles.text} size="$4">{`${readableTime}\n${info.item.content}`}</Paragraph>
    );
  }, []);

  return (
    <>
      <Stack.Screen
        options={{
          headerTitle: '内存日志',
          headerRight: () => (
            <Button onPress={handleClearLogs} variant="outlined">
              清空
            </Button>
          ),
        }}
      />
      <YStack flex={1}>
        <FlatList data={flatListStatus ? logs : []} style={styles.list} renderItem={renderItem} />
      </YStack>
    </>
  );
}

const styles = StyleSheet.create({
  list: { flex: 1 },
  text: { padding: 15 },
});
