import { useCallback, useState } from 'react';
import { ScrollView, Spinner, Text, View, XStack, YStack, useTheme } from 'tamagui';
import { TamaguiThemeSwitch } from '../../components/TamaguiThemeSwitch';

export default function DebugTamaguiScreen() {
  const theme = useTheme();
  const [selectedColor, setSelectedColor] = useState<string | undefined>(undefined);

  const {
    color1,
    color2,
    color3,
    color4,
    color5,
    color6,
    color7,
    color8,
    color9,
    color10,
    color11,
    color12,

    color,
    colorHover,
    colorPress,
    colorFocus,
    colorTransparent,

    background,
    backgroundHover,
    backgroundPress,
    backgroundFocus,
    backgroundStrong,
    backgroundTransparent,

    borderColor,
    borderColorHover,
    borderColorFocus,
    borderColorPress,

    placeholderColor,

    shadowColor,
    shadowColorFocus,
    shadowColorHover,
    shadowColorPress,

    blue1,
    blue2,
    blue3,
    blue4,
    blue5,
    blue6,
    blue7,
    blue8,
    blue9,
    blue10,
    blue11,
    blue12,

    yellow1,
    yellow2,
    yellow3,
    yellow4,
    yellow5,
    yellow6,
    yellow7,
    yellow8,
    yellow9,
    yellow10,
    yellow11,
    yellow12,

    orange1,
    orange2,
    orange3,
    orange4,
    orange5,
    orange6,
    orange7,
    orange8,
    orange9,
    orange10,
    orange11,
    orange12,

    pink1,
    pink2,
    pink3,
    pink4,
    pink5,
    pink6,
    pink7,
    pink8,
    pink9,
    pink10,
    pink11,
    pink12,

    red1,
    red2,
    red3,
    red4,
    red5,
    red6,
    red7,
    red8,
    red9,
    red10,
    red11,
    red12,

    gray1,
    gray2,
    gray3,
    gray4,
    gray5,
    gray6,
    gray7,
    gray8,
    gray9,
    gray10,
    gray11,
    gray12,

    purple1,
    purple2,
    purple3,
    purple4,
    purple5,
    purple6,
    purple7,
    purple8,
    purple9,
    purple10,
    purple11,
    purple12,

    green1,
    green2,
    green3,
    green4,
    green5,
    green6,
    green7,
    green8,
    green9,
    green10,
    green11,
    green12,
  } = theme;

  const colorGroups = {
    basic: [
      color1,
      color2,
      color3,
      color4,
      color5,
      color6,
      color7,
      color8,
      color9,
      color10,
      color11,
      color12,
    ],
    func: [color, colorHover, colorPress, colorFocus, colorTransparent],
    bg: [
      background,
      backgroundHover,
      backgroundPress,
      backgroundFocus,
      backgroundStrong,
      backgroundTransparent,
    ],
    border: [borderColor, borderColorHover, borderColorFocus, borderColorPress, placeholderColor],
    shadow: [shadowColor, shadowColorFocus, shadowColorHover, shadowColorPress],
    blue: [blue1, blue2, blue3, blue4, blue5, blue6, blue7, blue8, blue9, blue10, blue11, blue12],
    yellow: [
      yellow1,
      yellow2,
      yellow3,
      yellow4,
      yellow5,
      yellow6,
      yellow7,
      yellow8,
      yellow9,
      yellow10,
      yellow11,
      yellow12,
    ],
    orange: [
      orange1,
      orange2,
      orange3,
      orange4,
      orange5,
      orange6,
      orange7,
      orange8,
      orange9,
      orange10,
      orange11,
      orange12,
    ],
    pink: [pink1, pink2, pink3, pink4, pink5, pink6, pink7, pink8, pink9, pink10, pink11, pink12],
    red: [red1, red2, red3, red4, red5, red6, red7, red8, red9, red10, red11, red12],
    gray: [gray1, gray2, gray3, gray4, gray5, gray6, gray7, gray8, gray9, gray10, gray11, gray12],
    purple: [
      purple1,
      purple2,
      purple3,
      purple4,
      purple5,
      purple6,
      purple7,
      purple8,
      purple9,
      purple10,
      purple11,
      purple12,
    ],
    green: [
      green1,
      green2,
      green3,
      green4,
      green5,
      green6,
      green7,
      green8,
      green9,
      green10,
      green11,
      green12,
    ],
  };

  const colorEntries = Object.entries(colorGroups);

  return (
    <ScrollView bg={'$background'} className="flex-1 px-1">
      <YStack space>
        <Spinner />
        <TamaguiThemeSwitch />

        {colorEntries.map((item) => {
          return (
            <YStack space={'$2'} key={item[0]}>
              <Text>{item[0]}</Text>
              {item[1].map((c) => {
                return (
                  <ColorCube
                    isSelected={selectedColor === c.val}
                    onSelect={(selected) => setSelectedColor(selected)}
                    key={c.key}
                    color={c}
                  />
                );
              })}
            </YStack>
          );
        })}
        <XStack></XStack>
      </YStack>
    </ScrollView>
  );
}

const ColorCube = <T extends { key: string; val: string }>(props: {
  color: T;
  isSelected: boolean;
  onSelect: (colorValue: string) => void;
}) => {
  const { color, isSelected, onSelect } = props;
  const { key, val } = color;

  const onPress = useCallback(() => {
    onSelect(val);
  }, [onSelect, val]);

  return (
    <XStack onPress={onPress} key={key} space>
      <View
        borderColor={isSelected ? 'black' : undefined}
        borderWidth={isSelected ? 1 : undefined}
        className={`w-[20]`}
        bg={isSelected ? val : '$colorTransparent'}
      />
      <View className="w-[100]" bg={val} />
      <Text className="text-right">{key}</Text>
      <Text className="flex-1 text-center">{val}</Text>
    </XStack>
  );
};
