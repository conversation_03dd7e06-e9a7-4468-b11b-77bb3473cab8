import { showToast, useRouterParams } from '@jgl/utils';
import Clipboard from '@react-native-clipboard/clipboard';
import { Stack } from 'expo-router';
import { ScrollView } from 'react-native';
import { Text } from 'tamagui';

export default function DebugInfoScreen() {
  const { info, title } = useRouterParams();
  return (
    <>
      <Stack.Screen options={{ headerTitle: title }} />
      <ScrollView
        className='flex-1 bg-white px-2'
        contentInset={{ bottom: 40 }}
      >
        <Text
          onPress={() => {
            if (info) {
              Clipboard.setString(info);
              showToast({ title: '已复制' });
            }
          }}
        >
          {info}
        </Text>
      </ScrollView>
    </>
  );
}
