import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { ScanChooseBookList } from '../components/ScanChooseBookList';
/**
 * 扫码选择书单
 */
export default function ScanChooseBookListScreen() {
  return (
    <>
      <Stack.Screen options={{ headerTitle: '书籍选择', headerShadowVisible: false }} />
      <StatusBar style="dark" />
      <ScanChooseBookList />
    </>
  );
}
