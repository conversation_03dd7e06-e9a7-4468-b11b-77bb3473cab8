import {
  type MathExerciseDTO,
  type <PERSON><PERSON><PERSON>RivalDTO,
  type Math<PERSON>KRulesDTO,
  type MathQuestionExerciseDTO,
  OralArithmeticPKResultScreen,
} from '@bookln/math-exam';
import { useRouterParams } from '@jgl/utils';
import { router, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useCallback } from 'react';

export default function OralArithmeticPKResult() {
  const {
    exerciseDTO: exerciseDTOString,
    userDTO: userDTOString,
    rivalDTO: rivalDTOString,
    userTotalScore: userTotalScoreString,
    rivalTotalScore: rivalTotalScoreString,
    userAnswer: userAnswerString,
    needReport: needReportString,
  } = useRouterParams<{
    exerciseDTO: string;
    userDTO: string;
    rivalDTO: string;
    userTotalScore: string;
    rivalTotalScore: string;
    userAnswer: string;
    needReport: string;
  }>();

  // console.log(
  //   'leejunhui - 🔥🔥🔥🔥🔥🔥 - OralArithmeticPKResult - exerciseDTOString',
  //   exerciseDTOString,
  // );
  const exerciseDTO = JSON.parse(exerciseDTOString || '') as MathExerciseDTO & {
    rules: MathPKRulesDTO;
  };
  // console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - OralArithmeticPKResult - exerciseDTO', exerciseDTO);

  // console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - OralArithmeticPKResult - userDTOString', userDTOString);
  const userDTO = JSON.parse(userDTOString || '') as {
    nick: string;
    avatar: { uri: string } | number;
  };
  // console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - OralArithmeticPKResult - userDTO', userDTO);

  // console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - OralArithmeticPKResult - rivalDTOString', rivalDTOString);
  const rivalDTO = JSON.parse(rivalDTOString || '') as MathPKRivalDTO;
  // console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - OralArithmeticPKResult - rivalDTO', rivalDTO);

  // console.log(
  //   'leejunhui - 🔥🔥🔥🔥🔥🔥 - OralArithmeticPKResult - userTotalScoreString',
  //   userTotalScoreString,
  // );
  const userTotalScore = Number.parseInt(userTotalScoreString || '0');
  // console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - OralArithmeticPKResult - userTotalScore', userTotalScore);

  // console.log(
  //   'leejunhui - 🔥🔥🔥🔥🔥🔥 - OralArithmeticPKResult - rivalTotalScoreString',
  //   rivalTotalScoreString,
  // );
  const rivalTotalScore = Number.parseInt(rivalTotalScoreString || '0');
  // console.log(
  //   'leejunhui - 🔥🔥🔥🔥🔥🔥 - OralArithmeticPKResult - rivalTotalScore',
  //   rivalTotalScore,
  // );

  // console.log(
  //   'leejunhui - 🔥🔥🔥🔥🔥🔥 - OralArithmeticPKResult - userAnswerString',
  //   userAnswerString,
  // );
  const userAnswer = JSON.parse(userAnswerString || '') as MathQuestionExerciseDTO[];
  // console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - OralArithmeticPKResult - userAnswer', userAnswer);

  // console.log(
  //   'leejunhui - 🔥🔥🔥🔥🔥🔥 - OralArithmeticPKResult - needReportString',
  //   needReportString,
  // );
  const needReport = needReportString === 'true';
  // console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - OralArithmeticPKResult - needReport', needReport);

  const onPressNavBack = useCallback(() => {
    router.back();
  }, []);

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} getId={({ params }) => params?._t} />
      <StatusBar style="light" />
      <OralArithmeticPKResultScreen
        needReport={needReport}
        exerciseDTO={exerciseDTO}
        userDTO={userDTO}
        rivalDTO={rivalDTO}
        userTotalScore={userTotalScore}
        rivalTotalScore={rivalTotalScore}
        userAnswer={userAnswer}
        onGoBack={onPressNavBack}
      />
    </>
  );
}
