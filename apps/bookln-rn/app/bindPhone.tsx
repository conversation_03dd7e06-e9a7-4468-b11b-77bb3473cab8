import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { BooklnLoginComponents } from '@bookln/bookln-biz';

const { BindMobileContent } = BooklnLoginComponents;

export default function BindPhoneScreen() {
  return (
    <>
      <Stack.Screen
        options={{ title: '绑定手机号', headerShadowVisible: false }}
      />
      <StatusBar style='dark' />
      <BindMobileContent />
    </>
  );
}
