import { BooklnLoginComponents } from '@bookln/bookln-biz';
import { DebugSettingsButton } from '@jgl/biz-components';
import { agreementStateAtom } from '@jgl/biz-func';
import { router } from '@jgl/utils';
import { Stack, useFocusEffect } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useAtomValue } from 'jotai';
import { useCallback, useMemo } from 'react';
import { BackHandler, Platform } from 'react-native';
import { routerMap } from '../utils/routerMap';

const { LoginContent } = BooklnLoginComponents;

export default function LoginScreen() {
  const onPressLoginByMobile = useCallback(() => {
    router.push(routerMap.LoginByPhone);
  }, []);

  const agreementState = useAtomValue(agreementStateAtom);

  const headerRight = useCallback(() => {
    return <DebugSettingsButton />;
  }, []);

  useFocusEffect(
    useCallback(() => {
      // 拦截返回键
      const onBackPress = () => {
        if (agreementState !== 'disagreed') {
          BackHandler.exitApp();
          return true;
        }
        return false;
      };

      BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () => {
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
      };
    }, [agreementState]),
  );

  const commonOptions = useMemo(
    () => ({
      title: '',
      headerShadowVisible: false,
      headerRight,
      gestureEnabled: agreementState === 'disagreed',
    }),
    [agreementState, headerRight],
  );

  const androidOptions = useMemo(
    () => ({
      headerBackVisible: agreementState === 'disagreed',
      headerLeft: agreementState === 'disagreed' ? undefined : () => null,
    }),
    [agreementState],
  );

  const iosOptions = useMemo(
    () =>
      agreementState === 'disagreed'
        ? {}
        : {
            headerBackVisible: false,
            headerLeft: () => null,
          },
    [agreementState],
  );

  const options = useMemo(() => {
    if (Platform.OS === 'ios') {
      return {
        ...commonOptions,
        ...iosOptions,
      };
    } else {
      return {
        ...commonOptions,
        ...androidOptions,
      };
    }
  }, [commonOptions, androidOptions, iosOptions]);

  return (
    <>
      <Stack.Screen options={options} />
      <StatusBar style='dark' />

      <LoginContent onPressLoginByMobile={onPressLoginByMobile} />
    </>
  );
}
