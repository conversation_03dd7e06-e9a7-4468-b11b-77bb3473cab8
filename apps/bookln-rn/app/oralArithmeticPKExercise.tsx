import {
  type MathExerciseDTO,
  type MathPKRivalDTO,
  type MathP<PERSON>RulesDTO,
  type MathQuestionExerciseDTO,
  OralArithmeticPKExerciseScreen,
} from '@bookln/math-exam';
import { router, useRouterParams } from '@jgl/utils';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useCallback } from 'react';
import { routerMap } from '../utils/routerMap';

export default function OralArithmeticPKExercise() {
  const {
    questions: questionsString,
    exerciseDTO: exerciseDTOString,
    userDTO: userDTOString,
    rivalDTO: rivalDTOString,
  } = useRouterParams<{
    questions: string;
    exerciseDTO: string;
    userDTO: string;
    rivalDTO: string;
  }>();

  const questions = JSON.parse(questionsString || '') as unknown as Array<{
    topic: string;
    answer: string;
  }>;

  const exerciseDTO = JSON.parse(exerciseDTOString || '') as MathExerciseDTO & {
    rules: MathPKRulesDTO;
  };
  const userDTO = JSON.parse(userDTOString || '') as {
    nick: string;
    avatar: { uri: string } | number;
  };
  const rivalDTO = JSON.parse(rivalDTOString || '') as MathPKRivalDTO;

  const handleBack = useCallback(() => {
    router.back();
  }, []);

  const handleComplete = useCallback(
    (param: {
      exerciseDTO: MathExerciseDTO & { rules: MathPKRulesDTO };
      userDTO: { avatar: { uri: string } | number; nick: string; headPortraitUrl?: string };
      rivalDTO: MathPKRivalDTO;
      userTotalScore: number;
      rivalTotalScore: number;
      userAnswer: MathQuestionExerciseDTO[];
      needReport: boolean;
    }) => {
      console.log(
        'leejunhui - 🔥🔥🔥🔥🔥🔥 - OralArithmeticPKExercise - handleComplete - param',
        param,
      );
      const {
        exerciseDTO: exerciseDTOFromParam,
        userDTO: userDTOFromParam,
        rivalDTO: rivalDTOFromParam,
        userTotalScore,
        rivalTotalScore,
        userAnswer,
        needReport,
      } = param;
      router.replace(routerMap.OralArithmeticPKResult, {
        exerciseDTO: JSON.stringify(exerciseDTOFromParam),
        userDTO: JSON.stringify(userDTOFromParam),
        rivalDTO: JSON.stringify(rivalDTOFromParam),
        userTotalScore: userTotalScore.toString(),
        rivalTotalScore: rivalTotalScore.toString(),
        userAnswer: JSON.stringify(userAnswer),
        needReport: needReport.toString(),
      });
    },
    [],
  );

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} getId={({ params }) => params?._t} />
      <StatusBar style="light" />
      <OralArithmeticPKExerciseScreen
        questions={questions}
        exerciseDTO={exerciseDTO}
        userDTO={userDTO}
        rivalDTO={rivalDTO}
        onBack={handleBack}
        onComplete={handleComplete}
      />
    </>
  );
}
