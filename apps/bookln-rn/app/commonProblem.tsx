import { useRouterParams } from '@jgl/utils';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { CommonProblem } from '../components/CommonProblem';
/**
 * 常见问题
 */
export default function CommonProblemScreen() {
  const { title } = useRouterParams();

  return (
    <>
      <Stack.Screen options={{ headerTitle: title, headerShadowVisible: false }} />
      <StatusBar style="dark" />
      <CommonProblem />
    </>
  );
}
