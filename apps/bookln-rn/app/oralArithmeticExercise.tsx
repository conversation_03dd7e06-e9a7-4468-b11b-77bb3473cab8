import {
  type MathExerciseDTO,
  type MathQuestionExerciseDTO,
  OralArithmeticExerciseScreen,
} from '@bookln/math-exam';
import { router, useRouterParams } from '@jgl/utils';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useCallback } from 'react';
import { routerMap } from '../utils/routerMap';

export default function OralArithmeticExercise() {
  const { questions: questionsString, exerciseDTO: exerciseDTOString } = useRouterParams<{
    questions: string;
    exerciseDTO: string;
  }>();

  const questions = JSON.parse(questionsString || '') as unknown as Array<{
    topic: string;
    answer: string;
  }>;
  const exerciseDTO = JSON.parse(exerciseDTOString || '') as MathExerciseDTO;

  const handleBack = useCallback(() => {
    router.back();
  }, []);

  const handleComplete = useCallback(
    (param: {
      exerciseDTO: MathExerciseDTO;
      questions: MathQuestionExerciseDTO[];
      seconds: number;
      needReport: boolean;
    }) => {
      router.replace(routerMap.OralArithmeticResult, {
        questions: JSON.stringify(param.questions),
        exerciseDTO: JSON.stringify(param.exerciseDTO),
        seconds: param.seconds.toString(),
        needReport: param.needReport.toString(),
      });
    },
    [],
  );

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} getId={({ params }) => params?._t} />
      <StatusBar style="light" />
      <OralArithmeticExerciseScreen
        questions={questions}
        exerciseDTO={exerciseDTO}
        onComplete={handleComplete}
        onBack={handleBack}
      />
    </>
  );
}
