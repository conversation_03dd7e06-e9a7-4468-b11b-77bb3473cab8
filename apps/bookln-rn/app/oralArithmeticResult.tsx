import {
  type MathExerciseDTO,
  type MathQuestionExerciseDTO,
  OralArithmeticResultScreen,
} from '@bookln/math-exam';
import { router, useRouterParams } from '@jgl/utils';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useCallback } from 'react';
import { routerMap } from '../utils/routerMap';

export default function OralArithmeticResult() {
  const {
    questions: questionsString,
    exerciseDTO: exerciseDTOString,
    seconds: secondsString,
    needReport: needReportString,
  } = useRouterParams<{
    questions: string;
    exerciseDTO: string;
    seconds: string;
    needReport: string;
  }>();

  const questions = JSON.parse(questionsString || '') as unknown as Array<MathQuestionExerciseDTO>;
  const exerciseDTO = JSON.parse(exerciseDTOString || '') as MathExerciseDTO;
  const seconds = Number(secondsString || 0);
  const needReport = needReportString === 'true';

  // const bizRouter = useBizRouter();

  const onStartStudy = useCallback(() => {
    router.replace(routerMap.OralArithmeticExercise, {
      questions: JSON.stringify(questions),
      exerciseDTO: JSON.stringify(exerciseDTO),
      _t: Date.now(),
    });
  }, [questions, exerciseDTO]);

  return (
    <>
      <Stack.Screen options={{ title: '练习详情' }} />
      <StatusBar style="dark" />
      <OralArithmeticResultScreen
        questions={questions}
        exerciseDTO={exerciseDTO}
        seconds={seconds}
        needReport={needReport}
        onStartStudy={onStartStudy}
      />
    </>
  );
}
