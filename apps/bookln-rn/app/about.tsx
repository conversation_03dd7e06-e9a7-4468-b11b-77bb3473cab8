import { externalUrls, openExternalLink } from '@jgl/biz-func';
import { envVars } from '@jgl/utils';
import { ChevronRight } from '@tamagui/lucide-icons';
import { Stack } from 'expo-router';
import { Image, Pressable, StyleSheet } from 'react-native';
import { ScrollView, Text, YGroup, YStack } from 'tamagui';

export default function AboutScreen() {
  return (
    <>
      <Stack.Screen options={{ headerTitle: '关于', headerShadowVisible: false }} />
      <ScrollView
        className="flex-1 bg-white"
        contentInset={{ top: 20, bottom: 40 }}
        contentContainerStyle={styles.content}
      >
        <Image className="my-16" source={require('../assets/images/ic_log_in_logo.png')} />

        <YStack space>
          <YGroup>
            <Pressable
              className="flex-row items-center justify-center"
              onPress={() => {
                openExternalLink(externalUrls.icpLookUp.url);
              }}
            >
              <Text className="text-center">ICP备案号：{envVars.appIcp()}</Text>
              <ChevronRight size={16} />
            </Pressable>
          </YGroup>

          <YGroup>
            <YStack space={'$1'}>
              <Text className="text-center">云梯科技 版权所有</Text>
              <Text className="text-center">
                Copyright © {new Date().getFullYear()} Yunti. All Rights Reserved.
              </Text>
            </YStack>
          </YGroup>
        </YStack>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  content: { alignItems: 'center', justifyContent: 'center' },
});
