import { JglImage, JglView } from '@jgl/ui-v4';
import { Stack } from 'expo-router';

export default function AiBookKnowledgePointDetailScreen() {
  return (
    <>
      <Stack.Screen
        options={{
          title: '知识点详情',
        }}
      />
      <JglView flex={1}>
        <JglImage
          source={require('../../assets/images/ic_knowledge.png')}
          flex={1}
          resizeMode='contain'
        />
      </JglView>
    </>
  );
}
