import { JglStateView } from '@jgl/ui-v4';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useAtomValue } from 'jotai';
import { atomMap } from '../../atom';
import { AiBookTopicList } from '../../components/aiBook';
import { useFetchAiBookData } from '../../hooks/aiBook';

export default function MyWrongExercise() {
  const { isLoading } = useFetchAiBookData();
  const { wrongTopics } = useAtomValue(atomMap.aiBookMockData);

  return (
    <>
      <Stack.Screen
        options={{
          headerTitle: '我的错题',
          headerShadowVisible: false,
        }}
      />
      <StatusBar style='dark' />
      <JglStateView isLoading={isLoading} isEmpty={wrongTopics.length === 0}>
        <AiBookTopicList type='wrong' />
      </JglStateView>
    </>
  );
}
