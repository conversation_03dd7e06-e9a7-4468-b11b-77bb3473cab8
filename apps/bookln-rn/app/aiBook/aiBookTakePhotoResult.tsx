import { JglSafeArea } from '@jgl/ui-v4';
import { router } from '@jgl/utils';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useAtom, useSetAtom } from 'jotai';
import { useEffect } from 'react';
import { atomMap } from '../../atom';
import { AiBookTakePhotoResultDetail } from '../../components/aiBook';

export default function AiBookTakePhotoResult() {
  const [aiBookInfo, setAiBookInfo] = useAtom(atomMap.aiBookInfo);
  const setAiBookTakePhotoResult = useSetAtom(atomMap.aiBookTakePhotoResult);
  const { pageId } = aiBookInfo;
  const handlePress = (item: { pageId?: number; itemId?: number }) => {
    const { pageId: pid, itemId } = item;
    if (pageId && itemId) {
      setAiBookInfo((prev) => {
        return {
          ...prev,
          pageId: pid,
          topicId: itemId,
        };
      });
    }
    router.back();
  };

  useEffect(() => {
    if (!pageId) return;
    setAiBookTakePhotoResult((prev) => {
      return {
        ...prev,
        showResultPages: new Set(prev.showResultPages.add(pageId)),
      };
    });
  }, [aiBookInfo.pageId, pageId, setAiBookTakePhotoResult]);

  return (
    <>
      <Stack.Screen
        options={{
          headerTitle: '批改结果',
          headerShadowVisible: false,
          headerShown: true,
        }}
      />
      <StatusBar style='dark' />
      <JglSafeArea>
        <AiBookTakePhotoResultDetail type='page' onPress={handlePress} />
      </JglSafeArea>
    </>
  );
}
