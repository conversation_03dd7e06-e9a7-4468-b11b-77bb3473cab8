import { JglStateView } from '@jgl/ui-v4';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useAtomValue } from 'jotai';
import { atomMap } from '../../atom';
import { AiBookTopicList } from '../../components/aiBook';
import { useFetchAiBookData } from '../../hooks/aiBook';

export default function MyCollection() {
  useFetchAiBookData();
  const { isLoading } = useFetchAiBookData();
  const { collectionTopics } = useAtomValue(atomMap.aiBookMockData);

  return (
    <>
      <Stack.Screen
        options={{
          headerTitle: '我的收藏',
          headerShadowVisible: false,
          headerShown: true,
        }}
      />
      <StatusBar style='dark' />
      <JglStateView
        isLoading={isLoading}
        isEmpty={collectionTopics.length === 0}
      >
        <AiBookTopicList type='collection' />
      </JglStateView>
    </>
  );
}
