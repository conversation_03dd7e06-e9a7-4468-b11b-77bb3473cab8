import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { AiBookTopicList } from '../../components/aiBook';

export default function BookCollection() {
  return (
    <>
      <Stack.Screen
        options={{
          headerTitle: '本书收藏',
          headerShadowVisible: false,
          headerShown: true,
        }}
      />
      <StatusBar style='dark' />
      <AiBookTopicList type='collection' />
    </>
  );
}
