import { useBizRouter } from '@jgl/biz-func';
import { Stack } from 'expo-router';
import { useCallback } from 'react';
import { AiBookWordEliminate } from '../../components/aiBook/scene/AiBookWordEliminate';

export default function WordEliminate() {
  const router = useBizRouter();

  const handleExit = useCallback(() => {
    router.back();
  }, [router]);

  return (
    <>
      <Stack.Screen options={{ title: '单词消消乐' }} />
      <AiBookWordEliminate onEnd={() => {}} onExit={handleExit} />
    </>
  );
}
