import {
  useNavigationBarBarHeight,
  useNavigationBarHeight,
  useSafeAreaInsets,
} from '@jgl/biz-func';
import { JglImage, JglTouchable, JglXStack, JglYStack } from '@jgl/ui-v4';
import { router, showToast } from '@jgl/utils';
import { Camera, FlashMode } from 'expo-camera';
// import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import {
  PermissionPurposeScene,
  PermissionHooks,
  PermissionEnum,
} from '@bookln/permission';
import { useCallback, useMemo, useRef, useState } from 'react';
import { routerMap } from '../../utils/routerMap';

export default function AiBookTakePhoto() {
  const safeInsets = useSafeAreaInsets();
  const navigationBarHeight = useNavigationBarHeight();
  const barHeight = useNavigationBarBarHeight();

  const cameraRef = useRef<Camera>(null);
  const [isCameraReady, setIsCameraReady] = useState(false);
  const [flashMode, setFlashMode] = useState(FlashMode.off);
  const { checkAndRequestPermission } = PermissionHooks.usePermission();
  const [isUploading, setIsUploading] = useState(false);

  const onCommitPhoto = useCallback(
    async (param: {
      url: string;
      /**
       * 图片大小 in Bytes
       */
      size: number;
      width: number;
      height: number;
    }) => {
      showToast({
        title: '上传中...',
      });
      setIsUploading(true);
      // const imageUrl = await OSSUploadNative.uploadToOSSPermanently({
      //   tmpPath: url,
      //   options: {
      //     net: container.net(),
      //   },
      //   bizCode: 'bookln',
      //   imageDimensions: { width, height },
      // });
      await new Promise((resolve) => setTimeout(resolve, 1000));
      router.replace(routerMap.AiBookTakePhotoResult, {
        image: param.url,
        width: param.width,
        height: param.height,
      });
    },
    [],
  );

  const onPressTakePhoto = useCallback(async () => {
    if (cameraRef.current && isCameraReady) {
      if (isUploading) {
        return;
      }

      const result = await cameraRef.current.takePictureAsync({
        base64: false,
        quality: 1,
      });
      console.log('take Photo result', result);

      // const fileInfo = await FileSystem.getInfoAsync(result.uri);
      // console.log('take Photo fileInfo', fileInfo);

      onCommitPhoto({
        url: result.uri,
        width: result.width,
        height: result.height,
        size: 0,
      });
    }
  }, [isCameraReady, isUploading, onCommitPhoto]);

  const onPressNavBack = useCallback(() => {
    if (isUploading) {
      return;
    }
    router.back();
  }, [isUploading]);

  const renderNavBar = useMemo(() => {
    return (
      <JglXStack
        position='fixed'
        top={0}
        left={0}
        right={0}
        zIndex={1000}
        w='full'
        h={safeInsets.top + navigationBarHeight}
        ai='center'
      >
        <JglXStack
          position='absolute'
          w='full'
          left={0}
          right={0}
          h={barHeight}
          bg='green'
          px={16}
        >
          <JglTouchable onPress={onPressNavBack}>
            <JglImage
              source={require('../../assets/images/ic_nav_back_white.png')}
              w={24}
              h={24}
            />
          </JglTouchable>
        </JglXStack>
      </JglXStack>
    );
  }, [barHeight, navigationBarHeight, onPressNavBack, safeInsets.top]);

  const onPressOpenAlbum = useCallback(async () => {
    if (isUploading) {
      return;
    }

    const authResult = await checkAndRequestPermission({
      permission: PermissionEnum.Album,
      scene: PermissionPurposeScene.ChoicePicture,
    });
    if (authResult) {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        aspect: [4, 3],
        quality: 1,
        selectionLimit: 1,
      });
      console.log('🚀 ~ handlePickPhoto ~ result:', result);
      if (!result.canceled) {
        if (result.assets.length > 0) {
          const asset = result.assets[0];
          if (asset) {
            const { uri, width, height } = asset;
            if (uri) {
              onCommitPhoto({
                url: uri,
                width,
                height,
                size: 0,
              });
            }
          }
        }
      }
    }
  }, [checkAndRequestPermission, isUploading, onCommitPhoto]);

  const renderBottomPanel = useMemo(() => {
    return (
      <JglXStack
        position='fixed'
        w='full'
        left={0}
        right={0}
        bottom={0}
        px={24}
        pb={safeInsets.bottom}
        h={142 + safeInsets.bottom}
        ai='center'
        justifyContent='space-between'
      >
        <JglTouchable
          w={44}
          h={44}
          bg='#262626'
          ai='center'
          jc='center'
          borderRadius={9999}
          onPress={onPressOpenAlbum}
        >
          <JglImage
            source={require('../../assets/images/ic_open_album.png')}
            w={24}
            h={24}
          />
        </JglTouchable>
        <JglTouchable
          w={64}
          h={64}
          bg='#262626'
          ai='center'
          jc='center'
          borderRadius={9999}
          onPress={onPressTakePhoto}
        >
          <JglImage
            source={require('../../assets/images/ic_take_photo_btn.png')}
            w={64}
            h={64}
          />
        </JglTouchable>
        <JglTouchable
          w={44}
          h={44}
          bg={flashMode === FlashMode.off ? '#262626' : 'white'}
          ai='center'
          jc='center'
          borderRadius={9999}
          onPress={() =>
            setFlashMode(
              flashMode === FlashMode.off ? FlashMode.torch : FlashMode.off,
            )
          }
        >
          <JglImage
            source={
              flashMode === FlashMode.off
                ? require('../../assets/images/ic_flash_light_off.png')
                : require('../../assets/images/ic_flash_light_on.png')
            }
            w={24}
            h={24}
          />
        </JglTouchable>
      </JglXStack>
    );
  }, [flashMode, onPressOpenAlbum, onPressTakePhoto, safeInsets.bottom]);

  const onCameraReady = useCallback(() => {
    setIsCameraReady(true);
  }, []);

  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar style='light' />
      <JglYStack flex={1} bg='black' position='relative'>
        {renderNavBar}
        <Camera
          style={{ flex: 1 }}
          ref={cameraRef}
          onCameraReady={onCameraReady}
          ratio='16:9'
          flashMode={flashMode}
          autoFocus
        />
        {renderBottomPanel}
      </JglYStack>
    </>
  );
}
