import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { AiBookTopicList } from '../../components/aiBook';

export default function BookWrongExercise() {
  return (
    <>
      <Stack.Screen
        options={{
          headerTitle: '本书错题',
          headerShadowVisible: false,
        }}
      />
      <StatusBar style='dark' />
      <AiBookTopicList type='wrong' />
    </>
  );
}
