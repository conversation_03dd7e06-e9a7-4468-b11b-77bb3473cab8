import {
  imFloatBallVisibleAtom,
  useBookAIShowInputToolBarTopFunction,
} from '@jgl/ai-qa-v2';
import { useMount, useUnmount } from 'ahooks';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useAtomValue, useSetAtom } from 'jotai';
import { useEffect } from 'react';
import { atomMap } from '../../atom';
import { AiBookPageDetail } from '../../components/aiBook';

export default function AiBookDetailScreen() {
  const setImFloatBallVisibleAtom = useSetAtom(imFloatBallVisibleAtom);
  const {
    handleShowInputToolBarTopFunctionPanel,
    handleHideInputToolBarTopFunctionPanel,
  } = useBookAIShowInputToolBarTopFunction();
  const aiBookInfo = useAtomValue(atomMap.aiBookInfo);
  useMount(() => {
    setImFloatBallVisibleAtom(true);
  });
  useUnmount(() => {
    setImFloatBallVisibleAtom(false);
  });

  const inTopic = !!aiBookInfo.topicId;

  useEffect(() => {
    if (inTopic) {
      handleShowInputToolBarTopFunctionPanel();
    } else {
      handleHideInputToolBarTopFunctionPanel();
    }
  }, [
    inTopic,
    handleHideInputToolBarTopFunctionPanel,
    handleShowInputToolBarTopFunctionPanel,
  ]);

  return (
    <>
      <Stack.Screen
        options={{
          headerTitle: '',
          headerShadowVisible: false,
          headerShown: false,
        }}
      />
      <StatusBar style='dark' />
      <AiBookPageDetail />
    </>
  );
}
