import { DebugSettingsButton, StateView } from '@jgl/biz-components';
import {
  agreementLinks,
  agreementStateAtom,
  openExternalLink,
  useAgreementScreenIsModal,
} from '@jgl/biz-func';
import { envVars } from '@jgl/utils';
import { useRouter } from 'expo-router';
import { useAtomValue } from 'jotai';
import { Suspense, useCallback, useEffect, useMemo, useRef } from 'react';
import { SafeAreaView, ScrollView, StyleSheet, View } from 'react-native';
import { Button, Spinner, Text, XStack } from 'tamagui';
import { StatusBarForModal } from '../components/StatusBarForModal';
import { useSetAgreementState } from '../hooks/useAgreementState';

export default function AgreementScreen() {
  const {
    agreementState,
    agreementScreenIsModal,
    externalLinks,
    handlePressAgree,
    handlePressDisagree,
  } = useAgreementScreen();
  const appName = envVars.appNameForUser();

  const buttonDisabled = useMemo(() => {
    return (
      agreementState === 'isAgreeing' || agreementState === 'isDisagreeing'
    );
  }, [agreementState]);

  return (
    <>
      {agreementScreenIsModal ? <StatusBarForModal /> : null}
      <SafeAreaView className='flex-1 bg-white'>
        <Suspense fallback={<StateView isLoading />}>
          <View className='items-end'>
            <DebugSettingsButton />
          </View>
          <Text className='m-8 text-center text-3xl font-bold'>
            欢迎使用
            {'\n'}
            {appName}
          </Text>
          <ScrollView
            className='flex-1'
            contentInset={{ bottom: 20 }}
            contentContainerStyle={styles.scrollContentContainer}
          >
            <Text className='mx-6 text-lg'>
              我们非常重视您的个人信息及隐私保护，为了更好地保障您的个人权益，在您使用
              {appName}服务前，请务必认真阅读
              {externalLinks.map((e, i) => {
                const isLastOne = i === externalLinks.length - 1;
                return (
                  <>
                    <ExternalLink key={e.url} externalUrl={e} />
                    {isLastOne ? null : <Text key={`${e.url}-${i}`}>、</Text>}
                  </>
                );
              })}
              的全部条款，以便您了解我们如何向您提供服务、保障您的合法权益，如何收集、使用、储存、共享您的相关个人信息，如何管理您的相关个人信息，以及我们对您提供的相关信息的保护方式等。我们会严格在您的授权范围内，按照上述协议约定的方式收集、使用、储存、共享您的账户信息、日志信息等。
            </Text>
          </ScrollView>

          <XStack space='$3' className='m-5'>
            <Button
              // TODO: aini - theme config
              className='flex-1 rounded-full'
              size={'$5'}
              disabled={buttonDisabled}
              onPress={handlePressDisagree}
            >
              {agreementState === 'isDisagreeing' ? <Spinner /> : '不同意'}
            </Button>
            <Button
              className='bg-primary flex-1 rounded-full text-white'
              theme={'active'}
              size={'$5'}
              disabled={buttonDisabled}
              onPress={handlePressAgree}
            >
              {agreementState === 'isAgreeing' ? (
                <Spinner color={'white'} />
              ) : (
                '同意'
              )}
            </Button>
          </XStack>
        </Suspense>
      </SafeAreaView>
    </>
  );
}

type ExternalLinkProps = {
  externalUrl: { name: string; url: string };
};

const ExternalLink = (props: ExternalLinkProps) => {
  const { externalUrl } = props;
  const { name, url } = externalUrl;

  return (
    <Text
      className='text-primary-active font-semibold'
      onPress={() => openExternalLink(url)}
    >
      《{name}》
    </Text>
  );
};

const useAgreementScreen = () => {
  const { agree, disagree } = useSetAgreementState();
  const shouldDismissModal = useRef<boolean>(false);
  const currentRouter = useRouter();
  const agreementState = useAtomValue(agreementStateAtom);
  const agreementScreenIsModal = useAgreementScreenIsModal();

  // 当状态变为 disagreed 的时候直接进入了首页
  // 再次打开协议页面是 modal 状态
  // 需要在同意协议后 dismiss modal
  useEffect(() => {
    if (agreementState === 'disagreed') {
      shouldDismissModal.current = true;
    }

    if (agreementState === 'agreed') {
      if (shouldDismissModal.current === true) {
        if (currentRouter.canGoBack()) {
          currentRouter.back();
        }
      }
    }
  }, [agreementState, currentRouter]);

  const handlePressAgree = useCallback(async () => {
    await agree();
  }, [agree]);

  return {
    externalLinks: agreementLinks,
    handlePressAgree,
    agreementState,
    agreementScreenIsModal,
    handlePressDisagree: disagree,
  };
};

const styles = StyleSheet.create({
  buttonContainer: { marginHorizontal: 6, flex: 1 },
  scrollContentContainer: { alignItems: 'center', flex: 1 },
});
