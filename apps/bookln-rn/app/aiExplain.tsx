import { PermissionPurposeScene } from '@bookln/permission';
import { PhotoAndAnalysis } from '@jgl/biz-components';
import { PhotoQuestionAnswerResult } from '@jgl/biz-components-rojer-katex-mini';
import { PhotoProgress } from '@jgl/biz-func';
import { JglTouchable } from '@jgl/ui-v4';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Image, View } from 'tamagui';
import { usePhotoQuestionAnswer } from '../hooks/usePhotoQuestionAnswer';

/**
 * AI讲解
 */
export default function AiExplainScreen() {
  const {
    // markdownResult,
    imageUrl,
    progress,
    boxList,
    loading,
    error,
    onAnalysisImage,
    updateProgress,
    onPressTakePhoto,
    onPressBack,
  } = usePhotoQuestionAnswer();

  return (
    <>
      <View className='flex h-full w-full flex-col'>
        <Stack.Screen
          options={{
            headerBackVisible: false,
            headerTransparent: true,
            headerLeft: () => (
              <JglTouchable onPress={onPressBack}>
                <Image
                  source={require('../assets/images/ic_circle_back.png')}
                  w={32}
                  h={32}
                />
              </JglTouchable>
            ),
            title: '',
          }}
        />
        <StatusBar style='light' />
        {progress !== PhotoProgress.Result && (
          <PhotoAndAnalysis
            progress={progress}
            onAnalysisImage={onAnalysisImage}
            updateProgress={updateProgress}
            scene={PermissionPurposeScene.AiExplain}
            title={'AI讲解'}
          />
        )}

        {progress === PhotoProgress.Result && (
          <PhotoQuestionAnswerResult
            imageUrl={imageUrl}
            loading={loading}
            visible={true}
            boxList={boxList}
            onPressTakePhoto={onPressTakePhoto}
            onPressBack={onPressBack}
            error={error}
            bizCode='bookln'
          />
        )}
      </View>
    </>
  );
}
