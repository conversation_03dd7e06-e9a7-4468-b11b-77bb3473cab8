import {
  type MathExerciseDTO,
  type MathQuestionDTO,
  OralArithmeticHomeScreen,
} from '@bookln/math-exam';
import { useNavigationBarHeight } from '@jgl/biz-func';
import { JglImage, JglText, JglTouchable, JglXStack } from '@jgl/ui-v4';
import { router } from '@jgl/utils';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useCallback, useMemo } from 'react';
import { routerMap } from '../utils/routerMap';

export default function OralArithmetic() {
  const navigationBarHeight = useNavigationBarHeight();

  const onPressNavBack = useCallback(() => {
    router.back();
  }, []);

  const renderNavBar = useMemo(() => {
    return (
      <JglXStack
        position="fixed"
        top={0}
        left={0}
        right={0}
        zIndex={1000}
        w="full"
        h={navigationBarHeight + 44}
        ai="center"
        bg="white"
      >
        <JglXStack
          position="absolute"
          w="full"
          left={0}
          right={0}
          h={44}
          px={16}
          ai="center"
          jc="center"
          bottom={0}
          bg="white"
        >
          <JglTouchable position="absolute" left={16} onPress={onPressNavBack}>
            <JglImage source={require('../assets/images/ic_back.png')} w={24} h={24} />
          </JglTouchable>
          <JglText color="$color12" fontSize={18} fontWeight="bold">
            口算练习
          </JglText>
        </JglXStack>
      </JglXStack>
    );
  }, [navigationBarHeight, onPressNavBack]);

  const onStartStudy = useCallback(
    (param: { questions: MathQuestionDTO[]; exerciseDTO: MathExerciseDTO }) => {
      console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ OralArithmetic ~ onStartStudy ~ param:', param);

      router.push(routerMap.OralArithmeticExercise, {
        questions: JSON.stringify(param.questions),
        exerciseDTO: JSON.stringify(param.exerciseDTO),
        _t: Date.now(),
      });
    },
    [],
  );

  const onStartPk = useCallback(
    (param: {
      exerciseDTO: MathExerciseDTO;
      questions: MathQuestionDTO[];
      userDTO: {
        nick: string;
        avatar: string;
      };
      rivalDTO: {
        headPortraitUrl: string;
        nick: string;
      };
    }) => {
      console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ OralArithmetic ~ onStartPk ~ param:', param);
      router.push(routerMap.OralArithmeticPKExercise, {
        exerciseDTO: JSON.stringify(param.exerciseDTO),
        questions: JSON.stringify(param.questions),
        userDTO: JSON.stringify(param.userDTO),
        rivalDTO: JSON.stringify(param.rivalDTO),
        _t: Date.now(),
      });
    },
    [],
  );

  return (
    <>
      <Stack.Screen options={{ title: '口算练习', headerShown: false }} />
      <StatusBar style="dark" />
      {renderNavBar}
      <OralArithmeticHomeScreen onStartStudy={onStartStudy} onStartPk={onStartPk} />
    </>
  );
}
