import { ActionSheetProvider } from '@expo/react-native-action-sheet';
import { AIChatFloatBall } from '@jgl/ai-qa-v2';
import { agreementStateAtom, store, tamaguiThemeNameAtom } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { JglThemeProvider } from '@jgl/ui-v4';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ThemeProvider as NavigationThemeProvider } from '@react-navigation/native';
import { captureEvent, wrap } from '@sentry/react-native';
import { AlertTriangle } from '@tamagui/lucide-icons';
import { QueryProvider } from '@yunti-private/net-query-hooks';
import { MemoryLogger } from '@yunti-private/rn-memory-logger';
import type { ErrorBoundaryProps } from 'expo-router';
import { Stack } from 'expo-router';
import { useAtom, useAtomValue } from 'jotai';
import { Suspense, useCallback, useEffect } from 'react';
import { Platform } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { RootSiblingParent } from 'react-native-root-siblings';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Provider } from 'react-redux';
import Reactotron from 'reactotron-react-native';
import {
  Button,
  FontLanguage,
  TamaguiProvider,
  Text,
  Theme,
  YStack,
} from 'tamagui';
import { PermissionComponents } from '@bookln/permission';
import { atomMap } from '../atom';
import { AuthGuideModal } from '../components/AuthGuideModal';
import { CustomBackButton } from '../components/CustomBackButton';
import { SimpleSuspenseFallback } from '../components/SimpleSuspenseFallback';
import { UpgradeModal } from '../components/UpgradeModal';
import { useNavigationTheme } from '../hooks/useBooklnThemes';
import { useInitAfterRender } from '../hooks/useInitAfterRender';
import { useInitBeforeRender } from '../hooks/useInitBeforeRender';
import { initBeforeLaunch } from '../init/initApp';
import { tamaguiConfig } from '../tamagui.config';
import { AiBookBottomToolbar } from '../components/aiBook/common/AiBookBottomToolbar';
import { useAiBookHandleIntentCommand } from '../hooks/aiBook/useAiBookHandleIntentCommand';

if (__DEV__ && Platform.OS === 'ios') {
  Reactotron.setAsyncStorageHandler(AsyncStorage)
    .configure({}) // controls connection & communication settings
    .useReactNative() // add all built-in react native plugins
    .connect(); // let's connect!
}

export const unstable_settings = {
  // Ensure that reloading on `/modal` keeps a back button present.
  initialRouteName: 'index',
};

// 在渲染UI之前的一些初始化工作
initBeforeLaunch();

export function RootLayout() {
  // 在渲染UI之前的一些初始化工作
  useInitBeforeRender();

  const splashScreenHidden = useAtomValue(atomMap.splashScreenHiddenAtom);
  const isNetContainerInitialized = useAtomValue(
    atomMap.isNetContainerInitializedAtom,
  );
  if (splashScreenHidden && isNetContainerInitialized) {
    // 开屏页已经隐藏，渲染 UI
    return (
      <SafeAreaProvider>
        <Suspense
          // agreementStateAtom 使用了异步 atom
          // 需要启用 Suspense ，正好尝试一下
          fallback={<SimpleSuspenseFallback />}
        >
          <Provider store={store}>
            <QueryProvider networking={container.net()}>
              <JglThemeProvider initialTheme={'light'}>
                <RootLayoutNav />
              </JglThemeProvider>
            </QueryProvider>
          </Provider>
        </Suspense>
      </SafeAreaProvider>
    );
  }

  return null;
}

function RootLayoutNav() {
  // 在渲染UI之后的一些初始化工作
  useInitAfterRender();
  const tamaguiThemeName = useAtomValue(tamaguiThemeNameAtom);

  return (
    <RootSiblingParent>
      <TamaguiProvider config={tamaguiConfig}>
        <FontLanguage body={'default'}>
          <ActionSheetProvider>
            <Theme name={tamaguiThemeName}>
              <Children />
            </Theme>
          </ActionSheetProvider>
        </FontLanguage>
      </TamaguiProvider>
    </RootSiblingParent>
  );
}

const Children = () => {
  const navigationTheme = useNavigationTheme();
  const [agreementState] = useAtom(agreementStateAtom);
  const aiBookInfo = useAtomValue(atomMap.aiBookInfo);
  const renderInputToolBarTopNode = useCallback(() => {
    return <AiBookBottomToolbar paddingHorizontal={8} />;
  }, []);
  const { handleAiBookIntentCommand } = useAiBookHandleIntentCommand();

  return (
    <NavigationThemeProvider value={navigationTheme}>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <AuthGuideModal />
        <UpgradeModal />
        <PermissionComponents.PermissionDescView />
        <Stack
          screenOptions={{
            headerLeft: () => {
              return <CustomBackButton />;
            },
            // 解决 Android 上因为 react-native-blur 导致的页面白屏问题
            // https://github.com/Kureev/react-native-blur/issues/595#issuecomment-**********
            animation: Platform.OS === 'android' ? 'none' : 'default',
          }}
        >
          <Stack.Screen
            name='index'
            options={{ headerShown: false, animation: 'none' }}
          />
          <Stack.Screen
            name='agreement'
            options={{
              headerShown: false,
              headerTitle: '',
              headerShadowVisible: false,

              // disagreed 说明已经进入首页
              // 使用 modal 展示，并且加上动画
              animation: agreementState === 'undetermined' ? 'none' : undefined,
              presentation:
                agreementState === 'undetermined' ? undefined : 'modal',
            }}
          />
          <Stack.Screen
            name='bookScanResult'
            options={{
              headerShown: false,
              animation: 'none',
              presentation: 'containedTransparentModal',
            }}
          />
          <Stack.Screen name='(weChatOAuth)' options={{ animation: 'none' }} />
          <Stack.Screen
            name='(tabs)'
            options={{ headerShown: false, animation: 'none' }}
          />
          <Stack.Screen
            name='(logInModal)'
            options={{ headerShown: false, presentation: 'modal' }}
          />
          <Stack.Screen
            name='(bindPhoneModal)'
            options={{ headerShown: false, presentation: 'modal' }}
          />
        </Stack>
        <AIChatFloatBall
          renderInputToolBarTopNode={renderInputToolBarTopNode}
          aiBookInfo={aiBookInfo}
          onIntentCommandPayloadNeedBeHandled={handleAiBookIntentCommand}
        />
      </GestureHandlerRootView>
    </NavigationThemeProvider>
  );
};

export default wrap(RootLayout);

export function ErrorBoundary({ error, retry }: ErrorBoundaryProps) {
  useEffect(() => {
    try {
      MemoryLogger.log(
        `App 崩溃 - ${typeof error === 'string' ? error : error.message}`,
      );
      captureEvent({
        message: `App 崩溃 - ${error.message}`,
        level: 'error',
        extra: {
          stack: error.stack,
        },
      });
    } catch (e) {
      MemoryLogger.log(`ErrorBoundary 捕获异常 - ${JSON.stringify(e)}`);
      console.error(e);
    }
  }, [error]);

  return (
    <YStack space='$4' className='flex-1 items-center justify-center bg-white'>
      <AlertTriangle size={48} color='#f97316' />

      <YStack space='$2' alignItems='center'>
        <Text style={{ fontSize: 20, fontWeight: '600', color: '#18181b' }}>
          页面加载失败
        </Text>
        <Text style={{ fontSize: 14, color: '#71717a', textAlign: 'center' }}>
          抱歉，页面遇到了一些问题。请尝试重新加载。
        </Text>
      </YStack>

      <Button
        className='bg-primary rounded-full text-white'
        theme={'active'}
        size={'$4'}
        onPress={retry}
      >
        重新加载
      </Button>
    </YStack>
  );
}
