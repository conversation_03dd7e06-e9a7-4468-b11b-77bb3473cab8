import {
  AppMessageType,
  type H5ActionPayload,
  H5MessageType,
  guluBallCountAtom,
  isPlatform,
  routerMap,
  useAppSelector,
  useBizRouter,
  useSafeAreaInsets,
  useWeChatIsInstalled,
} from '@jgl/biz-func';
import { JGLWebView, type JGLWebViewRef } from '@jgl/components';
import Icon from '@jgl/icon';
import {
  envVars,
  featureToggles,
  isAndroid,
  useRouterParams,
} from '@jgl/utils';
import { appendQuery } from '@yunti-private/utils-universal';
import { Image } from 'expo-image';
import { Stack, useNavigation } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useSetAtom } from 'jotai';
import { NativeWechatConstants, shareMiniProgram } from 'native-wechat';
import {
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import type { HapticFeedbackTypes } from 'react-native-haptic-feedback';
import {
  lockAsync,
  OrientationLock,
  unlockAsync,
} from 'expo-screen-orientation';
import type { WebViewMessageEvent, WebViewProps } from 'react-native-webview';
import { View } from 'tamagui';
import { hapticFeedback } from '../utils/HapticFeedback';
import { useUnmount } from 'ahooks';
import { isTablet } from 'react-native-device-info';

export default function WebViewScreen() {
  const params = useRouterParams();
  console.log('🚀 ~ WebViewScreen ~ params:', params);
  const {
    url,
    title,
    webviewNeedSafeBottom,
    disableScroll = 'false',
    supportLogin = 'true',
    supportScreenRotation = 'false',
  } = params;
  const userInfo = useAppSelector((state) => state.userInfo);
  console.log('🚀 ~ WebViewScreen ~ userInfo:', userInfo);
  const navigation = useNavigation();
  const safeAreaInsets = useSafeAreaInsets();
  const webViewRef = useRef<JGLWebViewRef>(null);
  const [capableBackToPreviousWebPage, setCapableBackToPreviousWebPage] =
    useState<boolean>(false);
  const setGuluBallCountAtom = useSetAtom(guluBallCountAtom);
  const bizRouter = useBizRouter();

  const isWeChatInstalled = useWeChatIsInstalled();

  useLayoutEffect(() => {
    if (supportScreenRotation === 'true') {
      unlockAsync();
    }
  }, [supportScreenRotation]);

  useUnmount(() => {
    if (supportScreenRotation === 'true') {
      const isAndroidAndTablet = isAndroid() && isTablet();
      if (!isAndroidAndTablet) {
        lockAsync(OrientationLock.PORTRAIT_UP);
      }
    }
  });

  /**
   * 点击了关闭按钮
   */
  const handleCloseButtonPressed = useCallback(() => {
    navigation.goBack();
  }, [navigation]);

  /**
   * 点击了返回到上一个网页按钮
   */
  const handleBackToPreviousWebPageButtonPressed = useCallback(() => {
    webViewRef.current?.goBack();
  }, []);

  /**
   * 渲染导航栏左侧按钮
   */
  const renderHeaderLeft = useCallback(
    (buttonProps: unknown) => {
      return (
        <View className='flex flex-row items-center justify-between'>
          <View
            onPress={handleCloseButtonPressed}
            className='h-[24px] w-[24px]'
          >
            <Image source={Icon.closeOutline} className='h-[24px] w-[24px]' />
          </View>
          {capableBackToPreviousWebPage ? (
            <View
              onPress={handleBackToPreviousWebPageButtonPressed}
              className='ml-[16px] h-[24px] w-[24px]'
            >
              <Image
                source={Icon.leftArrowOutline}
                className='h-[24px] w-[24px]'
              />
            </View>
          ) : null}
        </View>
      );
    },
    [
      capableBackToPreviousWebPage,
      handleBackToPreviousWebPageButtonPressed,
      handleCloseButtonPressed,
    ],
  );

  const paddingBottom = useMemo(() => {
    return webviewNeedSafeBottom === 'true' ? safeAreaInsets.bottom : 0;
  }, [safeAreaInsets.bottom, webviewNeedSafeBottom]);

  /**
   * 处理是否是 App 环境请求
   */
  const handleAppEnvironmentRequest = useCallback(() => {
    webViewRef.current?.postMessage({
      type: AppMessageType.IsAppEnvironmentResponse,
      data: {
        isApp: isPlatform({
          runtime: 'rn',
        }),
      },
    });
  }, []);

  /**
   * 处理查询 SafeAreaInsets
   */
  const handleQuerySafeAreaInsets = useCallback(() => {
    console.log(
      '🚀 ~ file: webView.tsx:460 ~ handleQuerySafeAreaInsets ~ safeAreaInsets:',
      safeAreaInsets,
    );
    webViewRef.current?.postMessage({
      type: AppMessageType.QuerySafeAreaInsetsResponse,
      data: {
        success: true,
        insets: safeAreaInsets,
      },
    });
  }, [safeAreaInsets]);

  /**
   * 处理跳转网页请求
   */
  const handlePushToWebPage = useCallback(
    (param: { title?: string; url: string }) => {
      bizRouter.push(routerMap.webView, {
        url: appendQuery(param.url, { _t: Date.now() }),
        title: param.title,
      });
      webViewRef.current?.postMessage({
        type: AppMessageType.PushToWebPageResponse,
        data: {
          success: true,
        },
      });
    },
    [bizRouter],
  );

  /**
   * 处理震动
   */
  const handleVibrate = useCallback(
    (param: { feedback: HapticFeedbackTypes }) => {
      const { feedback } = param;
      hapticFeedback(feedback);
      webViewRef.current?.postMessage({
        type: AppMessageType.VibrateResponse,
        data: {
          success: true,
        },
      });
    },
    [],
  );

  /**
   * 处理咕噜球数量更新
   */
  const handleUpdateGuluBallCount = useCallback(
    (param: { guluBallBalance: number }) => {
      const { guluBallBalance } = param;
      setGuluBallCountAtom(guluBallBalance);
      webViewRef.current?.postMessage({
        type: AppMessageType.VibrateResponse,
        data: {
          success: true,
        },
      });
    },
    [setGuluBallCountAtom],
  );

  /**
   * 处理查询微信是否已安装请求
   */
  const handleQueryWeChatIsInstalled = useCallback(async () => {
    try {
      webViewRef.current?.postMessage({
        type: AppMessageType.QueryWeChatIsInstalledResponse,
        data: {
          success: true,
          weChatInstalled: !!isWeChatInstalled,
        },
      });
    } catch (error) {
      webViewRef.current?.postMessage({
        type: AppMessageType.QueryWeChatIsInstalledResponse,
        data: {
          success: false,
          weChatInstalled: false,
        },
      });
    }
  }, [isWeChatInstalled]);

  /**
   * 处理分享挑战结果到微信
   */
  const handleShareChallengeResultToWeChat = useCallback(
    (param: {
      shareWebPageUrl: string;
      miniProgramPagePath: string;
      title?: string;
      description?: string;
      coverUrl?: string;
    }) => {
      if (isWeChatInstalled) {
        const { shareWebPageUrl, miniProgramPagePath, coverUrl } = param;
        try {
          // shareWebpage({
          //   webpageUrl: shareWebPageUrl,
          //   ...rest,
          //   scene: NativeWechatConstants.WXSceneSession,
          // });
          shareMiniProgram({
            title: param.title,
            userName: envVars.miniProgramRawID(),
            path: miniProgramPagePath,
            // @ts-ignore
            miniProgramType: NativeWechatConstants.WXMiniProgramTypeRelease,
            webpageUrl: shareWebPageUrl,
            coverUrl: coverUrl ?? Icon.shareDetailCover1x,
            withShareTicket: false,
            // @ts-ignore
            scene: 0,
          });
        } catch (error) {
          // TODO: cenfeng - 分享失败？
        }
      }
    },
    [isWeChatInstalled],
  );

  const handleNavigateToVip = useCallback(() => {
    bizRouter.push(routerMap.orderVip);
    webViewRef.current?.postMessage({
      type: AppMessageType.NavigateToVip,
    });
  }, [bizRouter]);

  const handleH5Message = useCallback(
    (event: WebViewMessageEvent) => {
      if (event.nativeEvent.data === '') {
        return;
      }
      try {
        const message: H5ActionPayload = JSON.parse(event.nativeEvent.data);
        switch (message.type) {
          /** H5 - 退出页面 */
          case H5MessageType.Exit: {
            navigation.goBack();
            break;
          }
          /** H5 - 查询 SafeAreaInsets */
          case H5MessageType.QuerySafeAreaInsetsRequest: {
            handleQuerySafeAreaInsets();
            break;
          }
          /** H5 - 是否是 App 环境请求 */
          case H5MessageType.IsAppEnvironmentRequest: {
            handleAppEnvironmentRequest();
            break;
          }
          /** H5 - 跳转原生网页 */
          case H5MessageType.PushToWebPageRequest: {
            handlePushToWebPage(message.data);
            break;
          }
          /** H5 - 震动 */
          case H5MessageType.VibrateRequest: {
            handleVibrate(message.data);
            break;
          }
          /** H5 - 更新咕噜球余额 */
          case H5MessageType.UpdateGuluBallCountRequest: {
            handleUpdateGuluBallCount(message.data);
            break;
          }
          /** H5 - 查询微信是否安装 */
          case H5MessageType.QueryWeChatIsInstalledRequest: {
            handleQueryWeChatIsInstalled();
            break;
          }
          /** H5 - 分享挑战结果到微信 */
          case H5MessageType.ShareChallengeResultToWeChatRequest: {
            handleShareChallengeResultToWeChat(message.data);
            break;
          }
          /** H5 - 跳转会员页 */
          case H5MessageType.NavigateToVip: {
            handleNavigateToVip();
            break;
          }
          default: {
            /* empty */
          }
        }
      } catch (error) {
        console.log('handleH5Message - error', error);
      }
    },
    [
      handleAppEnvironmentRequest,
      handleNavigateToVip,
      handlePushToWebPage,
      handleQuerySafeAreaInsets,
      handleQueryWeChatIsInstalled,
      handleShareChallengeResultToWeChat,
      handleUpdateGuluBallCount,
      handleVibrate,
      navigation,
    ],
  );

  return (
    <>
      <Stack.Screen
        options={{
          // TODO: aini - 动态获取网页本身的title，同时也支持传入
          headerTitle: title ?? '',
          headerLeft: renderHeaderLeft,
          headerShadowVisible: false,
        }}
      />
      <StatusBar style={'dark'} />
      <View className='flex-1'>
        <JGLWebView<WebViewProps>
          ref={webViewRef}
          scrollEnabled={disableScroll !== 'true'}
          overScrollMode={'never'}
          containerStyle={{
            paddingBottom,
            backgroundColor: 'white',
          }}
          webviewDebuggingEnabled={featureToggles.webviewDebuggingEnabled()}
          onMessage={handleH5Message}
          handleCapableBackToPreviousWebPage={setCapableBackToPreviousWebPage}
          source={{ uri: url ?? '' }}
          supportLogin={supportLogin === 'true'}
          vid={envVars.appVersionId()}
        />
      </View>
    </>
  );
}
