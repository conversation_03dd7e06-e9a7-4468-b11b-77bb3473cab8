import { container } from '@jgl/container';
import { JglButton, JglImage, JglText, JglTouchable, JglXStack, JglYStack } from '@jgl/ui-v4';
import { router, useRouterParams } from '@jgl/utils';
import { showToast } from '@yunti-private/jgl-ui';
import { Stack } from 'expo-router';
import { useCallback, useMemo, useState } from 'react';
import { bookhomedetail } from '../api/BookServiceApi';
import type { BookDTO } from '../api/dto';
import { getRealMpHost } from '../utils/mpUrlHostHelper';
import { routerMap } from '../utils/routerMap';

export default function BookScanResultScreen() {
  const { bookStr = '{}' } = useRouterParams<{ bookStr: string }>();

  const [isLoading, setIsLoading] = useState(false);

  /**
   * 书籍信息
   */
  const bookInfo = useMemo(() => {
    return JSON.parse(bookStr) as BookDTO;
  }, [bookStr]);

  const { name, thumbnails = '', id } = bookInfo;

  const onPress = useCallback(async () => {
    setIsLoading(true);
    const res = await container
      .net()
      .fetch(bookhomedetail({ bookId: id }))
      .finally(() => {
        setIsLoading(false);
      });
    const { data, success, msg } = res;
    if (success && data?.bookDTO) {
      const { idSign } = data.bookDTO;
      router.replace(routerMap.WebView, {
        url: `${getRealMpHost()}/book.htm?id=${id}&sign=${idSign}`,
      });
    } else {
      showToast({
        title: msg ?? '获取数据失败',
      });
    }
  }, [id]);

  return (
    <>
      <Stack.Screen options={{ headerShown: false, presentation: 'containedTransparentModal' }} />

      <JglXStack
        backgroundColor="rgba(0,0,0,0.6)"
        width={'100%'}
        height={'100%'}
        justifyContent="center"
        alignItems="center"
        jglClassName="relative"
      >
        <JglTouchable
          position="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          onPress={() => router.back()}
        />
        <JglYStack
          maxW={400}
          py={20}
          px={33}
          flex={1}
          mx={20}
          minW={310}
          width="100%"
          alignItems="center"
          borderRadius={6}
          backgroundColor="white"
        >
          <JglText fontSize={14} color="#1A2038" fontWeight="bold">
            扫描结果
          </JglText>
          <JglImage
            defaultImage={require('../assets/images/img_cover.png')}
            source={thumbnails}
            overflow="hidden"
            borderWidth={0.5}
            borderColor="#EEF2F7"
            borderRadius={6}
            mt={20}
            width={89}
            height={125}
          />
          <JglText maxLines={2} fontSize={16} mt={10} mx={30} color="#1A2038">
            {name}
          </JglText>
          <JglButton
            mt={35}
            borderRadius={21}
            onPress={onPress}
            color="#4E76FF"
            radius={'circle'}
            loading={isLoading}
            style={{ width: '100%' }}
          >
            <JglText fontSize={16} color="white">
              我知道了
            </JglText>
          </JglButton>
        </JglYStack>
      </JglXStack>
    </>
  );
}
