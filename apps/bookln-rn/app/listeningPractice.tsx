import { JglStateView, JglYStack } from '@jgl/ui-v4';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { ListeningPracticeBg } from '../components/listeningPracticeBg';
import { ListeningPracticeHeader } from '../components/listeningPracticeHeader';

/**
 * 听力练习
 */
export default function ListeningPracticeScreen() {
  return (
    <>
      <Stack.Screen options={{ headerShown: false }} />
      <StatusBar style="light" />
      <JglYStack height="100%" position="relative">
        <ListeningPracticeBg />
        <ListeningPracticeHeader />
        <JglStateView
          isEmpty
          backgroundColor="transparent"
          // emptyProps={{
          //   message: '暂无图书',
          // }}
        />
      </JglYStack>
    </>
  );
}
