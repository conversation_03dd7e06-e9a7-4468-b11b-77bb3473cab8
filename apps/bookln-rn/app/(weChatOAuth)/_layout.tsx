import { HeaderCloseIconButton } from '@jgl/biz-components';
import { Stack } from 'expo-router/stack';
import { RootSiblingParent } from 'react-native-root-siblings';

export default function WeChatOAuthLayout() {
  return (
    <RootSiblingParent>
      <Stack>
        <Stack.Screen
          name='appid2/wx3b6e8db8046678e9/oauth'
          options={{
            headerShadowVisible: false,
            headerLeft: () => {
              return <HeaderCloseIconButton />;
            },
          }}
        />
      </Stack>
    </RootSiblingParent>
  );
}
