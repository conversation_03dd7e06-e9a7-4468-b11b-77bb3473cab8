import { JglText, JglYStack } from '@jgl/ui-v4';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { Image } from 'tamagui';

export default function ScanResultScreen() {
  return (
    <>
      <Stack.Screen
        options={{
          headerTitle: '搜索图书结果',
          headerShadowVisible: false,
        }}
      />
      <StatusBar style='dark' />
      <JglYStack
        bg='white'
        flex={1}
        alignItems='center'
        justifyContent='center'
        space={16}
      >
        <Image source={require('../assets/images/ic_empty.png')} />
        <JglText fontSize={14} color='#8F8F8F'>
          抱歉，暂时未找到这本书的资源
        </JglText>
      </JglYStack>
    </>
  );
}
