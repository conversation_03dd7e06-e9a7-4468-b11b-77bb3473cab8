import FontAwesome from '@expo/vector-icons/FontAwesome';
import { AppLoginType, useShowWeChatFeatures } from '@jgl/biz-func';
import { Link, Stack } from 'expo-router';

import { JglGameButton, JglText, JglXStack, JglYStack } from '@jgl/ui-v4';
import { useRouterParams } from '@jgl/utils';
import { ActivityIndicator, Image, SafeAreaView } from 'react-native';
import { showToast } from '@yunti-private/jgl-ui';
import { useCallback } from 'react';
// eslint-disable-next-line import/no-named-as-default
import LinearGradient from 'react-native-linear-gradient';
import { Text, XStack, YStack } from 'tamagui';
import { LoginAgreement } from '../../components/LoginAgreement';
import { StatusBarForModal } from '../../components/StatusBarForModal';
import { useLogIn } from '../../hooks/useLogIn';

export default function LogInModal() {
  const { logInByWeChat, isLoggingIn, headerRight, headerLeft } = useLogIn();
  const search = useRouterParams();
  const { communicationCode, backPath } = search;
  const showWeChatFeatures = useShowWeChatFeatures();

  const isLoggingInByWeChat = isLoggingIn === AppLoginType.WeChat;

  const preLogInByWeChat = useCallback(() => {
    if (showWeChatFeatures) {
      logInByWeChat();
    } else {
      showToast({ title: '请安装微信后再试' });
    }
  }, [logInByWeChat, showWeChatFeatures]);

  return (
    <>
      <StatusBarForModal />
      <Stack.Screen options={{ headerRight, headerLeft }} />
      <YStack className='flex-center flex-1 bg-white'>
        <Image source={require('../../assets/images/ic_log_in_logo.png')} />
      </YStack>
      <JglYStack jglClassName='flex-1 items-center justify-between bg-white pb-4'>
        <YStack className='w-[80%] max-w-sm' space='$3' alignItems='stretch'>
          <JglGameButton
            onPress={preLogInByWeChat}
            size='large'
            mb={40}
            backgroundColor='#4E76FF'
            secondaryBgColor='#2E58E9'
            radius={10}
          >
            <JglXStack minH={44} jglClassName='flex-center' py={6}>
              {isLoggingInByWeChat ? (
                <ActivityIndicator color={'white'} />
              ) : (
                <JglXStack spaceX={8} alignItems='center'>
                  <FontAwesome name='wechat' size={20} color={'white'} />
                  <JglText fontSize={16} color={'white'} fontWeight='bold'>
                    微信登录
                  </JglText>
                </JglXStack>
              )}
            </JglXStack>
          </JglGameButton>

          <LoginAgreement />
        </YStack>
        <SafeAreaView>
          <YStack className='mb-4 items-center'>
            <XStack className='items-center justify-center'>
              <LinearGradient
                className='h-px w-12'
                start={{ x: 1, y: 0 }}
                end={{ x: 0, y: 0 }}
                colors={['#E2E2E2', '#E2E2E257']}
              />
              <JglText
                fontSize={14}
                color={'#A0A0A0'}
                marginLeft={8}
                marginRight={8}
              >
                其他方式登录
              </JglText>
              <LinearGradient
                className='h-px w-12'
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                colors={['#E2E2E2', '#E2E2E257']}
              />
            </XStack>
            <Link
              asChild
              href={{
                pathname: '/logInByPhone',
                params: {
                  ...(communicationCode ? { communicationCode } : {}),
                  ...(backPath ? { backPath } : {}),
                },
              }}
            >
              <YStack className='mt-3 items-center'>
                <Image
                  source={require('../../assets/images/ic_phone_circle.png')}
                  width={46}
                  height={46}
                />
                <Text className='text-xs text-[#343434]'>手机号登录</Text>
              </YStack>
            </Link>
          </YStack>
        </SafeAreaView>
      </JglYStack>
    </>
  );
}
