import { HeaderCloseIconButton } from '@jgl/biz-components';
import { Stack } from 'expo-router/stack';
import { RootSiblingParent } from 'react-native-root-siblings';
import { CustomBackButton } from '../../components/CustomBackButton';

export const unstable_settings = {
  initialRouteName: 'logInModal',
};

export default function Layout() {
  return (
    <RootSiblingParent>
      <Stack
        screenOptions={{
          headerLeft: () => {
            return <CustomBackButton />;
          },
        }}
      >
        <Stack.Screen
          name='logInModal'
          options={{
            headerShadowVisible: false,
            headerTitle: '',
            headerLeft: () => {
              return <HeaderCloseIconButton />;
            },
          }}
        />
        <Stack.Screen
          options={{
            headerShadowVisible: false,
            headerTitle: '',
          }}
          name='logInByPhone'
        />
      </Stack>
    </RootSiblingParent>
  );
}
