import { AppLoginType } from '@jgl/biz-func';
import { Stack } from 'expo-router';
import { useCallback } from 'react';
import {
  PhoneAndSmsCodeView,
  type PhoneAndSmsCodeViewBizProps,
} from '../../components/PhoneAndSmsCodeView';
import { useLogIn } from '../../hooks/useLogIn';
import { checkPhoneNumber, checkSmsCode } from '../../utils/checkUtil';

export default function LogInByPhoneScreen() {
  const viewProps = useLogInByPhone();
  return (
    <>
      <Stack.Screen options={{ headerTitle: '手机号登录' }} />
      <PhoneAndSmsCodeView {...viewProps} />
    </>
  );
}

const useLogInByPhone = (): PhoneAndSmsCodeViewBizProps => {
  const { logInBySmsCode, isLoggingIn } = useLogIn();
  const handlePressLogIn = useCallback(
    async (args: {
      phoneNumber: string | undefined;
      smsCode: string | undefined;
    }) => {
      const { phoneNumber, smsCode } = args;
      const checkedPhoneNumber = checkPhoneNumber(phoneNumber);
      if (checkedPhoneNumber) {
        const checkedSmsCode = checkSmsCode(smsCode);
        if (checkedSmsCode) {
          await logInBySmsCode({
            mobile: checkedPhoneNumber,
            code: checkedSmsCode,
          });
        }
      }
    },
    [logInBySmsCode],
  );

  return {
    confirmButtonTitle: '登录',
    isConfirmButtonLoading: isLoggingIn === AppLoginType.SmsCode,
    onPressConfirm: handlePressLogIn,
    showAgreementView: true,
  };
};
