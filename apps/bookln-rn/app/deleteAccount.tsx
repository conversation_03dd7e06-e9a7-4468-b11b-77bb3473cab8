import { withLoginLogicVersion } from '@bookln/bookln-biz';
import { agreementStateAtom } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { JglText, JglTouchable, JglYStack } from '@jgl/ui-v4';
import { USERINFO, copyToClipBoard, storage } from '@jgl/utils';
import { showToast } from '@yunti-private/jgl-ui';
import { Stack, router } from 'expo-router';
import { useSetAtom } from 'jotai';
import { useCallback, useState } from 'react';
import { Alert } from 'react-native';
import { Button, ScrollView, Spinner, Text, YStack } from 'tamagui';
import { userLogout } from '../api/UserServiceApi';
import { replaceToLoginPage } from '../utils/routerHelper';

export default function DeleteAccountScreen() {
  const {
    descriptions,
    handlePressDelete,
    isDeleting,
    onPressCustomerService,
  } = useDeleteAccountScreen();

  return (
    <>
      <Stack.Screen
        options={{ headerTitle: '注销账号', headerShadowVisible: false }}
      />

      <ScrollView className='flex-1 bg-white' contentInset={{ bottom: 40 }}>
        <YStack space={'$10'} className='p-5'>
          <YStack space>
            {descriptions.map((d) => {
              return (
                <Text key={d} className='text-lg font-bold'>
                  {d}
                </Text>
              );
            })}
          </YStack>
          <JglYStack>
            <JglTouchable
              paddingVertical={8}
              marginHorizontal={16}
              jglClassName='flex-center'
              onPress={onPressCustomerService}
            >
              <JglText color='#4E76FF' fontSize={16} marginVertical={8}>
                咨询客服（工作日8:00 ~ 20:00）
              </JglText>
            </JglTouchable>
            <Button
              className='bg-error rounded-full text-white'
              size={'$5'}
              onPress={handlePressDelete}
            >
              {isDeleting ? <Spinner color={'white'} /> : '注销账号'}
            </Button>
          </JglYStack>
        </YStack>
      </ScrollView>
    </>
  );
}

const useDeleteAccountScreen = () => {
  const descriptions = [
    '一旦注销账号，所有与账号相关的个人信息、学习记录、购买内容等将被永久删除，且无法恢复。',
    '请慎重考虑，因为此操作不可逆。',
    // '如需帮助，微信搜索公众号「鲸咕噜AI学习」。',
  ];

  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const setAgreementState = useSetAtom(agreementStateAtom);

  const handlePressDelete = useCallback(async () => {
    const { confirmed } = await showAlert();
    if (confirmed) {
      setIsDeleting(true);

      const response = await container.net().fetch(userLogout());
      const { success } = response;
      if (success) {
        Alert.alert(
          '注销成功',
          undefined,
          [
            {
              text: '确认',
              onPress: async () => {
                // 清除用户信息
                await storage.removeItem(USERINFO);

                // 协议同意状态初始化
                await setAgreementState('undetermined');
                // 回到同意协议页面
                withLoginLogicVersion(replaceToLoginPage, () => {
                  router.replace('/agreement');
                })();
              },
            },
          ],
          { cancelable: false },
        );
      }

      setIsDeleting(false);
    }
  }, [setAgreementState]);

  const onPressCustomerService = useCallback(() => {
    const weChatId = 'wxxiaoming777';
    Alert.alert(`请添加微信客服\n${weChatId}`, undefined, [
      { text: '取消' },
      {
        text: '复制并添加',
        onPress: async () => {
          copyToClipBoard(weChatId);
          showToast({ title: '复制成功' });
        },
      },
    ]);
  }, []);

  return {
    isDeleting,
    descriptions,
    handlePressDelete,
    onPressCustomerService,
  };
};

const showAlert = (): Promise<{ confirmed: boolean }> => {
  return new Promise((resolve) => {
    Alert.alert(
      '确认注销？',
      '是否确定要注销账号？此操作将永久删除你的账号信息并且不可恢复。',
      [
        {
          text: '保留账号',
          style: 'cancel',
          onPress: () => {
            resolve({ confirmed: false });
          },
        },
        {
          text: '继续注销',
          style: 'destructive',
          onPress: async () => {
            const result = await showFinalAlert();
            resolve(result);
          },
        },
      ],
      {
        onDismiss: () => {
          resolve({ confirmed: false });
        },
      },
    );
  });
};

const showFinalAlert = (): Promise<{ confirmed: boolean }> => {
  return new Promise((resolve) => {
    Alert.alert(
      '⚠️ 最后一步',
      '请再次确认，注销后你将立即失去所有数据和设置。',
      [
        {
          text: '再想想',
          style: 'cancel',
          onPress: () => {
            resolve({ confirmed: false });
          },
        },
        {
          text: '立即永久注销',
          style: 'destructive',
          onPress: () => {
            resolve({ confirmed: true });
          },
        },
      ],
      {
        onDismiss: () => {
          resolve({ confirmed: false });
        },
      },
    );
  });
};
