import { Stack } from 'expo-router';

import { JglButton } from '@jgl/ui-v4';
import { StatusBar } from 'expo-status-bar';
import { Image, Input, Text, View, XStack } from 'tamagui';
import { useAuthIdentity } from '../hooks/useAuthIdentity';

/**
 * 身份认证页面
 */
export default function AuthIdentityScreen() {
  const { isAuthenticating, disabled, handleAuthenticate, setName, setIdCard } = useAuthIdentity();

  return (
    <>
      <Stack.Screen options={{ headerTitle: '实名认证' }} />
      <StatusBar style="dark" />
      <View className="flex-1 bg-white px-6 py-4">
        <Text className="text-sm text-[#343434]">
          根据国家法律法规要求，在使用生成式人工智能服务前请先进行真实身份信息认证
        </Text>
        <Input
          onChangeText={setName}
          maxLength={10}
          className="border- mt-4 rounded-full border-[#F8F9FB] bg-[#F8F9FB] px-4 py-2 text-base"
          cursorColor="#4E76FF"
          placeholder="请填写姓名"
          returnKeyType="done"
        />
        <Input
          className="mt-4 rounded-full border-[#F8F9FB] bg-[#F8F9FB] px-4 py-2 text-base"
          clearTextOnFocus
          onChangeText={setIdCard}
          maxLength={18}
          keyboardType="numeric"
          clearButtonMode="while-editing"
          cursorColor="#4E76FF"
          placeholder="请填写身份证号"
          returnKeyType="go"
          onSubmitEditing={handleAuthenticate}
        />
        <JglButton
          disabled={disabled}
          mt={40}
          radius={'circle'}
          fontSize={14}
          textColor="white"
          loading={isAuthenticating}
          onPress={handleAuthenticate}
        >
          认证
        </JglButton>
        <XStack className="mt-3">
          <Image source={require('../assets/images/ic_tip.png')} width={16} height={16} />
          <Text className="ml-1 text-xs text-[#A0A0A0]">不会对信息做任何采集或保留请放心使用</Text>
        </XStack>
      </View>
    </>
  );
}
