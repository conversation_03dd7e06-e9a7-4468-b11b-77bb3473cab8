import { JglGridView, JglStateView, JglTouchable } from '@jgl/ui-v4';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useCallback } from 'react';
import { Image } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ScrollView } from 'tamagui';
import { useBookShelf } from '../hooks/useBookShelf';
import { BookShelfItem } from '../components/BookShelfItem';

/**
 * 书架
 */
export default function BookShelfScreen() {
  const { onPressScan, bookShelfData } = useBookShelf();

  const { bottom } = useSafeAreaInsets();

  /**
   * 头部右侧
   */
  const headerRight = useCallback(() => {
    return (
      <JglTouchable onPress={onPressScan}>
        <Image
          source={require('../assets/images/ic_scan_dark.png')}
          width={24}
          height={24}
        />
      </JglTouchable>
    );
  }, [onPressScan]);

  return (
    <>
      <Stack.Screen
        options={{
          headerTitle: '书架',
          headerRight,
          headerShadowVisible: false,
        }}
      />
      <StatusBar style='dark' />
      <JglStateView
        isEmpty={bookShelfData.length === 0}
        jglClassName='bg-white'
        emptyProps={{
          message: '暂无图书',
        }}
      >
        <ScrollView>
          <JglGridView
            minColumns={3}
            minItemWidth={100}
            horizontalSpace={32}
            pb={bottom + 20}
            gap={16}
            jglClassName='self-center'
          >
            {bookShelfData.map((item) => (
              <BookShelfItem key={item.id} book={item} />
            ))}
          </JglGridView>
        </ScrollView>
      </JglStateView>
    </>
  );
}
