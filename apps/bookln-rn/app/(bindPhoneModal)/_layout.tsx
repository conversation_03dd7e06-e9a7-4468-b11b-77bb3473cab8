import { HeaderCloseIconButton } from '@jgl/biz-components';
import { Stack } from 'expo-router/stack';
import { RootSiblingParent } from 'react-native-root-siblings';

export const unstable_settings = {
  initialRouteName: 'bindPhoneModal',
};

export default function Layout() {
  return (
    <RootSiblingParent>
      <Stack>
        <Stack.Screen
          name='bindPhoneModal'
          options={{
            headerShadowVisible: false,
            headerLeft: () => {
              return <HeaderCloseIconButton />;
            },
          }}
        />
      </Stack>
    </RootSiblingParent>
  );
}
