import { HeaderCloseIconButton } from '@jgl/biz-components';
import { container } from '@jgl/container';
import { AccountEmitterHelper, type AccountEmitterResult, showToast } from '@jgl/utils';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { useCallback, useState } from 'react';

import { JglText, JglTouchable } from '@jgl/ui-v4';
import { bindPhone } from '../../api/UserServiceApi';
import {
  PhoneAndSmsCodeView,
  type PhoneAndSmsCodeViewBizProps,
} from '../../components/PhoneAndSmsCodeView';
import { StatusBarForModal } from '../../components/StatusBarForModal';
import { useRefreshUserInfo } from '../../hooks/useRefreshUserInfo';
import { checkPhoneNumber, checkSmsCode } from '../../utils/checkUtil';

export default function BindPhoneScreen() {
  const { headerRight, headerLeft, ...bindPhoneProps } = useBindPhone();

  return (
    <>
      <StatusBarForModal />
      <Stack.Screen options={{ headerTitle: '手机号绑定', headerRight, headerLeft }} />
      <PhoneAndSmsCodeView {...bindPhoneProps} />
    </>
  );
}

const useBindPhone = (): PhoneAndSmsCodeViewBizProps => {
  const { refreshUserInfo } = useRefreshUserInfo();
  const [isBinding, setIsBinding] = useState<boolean>(false);
  const params = useLocalSearchParams();
  const { communicationCode, canJump = 'false' } = params;

  /**
   * 一次性通知，通知上一个页面绑定成功或者跳过，目前在{@link orderVip}中使用
   * @see orderVip
   */
  const emit = useCallback(
    (result: AccountEmitterResult) => {
      if (communicationCode && typeof communicationCode === 'string') {
        AccountEmitterHelper.getInstance().emit(communicationCode, result);
      }
    },
    [communicationCode],
  );

  const handlePressBind = useCallback(
    async (args: {
      phoneNumber: string | undefined;
      smsCode: string | undefined;
    }) => {
      const { phoneNumber, smsCode } = args;
      const checkedPhoneNumber = checkPhoneNumber(phoneNumber);
      if (checkedPhoneNumber) {
        const checkedSmsCode = checkSmsCode(smsCode);
        if (checkedSmsCode) {
          setIsBinding(true);

          const request = bindPhone({
            mobile: checkedPhoneNumber,
            validCode: checkedSmsCode,
          });
          const response = await container.net().fetch(request);
          const { success } = response;
          if (success) {
            await refreshUserInfo();
            showToast({ title: '绑定手机号成功，可使用手机号登录当前账号' });
            emit({ state: 'success' });
            router.push('..');
          }

          setIsBinding(false);
        }
      }
    },
    [emit, refreshUserInfo],
  );

  const headerRight = useCallback(() => {
    if (canJump === 'true') {
      return (
        <JglTouchable
          onPress={() => {
            emit({ state: 'skip' });
            router.push('..');
          }}
          jglClassName="flex-center"
        >
          <JglText fontSize={14} color={'#A0A0A0'}>
            跳过
          </JglText>
        </JglTouchable>
      );
    }
    return null;
  }, [canJump, emit]);

  const handleClose = useCallback(() => {
    emit({ state: 'cancel' });
    router.back();
  }, [emit]);

  const headerLeft = useCallback(() => {
    return <HeaderCloseIconButton onPress={handleClose} />;
  }, [handleClose]);

  return {
    confirmButtonTitle: '绑定手机号',
    isConfirmButtonLoading: isBinding,
    onPressConfirm: handlePressBind,
    showAgreementView: false,
    headerRight,
    headerLeft,
  };
};
