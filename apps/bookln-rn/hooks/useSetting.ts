import { BooklnLoginHooks, withLoginLogicVersion } from '@bookln/bookln-biz';
import { useActionSheet } from '@expo/react-native-action-sheet';
import {
  externalUrls,
  storageKeys,
  useBizRouter,
  useIsUuidUser,
  routerMap as bizRouterMap,
} from '@jgl/biz-func';
import { envVars, getAppVid, router, storage } from '@jgl/utils';
import { GracefulExit } from '@sleiv/react-native-graceful-exit';
import { showToast } from '@yunti-private/jgl-ui';
import { useAtom } from 'jotai';
import { useCallback, useState } from 'react';
import { Alert, Linking, Platform, type ViewStyle } from 'react-native';
import { messageNotificationSwitchAtom } from '../atom';
import { openUrl } from '../utils/UrlJumpHelper';
import { docNames, showChildrenPrivacySwitch } from '../utils/constants';
import { routerMap } from '../utils/routerMap';
import { useCheckAppUpgrade } from './useCheckAppUpgrade';
import { useLogOut } from './useLogOut';
import { useLogInByUuidInternal } from './useLogIn';

export const ItemType = {
  // 自定义, section-row, 随便取, 不一样就行, 分辨onPress
  personalData: '1-0',
  accountSafe: '1-1',

  aboutBookln: '2-1',
  agreement: '2-2',
  privacy: '2-3',
  childrenPrivacy: '2-3-1',
  revokeConsentAgreement: '2-3-2', // 撤销同意协议

  clearCache: '2-4',
  iOSAppVersion: '2-5',
  androidPlayerChange: '2-6',
  personalInfoList: '2-7',
  personalInfoShareList: '2-8',
  messageNotification: '2-9',
  recommend: '2-10',

  checkVersion: '3-0',
  debug: '3-1',

  deleteAccount: '4-2',
  logout: '5-0',
};

export interface Item {
  name: string;
  type: string;
  value?: string;
  disablePress?: boolean;
  valueStyle?: ViewStyle;
}

export interface SectionData {
  data: Item[];
}

const { useLogout } = BooklnLoginHooks;

/**
 * 设置
 */
export const useSetting = () => {
  const { showActionSheetWithOptions } = useActionSheet();

  const { logOut: logoutOld } = useLogOut();
  const { logout } = useLogout();

  const [messageNotificationSwitch, setMessageNotificationSwitch] = useAtom(
    messageNotificationSwitchAtom,
  );

  const isUuidUser = useIsUuidUser();

  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const { checkVersion } = useCheckAppUpgrade({ isActive: false });

  const bizRouter = useBizRouter();

  const { logInByUuidInternal: logInByUuid } = useLogInByUuidInternal();

  /**
   * 个人信息
   */
  const sectionOneData = useCallback((): SectionData => {
    const section1: SectionData = {
      data: [
        {
          name: '个人信息',
          type: ItemType.personalData,
        },
        {
          name: '账号与安全',
          type: ItemType.accountSafe,
        },
      ],
    };
    return section1;
  }, []);

  /**
   * 协议
   */
  const sectionTwoData = useCallback((): SectionData => {
    const items: Item[] = [
      {
        name: '个性化内容推荐',
        type: ItemType.recommend,
      },
      {
        name: '关于书链',
        type: ItemType.aboutBookln,
      },
    ];

    return {
      data: items,
    };
  }, []);

  /**
   * 协议
   */
  const sectionAgreementData = useCallback((): SectionData => {
    const items: Item[] = [
      {
        name: docNames.userAgreement,
        type: ItemType.agreement,
      },
      {
        name: docNames.privacy,
        type: ItemType.privacy,
      },
      ...(showChildrenPrivacySwitch
        ? [
            {
              name: docNames.childrenPrivacy,
              type: ItemType.childrenPrivacy,
            },
          ]
        : []),
      {
        name: '个人信息清单和权限说明',
        type: ItemType.personalInfoList,
      },
      {
        name: '第三方共享个人信息清单',
        type: ItemType.personalInfoShareList,
      },
      {
        name: '撤销同意协议',
        type: ItemType.revokeConsentAgreement,
      },
    ];

    return {
      data: items,
    };
  }, []);

  /**
   * 版本号
   */
  const appVersionData = useCallback((): SectionData => {
    const items: Item[] = [];

    const extraInfo = getAppVid() ? `(${getAppVid()})` : '';

    if (Platform.OS === 'android') {
      items.push({
        name: '检查更新',
        value: `当前版本号 ${envVars.appVersion()}${extraInfo}`,
        type: ItemType.checkVersion,
      });
    }

    if (Platform.OS === 'ios') {
      items.push({
        name: '版本号',
        value: `${envVars.appVersion()}${extraInfo}`,
        type: ItemType.iOSAppVersion,
      });
    }

    return { data: items };
  }, []);

  const sectionMessage = useCallback((): SectionData => {
    if (Platform.OS === 'android') {
      return {
        data: [
          {
            name: '消息通知',
            type: ItemType.messageNotification,
          },
        ],
      };
    } else {
      return {
        data: [],
      };
    }
  }, []);

  /**
   * 退出
   */
  const sectionFiveData = useCallback((): SectionData => {
    return {
      data: [{ name: isUuidUser ? '登录' : '退出登录', type: ItemType.logout }],
    };
  }, [isUuidUser]);

  /**
   * 构建数据
   */
  const buildData = useCallback((): SectionData[] => {
    const section1 = sectionOneData();
    const section2 = sectionAgreementData();
    const section3 = sectionTwoData();
    const section4 = sectionMessage();
    const section7 = appVersionData();
    const logoutData = sectionFiveData();
    return [section1, section2, section3, section4, section7, logoutData];
  }, [
    sectionOneData,
    sectionAgreementData,
    sectionTwoData,
    sectionMessage,
    appVersionData,
    sectionFiveData,
  ]);

  /**
   * 退出登录
   */
  const handlePressLogout = useCallback(() => {
    const textCancel = '取消';
    const textLogOut = '退出登录';
    const options = [textLogOut, textCancel];
    const indexCancel = options.indexOf(textCancel);
    const indexLogOut = options.indexOf(textLogOut);
    showActionSheetWithOptions(
      {
        options,
        cancelButtonIndex: indexCancel,
      },
      async (selectedIndex) => {
        switch (selectedIndex) {
          case indexLogOut: {
            setIsLoggingOut(true);
            await withLoginLogicVersion(logout, logoutOld)().finally(() => {
              setIsLoggingOut(false);
            });
            break;
          }
          case indexCancel: {
            break;
          }
        }
      },
    );
  }, [logout, logoutOld, showActionSheetWithOptions]);

  const linkToAppStore = useCallback(async (appstoreId: string) => {
    const url = `https://apps.apple.com/cn/app/id${appstoreId}`;
    if (await Linking.canOpenURL(url)) {
      Linking.openURL(url);
    } else {
      showToast({ title: '很抱歉，AppStore没有找到该应用~' });
    }
  }, []);

  const onPressItem = useCallback(
    async (type: string) => {
      switch (type) {
        case ItemType.logout: {
          withLoginLogicVersion(handlePressLogout, () => {
            if (isUuidUser) {
              bizRouter.push(bizRouterMap.logInModal, { backPath: '..' });
            } else {
              handlePressLogout();
            }
          })();
          break;
        }
        case ItemType.iOSAppVersion: {
          linkToAppStore(envVars.iosAppstoreId());
          break;
        }
        case ItemType.personalData: {
          withLoginLogicVersion(
            () => {
              router.push(routerMap.PersonalInfo);
            },
            () => {
              if (isUuidUser) {
                bizRouter.push(bizRouterMap.logInModal, { backPath: '..' });
              } else {
                router.push(routerMap.PersonalInfo);
              }
            },
          )();
          break;
        }
        case ItemType.accountSafe: {
          withLoginLogicVersion(
            () => {
              router.push(routerMap.AccountAndSafety);
            },
            () => {
              if (isUuidUser) {
                bizRouter.push(bizRouterMap.logInModal, { backPath: '..' });
              } else {
                router.push(routerMap.PersonalInfo);
              }
            },
          )();
          router.push(routerMap.AccountAndSafety);
          break;
        }
        case ItemType.aboutBookln: {
          router.push(routerMap.AboutBookln);
          break;
        }
        case ItemType.recommend: {
          router.push(routerMap.RecommendManager);
          break;
        }
        case ItemType.agreement: {
          openUrl(externalUrls.agreement.url);
          break;
        }
        case ItemType.privacy: {
          openUrl(externalUrls.privacy.url);
          break;
        }
        case ItemType.childrenPrivacy: {
          openUrl(externalUrls.privacyForChildren.url);
          break;
        }
        case ItemType.personalInfoShareList: {
          openUrl(externalUrls.sharedInfoList.url);
          break;
        }
        case ItemType.personalInfoList: {
          openUrl(externalUrls.personalInfoList.url);
          break;
        }
        case ItemType.revokeConsentAgreement: {
          Alert.alert(
            '确认撤销同意协议？',
            undefined,
            [
              {
                text: '取消',
                style: 'cancel',
              },
              {
                text: '撤销同意协议',
                onPress: async () => {
                  await withLoginLogicVersion(logout, () => {
                    logInByUuid({ from: 'agreement' });
                  })();
                  await storage.setItem(storageKeys.agreementState, 'undetermined', { env: false });
                  Alert.alert(
                    '已撤销同意协议，请手动杀掉APP重新进入',
                    undefined,
                    [
                      {
                        text: '我知道了',
                        onPress: () => {
                          GracefulExit.exit().catch((e) => {
                            console.error(e);
                          });
                        },
                        style: 'default',
                      },
                    ],
                    { cancelable: false },
                  );
                },
                style: 'default',
              },
            ],
            {
              cancelable: false,
            },
          );
          break;
        }
        case ItemType.checkVersion: {
          if (Platform.OS === 'android') {
            checkVersion();
          } else {
            const url = `https://apps.apple.com/cn/app/id${envVars.iosAppstoreId()}`;
            if (await Linking.canOpenURL(url)) {
              Linking.openURL(url);
            } else {
              showToast({ title: '很抱歉，AppStore没有找到该应用~' });
            }
          }
          break;
        }
      }
    },
    [bizRouter, checkVersion, handlePressLogout, isUuidUser, linkToAppStore, logInByUuid, logout],
  );

  return {
    dataArray: buildData(),
    onPressItem,
    isLoggingOut,
    messageNotificationSwitch,
    setMessageNotificationSwitch,
  };
};
