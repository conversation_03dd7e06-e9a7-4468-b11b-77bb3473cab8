import { withLogin } from '@bookln/bookln-biz';
import { PermissionEnum, PermissionHooks, PermissionPurposeScene } from '@bookln/permission';
import { useAgreementCheck } from '@jgl/biz-func';
import { envVars, featureToggles, router } from '@jgl/utils';
import { useCallback, useMemo, useState } from 'react';
import { ScanType } from '../types';
import { routerMap } from '../utils/routerMap';

/**
 * 功能入口类型
 */
export type FeatureEntryType = {
  id: string;
  title: string;
  image: number;
  onPress: () => void;
  backgroundColor: string[];
};

/**
 * 书链内容功能入口
 */
export const useBooklnContentFeatureEntry = () => {
  const { checkAndRequestPermission } = PermissionHooks.usePermission();
  const { withAgreementCheck } = useAgreementCheck();

  const [learnToolsModalVisible, setLearnToolsModalVisible] = useState(false);

  /**
   * AI讲解
   */
  const onPressAi = useCallback(() => {
    router.push(routerMap.AiExplain);
  }, []);

  /**
   * 扫码搜书
   */
  const onPressSearchBook = useCallback(() => {
    checkAndRequestPermission({
      permission: PermissionEnum.Camera,
      scene: PermissionPurposeScene.ScanCode,
    }).then((result) => {
      if (result) {
        router.push(routerMap.ScanSearch, { scanType: ScanType.ScanParse });
      }
    });
  }, [checkAndRequestPermission]);

  /**
   * 听力练习
   */
  const onPressPractice = useCallback(() => {
    router.push(routerMap.ListeningPractice);
  }, []);

  /**
   * 学习工具
   */
  const onPressLearnTool = useCallback(() => {
    setLearnToolsModalVisible(true);
  }, []);

  /**
   * 功能入口列表
   */
  const featureEntryList: FeatureEntryType[] = useMemo(() => {
    const list: FeatureEntryType[] = [];
    list.push({
      id: '1',
      title: 'AI讲解',
      image: require('../assets/images/ic_ai.png'),
      onPress:
        envVars.loginLogicVersion() === 'new'
          ? withAgreementCheck(onPressAi)
          : withAgreementCheck(withLogin(onPressAi)),
      backgroundColor: ['#3075F9', '#5CB0F7'],
    });
    list.push({
      id: '2',
      title: '扫码搜书',
      image: require('../assets/images/ic_search_book.png'),
      onPress:
        envVars.loginLogicVersion() === 'new'
          ? withAgreementCheck(onPressSearchBook)
          : withAgreementCheck(withLogin(onPressSearchBook)),
      backgroundColor: ['#6761FF', '#9799FC'],
    });
    if (featureToggles.booklnHomeFeatureListeningExercisesVisible()) {
      list.push({
        id: '3',
        title: '听力练习',
        image: require('../assets/images/ic_practice.png'),
        onPress:
          envVars.loginLogicVersion() === 'new'
            ? withAgreementCheck(onPressPractice)
            : withAgreementCheck(withLogin(onPressPractice)),
        backgroundColor: ['#FF8000', '#FFB41F'],
      });
    }
    list.push({
      id: '4',
      title: '学习工具',
      image: require('../assets/images/ic_learn_tool.png'),
      onPress:
        envVars.loginLogicVersion() === 'new'
          ? withAgreementCheck(onPressLearnTool)
          : withAgreementCheck(withLogin(onPressLearnTool)),
      backgroundColor: ['#00B5B1', '#0DC0CC'],
    });
    return list;
  }, [withAgreementCheck, onPressAi, onPressSearchBook, onPressLearnTool, onPressPractice]);

  /**
   * 学习工具模态框消失
   */
  const onLearnToolsModalDismiss = useCallback(() => {
    setLearnToolsModalVisible(false);
  }, []);

  return { featureEntryList, learnToolsModalVisible, onLearnToolsModalDismiss };
};
