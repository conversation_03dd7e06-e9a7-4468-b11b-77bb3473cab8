import { feedbackGetContent } from '@jgl/biz-func';
import { useRouterParams } from '@jgl/utils';
import { useApiQuery } from '@yunti-private/net-query-hooks';
import { useMemo } from 'react';

/**
 * 常见问题
 */
export const useCommonProblem = () => {
  const { id = '', title } = useRouterParams();
  const { data, retry, isLoading, error } = useApiQuery(feedbackGetContent, { id });

  const html = useMemo(() => {
    const initialScale = `<meta name='viewport' content='initial-scale=1.0'/>`;
    return `${initialScale}${data}`;
  }, [data]);

  return {
    data: html,
    retry,
    isLoading,
    title,
    error,
  };
};
