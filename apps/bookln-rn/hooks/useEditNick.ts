import {
  setUserInfoStorage,
  store,
  updateUserInfo as updateUserInfoAction,
  useAppSelector,
} from '@jgl/biz-func';
import { container } from '@jgl/container';
import { router } from '@jgl/utils';
import { showToast } from '@yunti-private/jgl-ui';
import { useCallback, useState } from 'react';
import { updateUserInfo } from '../api/UserServiceApi';

export const useEditNick = () => {
  const userInfo = useAppSelector((state) => state.userInfo);
  const [nick, setNick] = useState(userInfo.nick);
  const [isLoading, setIsLoading] = useState(false);

  const onSubmitEditing = useCallback(async () => {
    if (!nick) {
      showToast({ title: '昵称不能为空' });
      return;
    }
    if (nick === userInfo.nick) {
      showToast({ title: '昵称不能与原昵称相同' });
      return;
    }
    setIsLoading(true);
    const response = await container
      .net()
      .fetch(updateUserInfo({ nick }))
      .finally(() => {
        setIsLoading(false);
      });
    const { success, msg } = response;
    if (success) {
      showToast({ title: '修改成功' });
      store.dispatch(updateUserInfoAction({ ...userInfo, nick }));
      setUserInfoStorage({ ...userInfo, nick });
      router.back();
    } else {
      showToast({ title: msg || '请求失败' });
    }
  }, [nick, userInfo]);

  return { nick, setNick, onSubmitEditing, isLoading };
};
