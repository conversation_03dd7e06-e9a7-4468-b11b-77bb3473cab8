import { isRefreshingUserInfoAtom, updateUserInfo, useAppDispatch, UserInfo } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { storage, USERINFO } from '@jgl/utils';
import { usersApiGetLoginInfo, UsersApiGetLoginInfoDTO } from '@yunti-private/api-xingdeng-boot';
import { useSetAtom } from 'jotai';
import { useCallback } from 'react';

export const useRefreshUserInfo = () => {
  const setIsRefreshing = useSetAtom(isRefreshingUserInfoAtom);
  const dispatch = useAppDispatch();
  const refreshUserInfo = useCallback((): Promise<boolean | undefined> | undefined => {
    //TODO: 后端暂时没有获取用户信息接口，先抛个异常
    throw new Error('后端没有获取用户信息接口');
    // setIsRefreshing(true);

    // const { success, data: user } = await container
    //   .net()
    //   // @ts-ignore
    //   .fetch(usersApiGetLoginInfo());
    // setIsRefreshing(false);
    // if (success && user) {
    //   await storage.setItem(USERINFO, user);
    //   dispatch(updateUserInfo(user));
    //   return true;
    // }

    // return false;
  }, []);

  /**
   * 刷新本地用户信息，包含redux与storage
   * @param {UserInfo}- newUserInfo 已变更的用户信息
   */
  const refreshLocalUserInfo = useCallback(
    async (newUserInfo: UserInfo) => {
      await storage.setItem(USERINFO, newUserInfo);
      dispatch(updateUserInfo(newUserInfo));
    },
    [dispatch],
  );

  return { refreshUserInfo, refreshLocalUserInfo };
};
