import { storage } from '@jgl/utils';
import { MD5 } from 'crypto-js';
import { useAtomValue, useSetAtom } from 'jotai';
import { useCallback, useMemo, useRef, useState } from 'react';
import { NativeModules } from 'react-native';
import { getApplicationName } from 'react-native-device-info';
import RNFS from 'react-native-fs';
import { atomMap } from '../atom';
import { DownloadStatus } from '../types';
import {
  STATUS_FAILED,
  STATUS_NOT_DOWNLOAD,
  STATUS_SUCCESSFUL,
} from '../upgrade/Const';
import { getUpgradeTipKey } from '../upgrade/UpgradeUtils';
import { native } from '../utils/Native';

export const useUpgradeModal = () => {
  const {
    isShowUpgradeModal,
    appVersionDTO,
    sysCheck,
    market,
    downloadStatus: defaultDownloadStatus,
  } = useAtomValue(atomMap.checkedAppVersionResultAtom);
  const updateAppVersion = useSetAtom(atomMap.checkedAppVersionResultAtom);
  /** 检查更新的时候可能有初始值 */
  const [downloadStatus, setDownloadStatus] = useState<DownloadStatus>(
    defaultDownloadStatus || {
      status: STATUS_NOT_DOWNLOAD,
      progress: 0,
      total: 0,
      localUri: '',
    },
  );

  const queryApkDownloadInterval = useRef<any>();

  // 1: 不强制不卸载更新 3:强制不卸载更新
  const isForceInstall = useMemo(
    (): boolean => appVersionDTO?.strategy === 3,
    [appVersionDTO],
  );

  const onNeverRemindPress = useCallback(() => {
    const isNeverRemind = sysCheck && !isForceInstall;
    if (isNeverRemind && appVersionDTO) {
      storage.setItem(getUpgradeTipKey(appVersionDTO.name ?? ''), 'false');
    }
  }, [appVersionDTO, isForceInstall, sysCheck]);

  const onPressClose = useCallback(() => {
    onNeverRemindPress();
    updateAppVersion({ isShowUpgradeModal: false });
  }, [onNeverRemindPress, updateAppVersion]);

  const queryApkDownloadStatusAndReportProgress = useCallback(() => {
    NativeModules.BooklnNativeHelper.queryApkDownloadStatusAndReportProgress()
      .then((result: DownloadStatus) => {
        const { status } = result;
        switch (status) {
          case STATUS_SUCCESSFUL:
          case STATUS_FAILED:
            queryApkDownloadInterval &&
              clearInterval(queryApkDownloadInterval.current);
            break;
          default:
            if (status !== STATUS_SUCCESSFUL) {
              queryApkDownloadInterval &&
                clearInterval(queryApkDownloadInterval.current);
              queryApkDownloadInterval.current = setInterval(() => {
                queryApkDownloadStatusAndReportProgress();
              }, 300);
            }
            break;
        }
        setDownloadStatus(result);
      })
      .catch((e: unknown) => {
        // ignore
      });
  }, []);

  const downloadAndInstallApk = useCallback((data: any) => {
    const fileName = MD5(`jgl_${data.name}`);
    NativeModules.BooklnNativeHelper.downloadAndInstallApk(
      {
        url: data.url,
        title: `正在下载${getApplicationName()}${data.name}`,
        path: `${RNFS.ExternalCachesDirectoryPath}/${fileName}`,
      },
      (status: number) => {
        if (status === STATUS_SUCCESSFUL && !isForceInstall) {
          updateAppVersion({ isShowUpgradeModal: false });
        }
      },
    );
  }, []);

  const onUpdatePress = useCallback(() => {
    const { status: newestStatus } = downloadStatus;
    switch (newestStatus) {
      case STATUS_NOT_DOWNLOAD:
      case STATUS_FAILED: {
        downloadAndInstallApk(appVersionDTO);
        queryApkDownloadStatusAndReportProgress();
        break;
      }
      case STATUS_SUCCESSFUL: {
        downloadAndInstallApk(appVersionDTO);
        queryApkDownloadStatusAndReportProgress();
        break;
      }
      default: {
        updateAppVersion({ isShowUpgradeModal: false });
        break;
      }
    }
  }, [
    appVersionDTO,
    downloadAndInstallApk,
    downloadStatus,
    isForceInstall,
    queryApkDownloadStatusAndReportProgress,
    updateAppVersion,
  ]);

  const onPressGotoMarket = useCallback(() => {
    if (market && market.name) {
      native.openAndroidAppStore(market.name);
    }
  }, [market]);

  return {
    market,
    isForceInstall,
    isShowUpgradeModal,
    appVersionDTO,
    downloadStatus,
    onUpdatePress,
    onPressGotoMarket,
    onPressClose,
  };
};
