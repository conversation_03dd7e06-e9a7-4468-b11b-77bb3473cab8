import { store } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { showToast } from '@jgl/utils';
import { router } from 'expo-router';
import { isEmpty } from 'lodash';
import { useCallback, useMemo, useState } from 'react';
import { Alert } from 'react-native';
import { identityVerification } from '../api/UserServiceApi';
import { useRefreshUserInfo } from './useRefreshUserInfo';

/**
 * 身份认证
 */
export const useAuthIdentity = () => {
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  const [name, setName] = useState('');

  const [idCard, setIdCard] = useState('');

  const { refreshLocalUserInfo } = useRefreshUserInfo();

  const handleAuthenticate = useCallback(async () => {
    try {
      setIsAuthenticating(true);
      const res = await container.net().fetch(
        identityVerification({
          realName: name,
          idCard,
        }),
      );
      const { success, data, msg } = res;

      if (success && data) {
        const oldUserInfo = store.getState().userInfo;
        const { extend } = oldUserInfo;
        const extendObj = JSON.parse(extend || '{}');
        extendObj.authStatus = 1;
        const newExtend = JSON.stringify(extendObj);
        refreshLocalUserInfo({ ...oldUserInfo, extend: newExtend });
        Alert.alert('认证成功', '', [
          {
            text: '确定',
            onPress: () => {
              router.push('..');
            },
          },
        ]);
      } else {
        showToast({
          title: msg ?? '认证失败',
        });
      }
    } catch (error) {
    } finally {
      setIsAuthenticating(false);
    }
  }, [idCard, name, refreshLocalUserInfo]);

  // 数据是否合法
  const isDataValid = useMemo(() => {
    // 身份证号码格式校验
    const reg = /^(?:\d{15}|\d{17}[\dX])$/;
    const validate = reg.test(idCard);
    return !isEmpty(name) && validate;
  }, [name, idCard]);

  const disabled = useMemo(() => isAuthenticating || !isDataValid, [isAuthenticating, isDataValid]);

  return {
    isAuthenticating,
    handleAuthenticate,
    disabled,
    setName,
    setIdCard,
    isDataValid,
  };
};
