import { useAppSelector, useIsUuidUser } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { router, useDidShow } from '@jgl/utils';
import { useSetAtom } from 'jotai';
import { useCallback, useEffect, useState } from 'react';
import { LayoutAnimation } from 'react-native';
import { myLessons } from '../api/LessonServiceApi';
import { myLessonsAtom } from '../atom';
import type { LessonDTO } from '../dtos/LessonDTO';
import { routerMap } from '../utils/routerMap';

/**
 * 我的课程
 */
export const useMineContentLessons = () => {
  const userId = useAppSelector((state) => state.userInfo.userId);
  const setMyLessons = useSetAtom(myLessonsAtom);
  const [lessons, setLessons] = useState<LessonDTO[]>([]);
  const isUuidUser = useIsUuidUser();

  const fetchMyLessonsData = useCallback(async () => {
    const response = await container.net().fetch(myLessons());
    const { data, success } = response;
    if (success) {
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
      setMyLessons(data ?? []);
      setLessons(data ?? []);
    }
  }, [setMyLessons]);

  useEffect(() => {
    if (userId && !isUuidUser) {
      fetchMyLessonsData();
    }
  }, [fetchMyLessonsData, isUuidUser, userId]);

  useDidShow(() => {
    fetchMyLessonsData();
  });

  const onPressMore = useCallback(() => {
    router.push(routerMap.MyLesson);
  }, []);

  return {
    lessons,
    onPressMore,
  };
};
