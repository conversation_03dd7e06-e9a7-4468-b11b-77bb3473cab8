import { useIMLogin, useIMLogout } from '@jgl/ai-qa-v2';
import { HeaderCloseIconButton } from '@jgl/biz-components';
import {
  type AppLoginParams,
  AppLoginType,
  type UserInfo,
  agreementLinks,
  getWeChatAccessInfo,
  isLoggingInAtom,
  isLoggingOutAtom,
  isLoginScreenAgreementCheckedAtom,
  logInUuidAtom,
  store,
  updateUserInfo,
  useAppDispatch,
  useAppSelector,
} from '@jgl/biz-func';
import { container } from '@jgl/container';
import { JglText, JglTouchable } from '@jgl/ui-v4';
import {
  AccountEmitterHelper,
  type AccountEmitterResult,
  USERINFO,
  router,
  setStorage,
  useRouterParams,
} from '@jgl/utils';
import { useNavigation } from 'expo-router';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import { useCallback } from 'react';
import { Alert } from 'react-native';
import { login, logout } from '../api/UserServiceApi';
import { routerMap } from '../utils/routerMap';
import { navigateToPage } from '../utils/sceneUtil';

const key = USERINFO;

/**
 * 登录、退出登录
 */
export const useLogIn = () => {
  const [isLoggingIn, setIsLoggingIn] = useAtom(isLoggingInAtom);
  const search = useRouterParams();
  const { logInByWeChatInternal } = useLogInByWeChatInternal();
  const { logInByUuidInternal } = useLogInByUuidInternal();
  const { logInBySessionIdInternal } = useLogInBySessionIdInternal();
  const { logInBySmsCodeInternal } = useLogInBySmsCodeInternal();
  const { showAgreementAlertIfNeeded } = useShowAgreementAlertIfNeeded();
  const [, setIsLoginScreenAgreementChecked] = useAtom(
    isLoginScreenAgreementCheckedAtom,
  );

  const nav = useNavigation();
  /**
  /**
   * 一次性通知，通知上一个页面绑定成功或者跳过，目前在{@link orderVip}中使用
   * @see orderVip
   */
  const emit = useCallback(
    (result: AccountEmitterResult) => {
      const { communicationCode } = search;
      if (communicationCode && typeof communicationCode === 'string') {
        AccountEmitterHelper.getInstance().emit(communicationCode, result);
      }
    },
    [search],
  );

  const logInCommon = useCallback(
    async (args: {
      loginType: AppLoginType;
      logInFunc: () => Promise<UserInfo | undefined>;
    }): Promise<{ logInSuccess: boolean }> => {
      const { logInFunc, loginType } = args;

      const needAgreementAlertTypes = [
        AppLoginType.Mobile,
        AppLoginType.SmsCode,
        AppLoginType.WeChat,
      ];
      if (needAgreementAlertTypes.includes(loginType)) {
        if ((await showAgreementAlertIfNeeded()).isAgreementChecked === false) {
          return { logInSuccess: false };
        }
      }

      if (loginType !== AppLoginType.WeChat) {
        // 避免微信登录跳转到微信后不登录直接返回app，一直转菊花的问题
        // 微信登录的菊花开时时机单独控制
        setIsLoggingIn(loginType);
      }

      const user = await logInFunc();
      if (user) {
        setIsLoginScreenAgreementChecked(undefined);

        // TODO: aini - 有可能改为 declarative 的形式，在logInModal里，更好一些
        // 关闭登录弹窗
        if (needAgreementAlertTypes.includes(loginType)) {
          if (!user.phone) {
            // 没绑定手机号，去绑定手机号
            const communicationCode = Math.random()
              .toString(36)
              .substring(2, 15);
            AccountEmitterHelper.getInstance().on(
              communicationCode,
              (result) => {
                emit({ state: 'success' });
                search.backPath
                  ? navigateToPage({ path: search.backPath })
                  : router.push(routerMap.Home);
              },
            );
            router.push(routerMap.BindPhoneModal, {
              canJump: true,
              communicationCode,
            });
          } else {
            if (
              loginType === AppLoginType.SmsCode &&
              search.backPath === '..'
            ) {
              nav.getParent()?.goBack();
            } else {
              if (search.backPath === undefined) {
                router.push(routerMap.Home);
              } else {
                navigateToPage({ path: search.backPath });
              }
            }
          }
        }
      }

      setIsLoggingIn(false);

      const logInSuccess = user != null;
      return { logInSuccess };
    },
    [
      setIsLoggingIn,
      showAgreementAlertIfNeeded,
      setIsLoginScreenAgreementChecked,
      emit,
      search.backPath,
      nav,
    ],
  );

  const logInBySmsCode = useCallback(
    async (args: { mobile: string; code: string }) => {
      return logInCommon({
        logInFunc: () => logInBySmsCodeInternal(args),
        loginType: AppLoginType.SmsCode,
      });
    },
    [logInCommon, logInBySmsCodeInternal],
  );

  const logInByWeChat = useCallback(async () => {
    return logInCommon({
      logInFunc: logInByWeChatInternal,
      loginType: AppLoginType.WeChat,
    });
  }, [logInCommon, logInByWeChatInternal]);

  const logInUuid = useCallback(async () => {
    return logInCommon({
      logInFunc: () => logInByUuidInternal({ from: 'agreement' }),
      loginType: AppLoginType.Uuid,
    });
  }, [logInCommon, logInByUuidInternal]);

  const logInBySessionId = useCallback(
    async (sessionId: string) => {
      return logInCommon({
        logInFunc: () => logInBySessionIdInternal({ sessionId }),
        loginType: AppLoginType.SessionId,
      });
    },
    [logInBySessionIdInternal, logInCommon],
  );

  const headerRight = useCallback(() => {
    if (search.canJump === 'true') {
      return (
        <JglTouchable
          onPress={() => {
            emit({ state: 'skip' });
            search.backPath
              ? navigateToPage({ path: search.backPath })
              : router.push(routerMap.Home);
          }}
          jglClassName='flex-center'
        >
          <JglText fontSize={14} color={'#A0A0A0'}>
            跳过
          </JglText>
        </JglTouchable>
      );
    }
    return null;
  }, [emit, search.backPath, search.canJump]);

  const handleClose = useCallback(() => {
    emit({ state: 'cancel' });
    search.backPath
      ? navigateToPage({ path: search.backPath })
      : router.push(routerMap.Home);
  }, [emit, search.backPath]);

  const headerLeft = useCallback(() => {
    return <HeaderCloseIconButton onPress={handleClose} />;
  }, [handleClose]);

  const preLogInByWeChat = useCallback(() => {
    logInByWeChat();
  }, [logInByWeChat]);

  return {
    isLoggingIn,
    logInByWeChat: preLogInByWeChat,
    logInUuid,
    logInBySessionId,
    logInBySmsCode,
    headerRight,
    headerLeft,
  };
};

const sendLogInRequest = async (
  args: AppLoginParams,
): Promise<UserInfo | undefined> => {
  const request = login(args);
  const response = await container.net().fetch(request);
  const { success, data } = response;
  if (success && data) {
    const user = data;

    await setStorage(key, user);
    store.dispatch(updateUserInfo(user));

    console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - sendLogInRequest', user);

    return user;
  }

  return undefined;
};

const useLogInByWeChatInternal = () => {
  const setIsLoggingIn = useSetAtom(isLoggingInAtom);
  const { imLogin } = useIMLogin();

  const logInByWeChatInternal = useCallback(async (): Promise<
    UserInfo | undefined
  > => {
    const weChatAccessInfo = await getWeChatAccessInfo({
      onLoadingStart: () => {
        setIsLoggingIn(AppLoginType.WeChat);
      },
    });

    const {
      ttpId,
      nickname: nick,
      sex,
      headimgurl: smallPhoto,
    } = weChatAccessInfo ?? {};
    if (ttpId) {
      const user = await sendLogInRequest({
        loginType: AppLoginType.WeChat,
        ttpId,
        nick,
        sex,
        smallPhoto,
      });
      if (user?.userId) {
        await imLogin(user.userId.toString());
      }
      return user;
    }

    return undefined;
  }, [imLogin, setIsLoggingIn]);

  return { logInByWeChatInternal };
};

const useLogInBySmsCodeInternal = () => {
  const logInUuid = useAtomValue(logInUuidAtom);
  const { imLogin } = useIMLogin();
  const logInBySmsCodeInternal = useCallback(
    async (args: {
      mobile: string;
      code: string;
    }): Promise<UserInfo | undefined> => {
      const { mobile, code } = args;
      const logInParams: AppLoginParams = {
        loginType: AppLoginType.SmsCode,
        mobile,
        validCode: code,
        ttpId: logInUuid,
      };
      const user = await sendLogInRequest(logInParams);
      if (user?.userId) {
        await imLogin(user.userId.toString());
      }
      return user;
    },
    [imLogin, logInUuid],
  );
  return { logInBySmsCodeInternal };
};

/**
 * 不要直接使用，使用 useLogIn、useLogOut
 *
 * 使用场景
 * - 同意协议时
 * - 退出登录时
 */
export const useLogInByUuidInternal = () => {
  const logInUuid = useAtomValue(logInUuidAtom);
  const [, setIsLoggingOut] = useAtom(isLoggingOutAtom);
  const dispatch = useAppDispatch();
  const userInfo = useAppSelector((s) => s.userInfo);

  const { imLogout } = useIMLogout();
  const { imLogin } = useIMLogin();
  const logInByUuidInternal = useCallback(
    async (args: {
      from: 'agreement' | 'logOut' | 'loadAndRefreshUserInfo';
    }) => {
      const { from } = args;

      if (from === 'logOut') {
        // 退出登录接口
        setIsLoggingOut(true);
        await container.net().fetch(logout());

        // 退出登录 IM
        await imLogout();

        // 清除tid
        dispatch(updateUserInfo({ ...userInfo, sessionId: undefined }));
      }

      const user = await sendLogInRequest({
        loginType: AppLoginType.Uuid,
        ttpId: logInUuid,
      });

      if (user?.userId) {
        console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - uuid 登录成功，开始登录 IM');
        await imLogin(user.userId.toString());
      }

      if (from === 'logOut') {
        // 退出登录流程结束
        setIsLoggingOut(false);
      }

      return user;
    },
    [dispatch, imLogin, imLogout, logInUuid, setIsLoggingOut, userInfo],
  );

  return { logInByUuidInternal };
};

const useLogInBySessionIdInternal = () => {
  const { imLogin } = useIMLogin();
  const logInBySessionIdInternal = useCallback(
    async (args: { sessionId: string }) => {
      const { sessionId } = args;
      const user = await sendLogInRequest({
        loginType: AppLoginType.SessionId,
        sessionId,
      });
      if (user?.userId) {
        console.log(
          'leejunhui - 🔥🔥🔥🔥🔥🔥 - sessionId 登录成功，开始登录 IM',
        );

        await imLogin(user.userId.toString());
      }
      return user;
    },
    [imLogin],
  );

  return { logInBySessionIdInternal };
};

const useShowAgreementAlertIfNeeded = () => {
  const [isLoginScreenAgreementChecked, setIsLoginScreenAgreementChecked] =
    useAtom(isLoginScreenAgreementCheckedAtom);

  const showAgreementAlertIfNeeded = useCallback(async (): Promise<{
    isAgreementChecked: boolean;
  }> => {
    if (isLoginScreenAgreementChecked) {
      return { isAgreementChecked: true };
    } else {
      return new Promise((resolve) => {
        Alert.alert(
          '是否已阅读并同意以下内容？',
          agreementLinks.map((a) => a.name).join('、'),
          [
            {
              text: '取消',
              onPress: () => {
                resolve({ isAgreementChecked: false });
              },
            },
            {
              text: '同意并继续',
              onPress: () => {
                setIsLoginScreenAgreementChecked(true);
                resolve({ isAgreementChecked: true });
              },
            },
          ],
        );
      });
    }
  }, [isLoginScreenAgreementChecked, setIsLoginScreenAgreementChecked]);

  return { showAgreementAlertIfNeeded };
};
