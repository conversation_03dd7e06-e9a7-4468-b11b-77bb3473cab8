import {
  AppConfigCode,
  type FeedbackDTO,
  feedbackList,
  getBooklnServiceConfig,
} from '@jgl/biz-func';
import type { JglModalRef } from '@jgl/ui-v4/src/components/JglModal/JglModal.type';
import { copyToClipBoard, envVars, router } from '@jgl/utils';
import { showToast } from '@yunti-private/jgl-ui';
import { useApiQueries } from '@yunti-private/net-query-hooks';
import { useCallback, useRef } from 'react';
// import type ViewShot from 'react-native-view-shot';
import { routerMap } from '../utils/routerMap';

/**
 * 处理客服中业务hook
 */
export const useCustomerServiceCenter = () => {
  const modalRef = useRef<JglModalRef>(null);
  //   const viewShotRef = useRef<ViewShot>(null);
  const { data, isLoading, retry, error } = useApiQueries({
    queries: {
      service: {
        apiFunc: getBooklnServiceConfig,
        params: AppConfigCode.BooklnServiceConfig,
      },
      feedbackDTOList: {
        apiFunc: feedbackList,
        params: { _appid: envVars.appId() },
      },
    },
    options: {},
  });

  // 获取反馈列表
  const feedbackDataList = data?.feedbackDTOList;

  // 获取反馈配置
  const { helpFeedback } = JSON.parse(data?.service?.result || '{}');

  // 复制微信号
  const copyWechat = useCallback(() => {
    copyToClipBoard(helpFeedback?.wechatId);
    showToast({ title: '复制成功' });
  }, [helpFeedback?.wechatId]);

  // 点击反馈列表
  const onPressItem = useCallback(async (item: FeedbackDTO) => {
    router.push(routerMap.CommonProblem, {
      title: item.title || '常见问题',
      id: item.id,
    });
  }, []);

  // 点击联系客服
  const onPressContact = useCallback(() => {
    modalRef.current?.show();
  }, []);

  // 点击投诉专线
  const onPressComplaint = useCallback(() => {
    modalRef.current?.show();
  }, []);

  // 点击保存二维码
  const onPressSaveQrCode = useCallback(() => {
    modalRef.current?.show();
  }, []);

  return {
    feedbackDataList,
    service: helpFeedback,
    isLoading,
    retry,
    error,
    copyWechat,
    onPressItem,
    modalRef,
    onPressContact,
    onPressComplaint,
    onPressSaveQrCode,
    // viewShotRef,
  };
};
