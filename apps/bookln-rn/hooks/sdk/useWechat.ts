import { agreementStateAtom } from '@jgl/biz-func';
import { envVars } from '@jgl/utils';
import { useAtomValue } from 'jotai';
import { registerApp } from 'native-wechat';
import { useEffect, useMemo } from 'react';

/**
 * 初始化微信
 */
export const useWechat = () => {
  const agreementState = useAtomValue(agreementStateAtom);

  const autoInit = useMemo(
    () => agreementState === 'agreed' && envVars.loginLogicVersion() === 'old',
    [agreementState],
  );

  useEffect(() => {
    if (autoInit) {
      // https://wxappsdk.bookln.cn/.well-known/apple-app-site-association
      // https://wxappsdk.bookln.cn/appid105
      const universalLink = `https://wxappsdk.bookln.cn/appid${envVars.appId()}`;
      registerApp({
        appid: envVars.weChatAppId(),
        universalLink: universalLink,
        log: __DEV__,
      });
    }
  }, [autoInit]);
};
