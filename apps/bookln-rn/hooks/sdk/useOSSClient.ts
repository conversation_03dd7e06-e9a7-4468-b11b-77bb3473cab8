import { agreementStateAtom } from "@jgl/biz-func";
import { OSSUploadNative } from "@jgl/upload/OSSUpload.native";
import { useAtomValue } from "jotai";
import { useEffect } from "react";

/**
 * 初始化OSS客户端
 */
export const useOSSClient = () => {
    const agreementState = useAtomValue(agreementStateAtom);
    useEffect(() => {
        if (agreementState === 'agreed') {
            OSSUploadNative.initOSSClient();
        }
    }, [agreementState]);
}