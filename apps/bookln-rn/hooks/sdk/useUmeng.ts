import { useAtomValue } from "jotai";
import { atomMap, currentChannelAtom } from "../../atom";
import { useEffect } from "react";
import { RnUmeng } from "@yunti-private/rn-umeng";
import { Platform } from "react-native";
import { agreementStateAtom } from "@jgl/biz-func";

/**
 * 初始化友盟
 */
export const useUmeng = () => {
    const currentChannel = useAtomValue(currentChannelAtom);

    const agreementState = useAtomValue(agreementStateAtom);

    const appKey = Platform.select({
            ios: '614ee3a87fc3a3059b1f7fc7',
            android: '61668ea9ac9567566e952b0e',
          });

    useEffect(() => {
        if (currentChannel && appKey && agreementState === 'agreed') {
          RnUmeng.init({
            appKey,
            channel: currentChannel,
          });
        }
    }, [currentChannel, appKey, agreementState]);
}