import { agreementStateAtom, useAppSelector } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { envVars } from '@jgl/utils';
import { init, mobileReplayIntegration, setUser } from '@sentry/react-native';
import { useAtom, useAtomValue } from 'jotai';
import { useCallback, useEffect, useMemo } from 'react';
import { atomMap } from '../../atom';

// 忽略的错误
const ignoreErrors = ['Google Play Services are not available on this device'];

/** 使用Sentry */
export const useSentry = () => {
  const { name, userId } = useAppSelector((s) => s.userInfo);
  const [isInitialized, setInitialized] = useAtom(atomMap.isSentryInitializedAtom);

  const agreementState = useAtomValue(agreementStateAtom);

  /** 是否需要初始化Sentry */
  const needInitSentry = useMemo(
    () => !isInitialized && !__DEV__ && agreementState === 'agreed',
    [agreementState, isInitialized],
  );

  const initSentry = useCallback(() => {
    init({
      dsn: 'https://<EMAIL>/5',
      // enableInExpoDevelopment: true,
      release: `com.yunti.zzm@${envVars.appVersion()}+${envVars.appVersionId()}`,
      environment: container.env().env(),
      ignoreErrors,
      dist: envVars.appVersion(),
      replaysSessionSampleRate: 0.1,
      replaysOnErrorSampleRate: 1.0,
      integrations: [
        mobileReplayIntegration({
          maskAllText: false,
          maskAllImages: false,
          maskAllVectors: false,
        }),
      ],
      sampleRate: 1.0,
      tracesSampleRate: 1.0,
      enableAutoSessionTracking: true,
      debug: false, // If `true`, Sentry will try to print out useful debugging information if something goes wrong with sending the event. Set it to `false` in production
    });
    setInitialized(true);
  }, [setInitialized]);

  useEffect(() => {
    if (needInitSentry) {
      initSentry();
    }
  }, [initSentry, needInitSentry]);

  useEffect(() => {
    if (isInitialized && userId) {
      setUser({ id: userId, username: name });
    }
  }, [isInitialized, name, userId]);
};
