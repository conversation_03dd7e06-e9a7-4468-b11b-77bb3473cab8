import { useInitIflytek } from '../useInitIflytek';
import { useInitIAP } from './useInitIAP';
import { useOSSClient } from './useOSSClient';
import { useSentry } from './useSentry';
import { useUmeng } from './useUmeng';
import { useExpoUpdate } from './useUpdateConfig';
import { useWechat } from './useWechat';

/**
 * 初始化sdk
 */
export const useInitSdk = () => {
  // 初始化友盟
  useUmeng();
  // 初始化Sentry
  useSentry();
  // 初始化OSS客户端
  useOSSClient();
  // 初始化热更新
  useExpoUpdate();
  // 初始化微信
  useWechat();
  // 初始化IAP
  useInitIAP();
  // 初始化讯飞SDK
  useInitIflytek();
};
