import { useAtom } from 'jotai';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type {
  FlatList,
  NativeScrollEvent,
  NativeSyntheticEvent,
} from 'react-native';
import { Dimensions } from 'react-native';
import { atomMap } from '../../atom';
import type { AiBookTopicItem } from './bookData.type';
import { useFetchAiBookData } from './useFetchAiBookData';

export const useAiBookPage = () => {
  const { book, isLoading } = useFetchAiBookData();
  const [aiBookInfo, setAiBookInfo] = useAtom(atomMap.aiBookInfo);
  const { pageId, topicId } = aiBookInfo;
  const cellWidth = useMemo(() => Dimensions.get('window').width, []);
  const tableViewIndexRef = useRef(0);

  const [flatListScrollEnabled, setFlatListScrollEnabled] =
    useState<boolean>(true);
  const pageTableRef = useRef<FlatList>(null);

  const currentPageIndex = useMemo(() => {
    const index = book?.pages?.findIndex((item) => item.pageId === pageId);
    if (index === undefined || index === -1) {
      return 0;
    }
    return index;
  }, [book?.pages, pageId]);

  const currentPage = useMemo(() => {
    return book?.pages?.[currentPageIndex];
  }, [book?.pages, currentPageIndex]);

  const currentTopic = useMemo(() => {
    return currentPage?.topics?.find((item) => item.itemId === topicId);
  }, [currentPage?.topics, topicId]);

  const totalPage = book?.pages?.length || 0;

  const handleClickTopic = useCallback(
    (topic: AiBookTopicItem) => {
      setAiBookInfo({
        pageId: currentPage?.pageId,
        topicId: topic.itemId,
        bookId: book?.bookId,
      });
    },
    [book?.bookId, currentPage?.pageId, setAiBookInfo],
  );

  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const offsetX = event.nativeEvent.contentOffset.x;
      const index = Math.round(offsetX / cellWidth);
      const page = book?.pages?.[index];
      // 冲突
      if (tableViewIndexRef.current !== index) {
        tableViewIndexRef.current = index;
        setAiBookInfo({
          pageId: page?.pageId,
          topicId,
          bookId: book?.bookId,
        });
      }
    },
    [book?.bookId, book?.pages, cellWidth, setAiBookInfo, topicId],
  );

  useEffect(() => {
    if (
      pageTableRef.current &&
      tableViewIndexRef.current !== currentPageIndex &&
      book?.pages?.length
    ) {
      tableViewIndexRef.current = currentPageIndex;

      pageTableRef.current.scrollToIndex({
        index: currentPageIndex,
        animated: true,
      });
    }
  }, [book, book?.pages, currentPageIndex]);

  const handleScrollToNextItem = useCallback(() => {
    if (currentPageIndex + 1 < totalPage) {
      setFlatListScrollEnabled(true);
      pageTableRef.current?.scrollToIndex({
        index: currentPageIndex + 1,
        animated: true,
      });
      return true;
    }
    return false;
  }, [currentPageIndex, totalPage]);

  const handleScrollToPrevItem = useCallback(() => {
    if (currentPageIndex - 1 >= 0) {
      setFlatListScrollEnabled(true);
      pageTableRef.current?.scrollToIndex({
        index: currentPageIndex - 1,
        animated: true,
      });
      return true;
    }
    return false;
  }, [currentPageIndex]);

  return {
    isLoading,
    currentPageIndex,
    currentPageId: currentPage?.pageId,
    currentTopic,
    cellWidth,
    book,
    handleClickTopic,
    handleScroll,
    pageTableRef,
    handleScrollToNextItem,
    handleScrollToPrevItem,
    flatListScrollEnabled,
    setFlatListScrollEnabled,
  };
};
