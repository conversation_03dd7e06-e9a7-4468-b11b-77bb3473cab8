import { audioSounds, shuffleArray, useAudio, useDidHide, useDidShow } from '@jgl/utils';
import { useCountDown, useCounter, useGetState, useUnmount } from 'ahooks';
import { cloneDeep, isNumber } from 'lodash';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type { WordItem } from '../../api/dto';
import { splitEliminateItem } from '../../utils/splitEliminateItem';

/** 单词解释匹配块数 */
const matchLen = 2;

type EliminateOptions = {
  /** 数据 */
  items: WordItem[];
  //   gameConf?: GameConf;
  onEnd?: (score: number, alreadyPass?: boolean) => void;
  //   sounds?: AudioSounds;
};

const defaultSounds = {
  successAudio: audioSounds.game_success_audio,
  errorAudio: audioSounds.game_fail_audio,
};
const gameConf = {
  itemsLengthPerRound: 9,
  limitTime: 120,
  checkChance: 1,
};
const sounds = defaultSounds;

type SplitItemDTO = {
  pid: string;
  text: string;
  matchText?: string;
  id: string;
  audio?: string;
  bgUrl?: string;
  disappear: boolean;
  success?: boolean;
  error?: boolean;
  bgColor?: string;
};

/** 单词消消乐业务逻辑hook */
export const useAiBookEliminate = (options: EliminateOptions) => {
  const {
    items = [],
    // sounds = defaultSounds,
    onEnd,
    // gameConf = defaultGameConf,
  } = options;

  /** 消消乐拆分后单词数据 */
  const [wordsItems, setWordsItems, getWordsItems] = useGetState<SplitItemDTO[]>([]);

  const [chosenItems, setChosenItems] = useState<SplitItemDTO[]>([]);
  /** 动画进行中 */
  const [animating, setAnimating] = useState(false);

  /** 游戏结束，展示结果页 */
  const [finished, setFinished] = useState(false);

  /** 长按详情id */
  const [longPressId, setLongPressId] = useState<string | undefined>();

  const [guluNum, setGuluNum] = useState(0);

  const [score, { inc, reset }] = useCounter(0);

  const eliminateAudio = useAudio();

  const [targetDate, setTargetDate] = useState<number | undefined>(undefined);
  const [challengeStatus, setChallengeStatus] = useState<'success' | 'fail'>('success');

  const initWordItems = useCallback(() => {
    const newItems = shuffleArray(items).slice(0, gameConf.itemsLengthPerRound);
    const splitWords = splitEliminateItem(newItems);
    setWordsItems(splitWords);
    setGuluNum(0);

    if (gameConf.limitTime) {
      const now = Date.now() + (gameConf.limitTime ?? 0) * 1000;
      setTargetDate(now);
    }
  }, [items, setWordsItems]);

  // 监听数据源变化重新分消消乐单词
  useEffect(() => {
    initWordItems();
  }, [initWordItems]);

  const refresh = useCallback(() => {
    initWordItems();
    setChosenItems([]);
    eliminateAudio.stop();
  }, [initWordItems, eliminateAudio]);

  const countdownRef = useRef<number | undefined>();

  const [countdown] = useCountDown({
    targetDate,
    onEnd: () => {
      if (countdownRef.current === undefined) {
        setChallengeStatus('fail');
        setFinished(true);
      }
    },
  });

  useDidHide(() => {
    if (countdown) {
      countdownRef.current = countdown;
    }
  });

  useDidShow(() => {
    if (countdownRef.current) {
      setTargetDate(Date.now() + countdownRef.current);
      countdownRef.current = undefined;
    }
  });

  // 组件卸载更新分数
  useUnmount(() => {
    !finished && onEnd?.(score, false);
  });

  /**
   * 检查是否消除正确
   * @items 当前选择的块
   */
  const checkIsRight = useCallback(
    async (checkItems: SplitItemDTO[]) => {
      if (checkItems.length === matchLen) {
        const [first, second] = checkItems;
        const newItems = cloneDeep(wordsItems);
        const firstIndex = newItems.findIndex((item) => item.id === first?.id);
        const secondIndex = newItems.findIndex((item) => item.id === second?.id);
        const firstItem = newItems[firstIndex];
        const secondItem = newItems[secondIndex];

        // 对应关系匹配or文案匹配均为正确
        const isRight =
          first?.pid === second?.pid ||
          firstItem?.matchText === secondItem?.text ||
          secondItem?.matchText === firstItem?.text;

        /** 单词和解释正确 */
        if (isRight) {
          if (firstItem) {
            firstItem.disappear = true;
            firstItem.success = true;
          }

          if (secondItem) {
            secondItem.disappear = true;
            secondItem.success = true;
          }
          eliminateAudio.play({
            src: sounds?.successAudio ?? '',
            isRepeat: false,
          });
          // 分数+1
          inc();
        } else {
          if (firstItem) {
            firstItem.error = true;
          }

          if (secondItem) {
            secondItem.error = true;
          }
          eliminateAudio.play({
            src: sounds?.errorAudio ?? '',
            isRepeat: false,
          });
        }
        setWordsItems(newItems);
      }
    },
    [wordsItems, setWordsItems, eliminateAudio, inc],
  );

  /** 选中块 */
  const onChoseWordItem = useCallback(
    (item: SplitItemDTO) => {
      if (animating) {
        return;
      }
      eliminateAudio.stop();
      const [first] = chosenItems;
      // 重复选中则取消选中
      if (first && first.id === item.id) {
        setChosenItems([]);
        setAnimating(false);
        return;
      }
      const newChosenItems = cloneDeep(chosenItems);
      newChosenItems.push(item);
      setChosenItems(newChosenItems);

      if (newChosenItems.length === matchLen) {
        checkIsRight(newChosenItems);
      } else {
        if (item.audio) {
          eliminateAudio.play({ src: item.audio, isRepeat: false });
        }
      }
    },
    [chosenItems, checkIsRight, eliminateAudio, animating],
  );

  /** 消除错误动画结束 */
  const onAnimationEnd = useCallback(async () => {
    const newItems = getWordsItems().map((item) => {
      return {
        ...item,
        success: false,
        error: false,
      };
    });
    setWordsItems(newItems);
    setChosenItems([]);
    setAnimating(false);
    if (newItems.every((i) => i.disappear)) {
      setChallengeStatus('success');
      setTargetDate(undefined);
      setFinished(true);
      const num = await onEnd?.(score, true);

      if (isNumber(num) && num > 0) {
        setGuluNum(num);
      }
    }
  }, [getWordsItems, onEnd, score, setWordsItems]);

  /** 长按显示内容 */
  const maskContent = useMemo(() => {
    const result = wordsItems.find((item) => item.id === longPressId);
    return result?.text;
  }, [longPressId, wordsItems]);

  /** 默认提示文案 */
  const tipText = useMemo(() => {
    if (score) {
      return `当前分数：${score}`;
    }
    return '长按内容可查看完整信息';
  }, [score]);

  return {
    wordsItems,
    score,
    guluNum,
    animating,
    setAnimating,
    finished,
    countdown,
    challengeStatus,
    tipText,
    setFinished,
    setWordsItems,
    chosenItems,
    setChosenItems,
    maskContent,
    longPressId,
    setLongPressId,
    onAnimationEnd,
    onChoseWordItem,
    refresh,
    reset,
  };
};
