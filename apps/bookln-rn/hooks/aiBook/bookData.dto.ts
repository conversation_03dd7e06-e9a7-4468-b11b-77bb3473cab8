export type AiBookDto = Partial<{
  bookId: number;
  bookName: string;
  bookItems: AiBookItemDto[];
}>;

type AiBookItemDto = Partial<{
  catalogName: string;
  pageNo: number;
  pageId: number;
  pageUrl: string;
  items: AiBookTopicItemDto[];
}>;

type AiBookTopicItemDto = Partial<{
  coord: {
    column: number[];
    height: number[];
  };
  itemId: number;
  itemUrl: string;
  video_info: {
    end: string;
    id: string;
    score: number;
    start: string;
    url: string;
  }[];
  audioUrl: string;
  words: AiBookWordItemDto[];
  answer_analysis: {
    answers: string[];
    markdown: string;
    srt_urls: string[];
    tts_urls: string[];
  };
}>;

type AiBookWordItemDto = Partial<{
  text: string;
  coord: {
    column: number[];
    height: number[];
  };
}>;
