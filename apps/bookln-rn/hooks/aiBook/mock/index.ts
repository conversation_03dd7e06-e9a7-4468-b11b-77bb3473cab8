import mockJson from './data.json';
import mockTakePhoto from './take_photo.json';

export const catalogsSubtitles = [
  ['必会单词', '重点短语', '核心句型'],
  [
    '一、判断句子中画线部分发音是（T）否（F）相同',
    '二、根据图片及首字母提示填空',
    '三、用所给词的适当形式填空',
    '四、单项选择',
  ],
  ['四、单项选择', '五、按要求完成句子', '六、情境题'],
  [
    '一、判断句子中画线部分发音是（T）否（F）相同',
    '二、从括号内选词填空',
    '三、单项选择',
    '四、判断下列句子与图片是（T）否（F）相符',
  ],
  [
    '四、判断下列句子与图片是（T）否（F）相符',
    '五、从右栏中选出左栏句子的答句',
    '六、连词成句',
    '七、(新考法)根据表格内容，补全句子',
  ],
  [
    '形容词比较级和最高级的变化规则（规则变化）',
    '形容词比较级和最高级的变化规则（不规则变化）',
    '形容词比较级和最高级的用法',
  ],
  [
    '一、用所给词的适当形式填空',
    '二、用方框内所给词的适当形式填空',
    '三、单项选择',
    '四、情境题',
  ],
  [
    '一、根据中文提示填空',
    '二、用所给词的适当形式填空',
    '三、单项选择',
    '四、按要求完成句子',
  ],
  [
    '一、从括号内选词填空',
    '二、用所给词的适当形式填空',
    '三、单项选择',
    '四、情境题',
  ],
  [
    '一、听录音，选出你所听到的单词或短语',
    '二、听录音，判断下列图片与所听内容是（T）否（F）相符',
    '三、听录音，选择合适的答句',
    '四、听录音，根据所听长对话，判断下列句子正（T）误（F）',
    '五、听录音，补全短文',
  ],

  [
    '一、阅读短文，选择正确的答案',
    '二、(新素材）请根据调查问卷上的信息，选择正确的答案',
  ],
  [
    '二、(新素材）请根据调查问卷上的信息，选择正确的答案',
    '三、阅读短文，完成下列各题',
  ],

  ['四、阅读材料，完成下列各题'],
  ['典例精析', '素材积累', '书面表达'],
  [
    '一、听录音，判断下列句子与所听内容是（T）否（F）一致',
    '二、听录音，给下列图片排序',
    '三、听录音，选择合适的答句',
    '四、听录音，根据所听长对话，选择正确的答案',
    '五、听录音，补全短文',
  ],
  [
    '五、听录音，补全短文',
    '六、情境题',
    '七、根据首字母提示填空',
    '八、用所给词的适当形式填空',
    '九、单项选择',
  ],
  [
    '九、单项选择',
    '十、从右栏中选出左栏句子的答句',
    '十一、按要求完成句子',
    '十二、看图，完成对话',
    '十三、从方框中选词完成短文',
  ],
  [
    '十三、从方框中选词完成短文',
    '十四、阅读短文，选择正确的答案',
    '十五、书面表达',
  ],
];


export { mockJson };
export { mockTakePhoto };
