import { container } from '@jgl/container';
import { useCallback, useEffect, useState } from 'react';
import { testWordDemo } from '../../api/AiLearnApi';
import type { WordItem } from '../../api/dto';
import WordListRawData from '../../assets/data/wordListRawData.json';

export const useAiBookSceneData = () => {
  const [wordList, setWordList] = useState<WordItem[]>([]);

  const fetchWordList = useCallback(async () => {
    const wordListStr = JSON.stringify(WordListRawData);
    const request = testWordDemo({
      sceneType: '2',
      wordList: wordListStr,
    });

    const res = await container.net().fetch(request);
    if (res.success && res.data) {
      // 给每个 Item 增加 id 属性
      const transformedWordList = res.data.map((item, index) => ({
        ...item,
        id: index.toString(),
      }));
      setWordList(transformedWordList);
    }
  }, []);

  useEffect(() => {
    fetchWordList();
  }, [fetchWordList]);

  const refresh = useCallback(() => {
    fetchWordList();
  }, [fetchWordList]);

  return {
    wordList,
    refresh,
  };
};
