import { UserCameraCorrectApiListPageDTO } from '@yunti-private/api-bizboot';
import { YTResponse } from '@yunti-private/net';
import { useAtom, useAtomValue } from 'jotai';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { atomMap } from '../../atom';
import { TakePhotoAnswerResult, TakePhotoRecResult } from './bookData.type';
import { mockTakePhoto } from './mock';
import { getSectionTitle } from './utils';

const getRectFromRecResult = (recResult: TakePhotoRecResult[]) => {
  const left = 0;
  const right = 1;
  const top = Math.min(...recResult.map((item) => item.top || 0));
  const bottom = Math.max(
    ...recResult.map((item) => (item.top || 0) + (item.height || 0)),
  );
  const width = right - left;
  const height = bottom - top;
  return {
    left,
    top,
    width,
    height,
  };
};

const resortRects = (rects: TakePhotoAnswerResult[]) => {
  return rects.sort((a, b) => (a.top || 0) - (b.top || 0));
};

type Props = {
  type: 'topic' | 'page' | 'book';
  autoFetch: boolean;
};

export const useTakePhotoResult = ({ type, autoFetch }: Props) => {
  const { pageId, topicId } = useAtomValue(atomMap.aiBookInfo);
  const book = useAtomValue(atomMap.aiBook);
  const [takePhotoResult, setTakePhotoResult] = useAtom(
    atomMap.aiBookTakePhotoResult,
  );
  const { showResultPages } = takePhotoResult;
  const pageIdToPageNo = useMemo(() => {
    const map = new Map<number | undefined, number | undefined>();
    book?.pages?.forEach((page) => {
      map.set(page.pageId, page.pageNo);
    });
    return map;
  }, [book?.pages]);

  // TODO: weiwen - 2,3,4,5 页之外的都到第二页
  const correctPageId = useMemo(() => {
    const pageNo = pageIdToPageNo.get(pageId) || 0;
    const hasTakePhotoPages = [2, 3, 4, 5];
    if (!hasTakePhotoPages.includes(pageNo)) {
      const page2Id = 1371152;
      return page2Id;
    } else {
      return pageId;
    }
  }, [pageId, pageIdToPageNo]);

  const [answerResult, setAnswerResult] = useState<TakePhotoAnswerResult[]>([]);

  const processResponse = useCallback(
    (response: UserCameraCorrectApiListPageDTO | undefined) => {
      const firstRecord: Set<string> = new Set();
      const items = (response?.pageData || [])
        .map((page) => {
          if (firstRecord.size === 0 && page.recordId) {
            firstRecord.add(page.recordId);
          }
          const result = JSON.parse(page.result || 'null') as any;
          return {
            recordId: page.recordId,
            pageId: page.pageId,
            itemId: page.itemId,
            pageNo: pageIdToPageNo.get(page.pageId),
            id: page.id,
            result,
          };
        })
        .filter((item) => {
          return firstRecord.has(item.recordId || '');
        });

      if (items.length === 0) return [];

      const results: TakePhotoAnswerResult[] = items.map(
        ({ pageId: _pageId, itemId, id, result: item, pageNo }, i) => {
          const result = item.result as number[];
          const rects: TakePhotoRecResult[] = item.recResult.map(
            (value: any, index: number) => {
              let refAnswer = '';
              if (typeof value.answer === 'string') {
                refAnswer = value.answer;
              } else if (Array.isArray(value.answer)) {
                refAnswer = value.answer[0].value;
              } else if (typeof value.answer === 'object') {
                refAnswer = value.answer.value;
              }

              return {
                left: value.coord[0],
                width: value.coord[2] - value.coord[0],
                top: value.coord[1],
                height: value.coord[3] - value.coord[1],
                refAnswer,
                userAnswer: value.value,
                isRight: result[index] === 1,
                id: uuidv4(),
              };
            },
          );

          const recResult: TakePhotoAnswerResult = {
            imageUrl: item.alignUrl,
            recResult: rects,
            pageId: _pageId,
            itemId: itemId,
            pageNo: pageNo,
            sectionTitle: getSectionTitle(pageNo || 0, i),
            ...getRectFromRecResult(rects),
            rightNum: rects.filter((v) => v.isRight).length,
            wrongNum: rects.filter((v) => !v.isRight).length,
            id,
          };
          return recResult;
        },
      );
      if (type === 'book') {
        return results;
      } else {
        return resortRects(results);
      }
    },
    [pageIdToPageNo, type],
  );

  const processAllResponse = useCallback(
    (data: (UserCameraCorrectApiListPageDTO | undefined)[]) => {
      const result = data
        .filter((item) => !!item?.pageData)
        .flatMap(processResponse);
      setAnswerResult(result);
      return result;
    },
    [processResponse],
  );

  // TODO: weiwen - mock data, don't request
  const sendRequest = useCallback(
    async (requestType: 'page' | 'topic', _pageId?: number) => {
      // let request: YTRequest<UserCameraCorrectApiListPageDTO> | undefined;
      if (requestType === 'page') {
        // request = userCameraCorrectApiListPage({
        //   pageId: _pageId,
        //   pageSize: 100,
        // });
        const pageNo = pageIdToPageNo.get(_pageId) || -1;
        return mockTakePhoto[
          pageNo - 1
        ] as YTResponse<UserCameraCorrectApiListPageDTO>;
      } else {
        // request = userCameraCorrectApiListPage({
        //   itemId: topicId,
        //   pageSize: 1,
        // });
        const pageNo = pageIdToPageNo.get(_pageId) || -1;
        const response = mockTakePhoto[pageNo - 1];
        const data =
          response?.data.pageData
            .filter((item) => item.itemId === topicId)
            ?.slice(0, 1) || [];

        return {
          ...response,
          data: {
            ...response?.data,
            pageData: data,
          },
        } as YTResponse<UserCameraCorrectApiListPageDTO>;
      }
      // return await container.net().fetch(request);
    },
    [pageIdToPageNo, topicId],
  );

  const fetchData = useCallback(async () => {
    if (type === 'book') {
      // const pages = (book?.pages || []).map((page) => page.pageId);
      // const promises = pages.map(async (id) => {
      //   return await sendRequest('page', id);
      // });
      // const responses = await Promise.all(promises);
      if (answerResult.length === 0) {
        const responses = mockTakePhoto;
        return processAllResponse(responses.map((v) => v.data));
      }
    } else {
      if (correctPageId !== pageId && type === 'page') {
        setTakePhotoResult((prev) => {
          return {
            ...prev,
            showResultPages: showResultPages.add(correctPageId || 0),
          };
        });
      }
      const response = await sendRequest(type, correctPageId);
      return processAllResponse([response.data]);
    }
  }, [
    answerResult.length,
    correctPageId,
    pageId,
    processAllResponse,
    sendRequest,
    setTakePhotoResult,
    showResultPages,
    type,
  ]);

  useEffect(() => {
    if (autoFetch) {
      fetchData();
    }
  }, [autoFetch, fetchData]);

  return {
    fetchData,
    answerResult,
  };
};
