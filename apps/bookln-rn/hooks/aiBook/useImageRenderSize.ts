import { useCallback, useState } from 'react';
import { Image } from 'react-native';

type Props = {
  url: string;
};

type ImageResult = {
  data?: {
    width: number;
    height: number;
  };
  success: boolean;
};

const getImageInfo = async (url: string): Promise<ImageResult> => {
  return new Promise((resolve) => {
    Image.getSize(
      url,
      (width, height) => {
        resolve({
          data: {
            width,
            height,
          },
          success: true,
        });
      },
      (err) => {
        return {
          success: false,
        };
      },
    );
  });
};

export const useImageRenderSize = (props: Props) => {
  const { url } = props;
  const [imgSize, setImgSize] = useState({
    width: 0,
    height: 0,
    scale: 1,
    imgOriginalWidth: 0,
    imgOriginalHeight: 0,
  });

  const calculateImageSize = useCallback(
    async (param: { width: number; height: number }) => {
      const imageInfo = await getImageInfo(url);
      if (!imageInfo.data) {
        return;
      }

      const { width: imageWidth, height: imageHeight } = imageInfo.data;

      const { width, height } = param;
      if (width === 0 || height === 0) {
        requestAnimationFrame(() => {
          calculateImageSize(param);
        });
        return;
      }

      const wRatio = width / imageWidth;
      const hRatio = height / imageHeight;
      const ratio = Math.min(wRatio, hRatio);
      setImgSize({
        width: imageWidth * ratio,
        height: imageHeight * ratio,
        scale: ratio,
        imgOriginalWidth: imageWidth,
        imgOriginalHeight: imageHeight,
      });
    },
    [url],
  );

  return { imgSize, calculateImageSize };
};
