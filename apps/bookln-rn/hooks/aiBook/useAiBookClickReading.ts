import { showToast, useAudio } from '@jgl/utils';
import { useDebounceFn } from 'ahooks';
import { useDebouncedCallback } from 'use-debounce';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type { WordItem } from '../../api/dto';

export const useClickReading = () => {
  const [realPlay, setRealPlay] = useState(false);
  const [currentItem, setCurrentItem] = useState<WordItem>({} as WordItem);
  const [show, setShow] = useState<boolean>(false);
  //   const [parsedPayload, setParsedPayload] = useState<IAiQueryWordPayload>(
  //     {} as IAiQueryWordPayload,
  //   );
  //   const wordDetailModalRef = useRef<JGLModalRef>(null);
  const wordBrowseAudio = useAudio();
  const { audioSrc, isPlay, play, stop, ended } = wordBrowseAudio;
  const itemClickTimeMapRef = useRef<Map<number, number>>(new Map());

  const scrollIntoViewId = useMemo(() => {
    return `browseItem${currentItem.id}`;
  }, [currentItem.id]);

  useEffect(() => {
    if (!isPlay) {
      setRealPlay(false);
    }
  }, [isPlay]);

  const handlePlay = useCallback(
    ({ src }: { src?: string }) => {
      if (src) {
        console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - handlePlay 来了', src);

        play({ src });
        setRealPlay(true);
      }
    },
    [play],
  );

  const handleStop = useCallback(() => {
    stop();
    setRealPlay(false);
  }, [stop]);

  // 继续播放当前 Item 的翻译
  const playTrans = useCallback(() => {
    const { meanSimpleAudio } = currentItem;
    if (!isPlay && meanSimpleAudio && meanSimpleAudio === audioSrc) {
      handleStop();
      handlePlay({ src: meanSimpleAudio });
    }
  }, [audioSrc, currentItem, handlePlay, handleStop, isPlay]);

  // 当前选中 Item 原文播放完毕
  useEffect(() => {
    if (!realPlay && ended && !show) {
      playTrans();
    }
  }, [realPlay, show, ended, playTrans]);

  /** 点击 Item */
  const onClickItem = useCallback(
    (item: WordItem) => {
      setCurrentItem(item);
      handleStop();
      if (item.phAmMp3) {
        console.log('useClickReading - onClickItem 来了', item.phAmMp3);
        handlePlay({ src: item.phAmMp3 });
      } else {
        showToast({ title: '暂无音频' });
      }
    },
    [handlePlay, handleStop],
  );

  // const { run: handleClickItem } = useDebounceFn(onClickItem, { wait: 200 });

  const handleClickItem = useDebouncedCallback(
    (item: WordItem) => {
      onClickItem(item);
    },
    200,
    { leading: true, trailing: false },
  );

  /** 长按 */
  const onLongPress = useCallback(
    (item: WordItem) => {
      console.log('useClickReading - onLongPress 来了', item);

      handleStop();
      // wordDetailModalRef.current?.show();
      setShow(true);
      //   setParsedPayload(item);
    },
    [handleStop],
  );

  /** 词义渲染 */
  const renderMean = useCallback((mean: string) => {
    return mean.split('\n').join('');
  }, []);

  return {
    realPlay,
    currentItem,
    setCurrentItem,
    show,
    scrollIntoViewId,
    changeShow: setShow,
    renderMean,
    handleStop,
    onClickItem: handleClickItem,
    onLongPress,
    setShow,
    itemClickTimeMapRef,
  };
};
