import {
  useBookAIUpdateIntentCommandMessageStatus,
  type BookAiIMCommandIntentPayload,
  type IMMessage,
} from '@jgl/ai-qa-v2';

import { useAtomValue, useSetAtom } from 'jotai';
import { useCallback, useEffect, useRef } from 'react';
import { DeviceEventEmitter } from 'react-native';
import { atomMap } from '../../atom';

export const useAiBookHandleIntentCommand = () => {
  const aiBook = useAtomValue(atomMap.aiBook);
  const aiBookRef = useRef(aiBook);

  useEffect(() => {
    aiBookRef.current = aiBook;
  }, [aiBook]);

  const setAiBookInfo = useSetAtom(atomMap.aiBookInfo);

  const { updateIntentCommandMessageStatus } = useBookAIUpdateIntentCommandMessageStatus();

  const handleAiBookIntentCommand = useCallback(
    async (param: {
      payload: BookAiIMCommandIntentPayload;
      sessionId: string;
      intentCustomMessage: IMMessage;
    }) => {
      const { payload, sessionId, intentCustomMessage } = param;
      const { scene, action, sectionNumber, pageNumber } = payload; // bookName is available if needed
      // console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ useAiBookHandleIntentCommand ~ payload:', payload); // Consider removing or using a formal logger
      // Send the "executing" message after basic validation (page exists)
      updateIntentCommandMessageStatus({
        sessionId,
        message: intentCustomMessage,
        actionStatus: 'executingCommand',
        textContent: '正在翻页',
      });
      if (pageNumber) {
        const page = aiBookRef.current?.pages?.[Number(pageNumber) - 1];
        if (!page) {
          // Page not found, consider sending an error/info message back to user via IM
          // For now, it returns early, matching original implicit behavior.
          updateIntentCommandMessageStatus({
            sessionId,
            message: intentCustomMessage,
            actionStatus: 'commandExecuteFailed',
            textContent: `第${pageNumber}页不存在`,
          });
          return;
        }

        if (sectionNumber) {
          const topicIndex = Number(sectionNumber) - 1;
          const topic = page.topics?.[topicIndex];

          if (topic) {
            setAiBookInfo((prevState) => {
              const newState = {
                ...prevState,
                pageId: topic.pageId,
                topicId: topic.itemId,
              };
              return newState;
            });

            setTimeout(() => {
              updateIntentCommandMessageStatus({
                sessionId,
                message: intentCustomMessage,
                actionStatus: 'commandExecuteSuccess',
                textContent: `已翻到第${pageNumber}页第${sectionNumber}题`,
              });
            }, 500);

            if (scene === 'aiExplain') {
              if (action === 'play' || action === 'open' || !action) {
                DeviceEventEmitter.emit('aiBookTopicPPTSection', {
                  type: 'play',
                });
              } else if (action === 'pause') {
                DeviceEventEmitter.emit('aiBookTopicPPTSection', {
                  type: 'pause',
                });
              }
            }
          } else {
            // Topic not found. "Executing" message was sent.
            // Original code did nothing more.
            // Consider sending a "failure" or "not found" message here for better UX.
            console.warn(
              `Topic ${sectionNumber} not found on page ${pageNumber}. "Executing" message sent, but no success/failure follow-up.`,
            );
            updateIntentCommandMessageStatus({
              sessionId,
              message: intentCustomMessage,
              actionStatus: 'commandExecuteFailed',
              textContent: `第${pageNumber}页第${sectionNumber}题不存在`,
            });
          }
        } else {
          // No sectionNumber
          setAiBookInfo((prevState) => {
            const newState = {
              ...prevState,
              pageId: page.pageId,
            };
            return newState;
          });

          setTimeout(() => {
            updateIntentCommandMessageStatus({
              sessionId,
              message: intentCustomMessage,
              actionStatus: 'commandExecuteSuccess',
              textContent: `已翻到第${pageNumber}页`,
            });
          }, 500);
        }
      }
    },
    [setAiBookInfo, updateIntentCommandMessageStatus],
  );

  return { handleAiBookIntentCommand };
};
