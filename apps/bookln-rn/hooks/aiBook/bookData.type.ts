export enum AiTopicType {
  Word = 'word',
  Listen = 'listen',
  Write = 'write',
  Read = 'read',
  Knowledge = 'knowledge',
}

export enum AiTopicLevel {
  Easy = 'easy',
  Medium = 'medium',
  Hard = 'hard',
}

export type AiBook = Partial<{
  bookId: number;
  bookName: string;
  pages: AiBookPage[];
}>;

export type AiBookPage = Partial<{
  catalogName: string;
  pageNo: number;
  pageId: number;
  pageUrl: string;
  topics: AiBookTopicItem[];
  bookId: number;
}>;

export type AiBookTopicItem = Partial<{
  pageId: number;
  pageNo: number;
  itemId: number;
  itemUrl: string;
  videoUrl: string;
  left: number;
  top: number;
  width: number;
  height: number;
  audioUrl: string;
  words: AiBookWordItem[];
  type: AiTopicType;
  title: {
    name: string;
    index: string;
  };
  time: number;
  answerAnalysis: AiBookAnswerAnalysis;
  audioTimestamps: AiBookAudioTimestamp[];
  level: AiTopicLevel;
  /**
   * 只是为了方便测试使用, 只在 aiBookMockData 中才进行测试.
   */
  takePhotoWrongResult: TakePhotoAnswerResult;
}>;

export type AiBookAnswerAnalysis = Partial<{
  markdown: string;
  ttsUrls: string[];
  srtUrls: string[];
}>;

export type AiBookAudioTimestamp = {
  startTime: number;
  stopTime: number;
  title: string;
  smallQuestions: AiBookAudioTimestamp[];
  lrcs: AiBookAudioLrc[];
};

export type AiBookAudioLrc = {
  timestamp: number;
  text: string;
  originalText: string;
  id: string;
};

export type AiBookWordItem = Partial<{
  text: string;
  left: number;
  top: number;
  width: number;
  height: number;
  id: string;
}>;

export type TakePhotoAnswerResult = Partial<{
  imageUrl: string;
  recResult: TakePhotoRecResult[];
  pageId: number;
  itemId: number;
  left: number;
  top: number;
  width: number;
  height: number;
  rightNum: number;
  wrongNum: number;
  id: number;
  sectionTitle: {
    name: string;
    index: string;
  };
  pageNo: number;
}>;

export type TakePhotoRecResult = Partial<{
  left: number;
  top: number;
  width: number;
  height: number;
  refAnswer: string;
  userAnswer: string;
  isRight: boolean;
  id: string;
}>;
