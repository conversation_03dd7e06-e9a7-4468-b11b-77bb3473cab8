import { catalogsSubtitles } from './mock';

const numberMap = {
  '1': '一',
  '2': '二',
  '3': '三',
  '4': '四',
  '5': '五',
  '6': '六',
  '7': '七',
  '8': '八',
  '9': '九',
  '10': '十',
};

export const getSectionTitle = (pageNo: number, index: number) => {
  const pageIndex = pageNo - 1;
  return {
    name: catalogsSubtitles[pageIndex]?.[index] || '',
    index: numberMap[`${index + 1}` as keyof typeof numberMap] || '',
  };
};
