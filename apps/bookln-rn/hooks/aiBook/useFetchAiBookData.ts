import { container } from '@jgl/container';
import { use<PERSON>tom, useSet<PERSON>tom } from 'jotai';
import { useCallback, useEffect, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { atomMap } from '../../atom';
import audioInfo from './audioInfo.json';
import { AiBookDto } from './bookData.dto';
import {
  AiTopicLevel,
  AiTopicType,
  TakePhotoAnswerResult,
  type AiBook,
  type AiBookAudioTimestamp,
  type AiBookPage,
  type AiBookTopicItem,
  type AiBookWordItem,
} from './bookData.type';
import { mockJson } from './mock';
import { useTakePhotoResult } from './useTakePhotoResult';
import { getSectionTitle } from './utils';

const mapCoordToRect = (
  coord: { column: number[]; height: number[] } | undefined,
) => {
  if (!coord) return { left: 0, top: 0, width: 0, height: 0 };
  const left = coord.column[0] || 0;
  const right = coord.column[1] || 0;
  const top = coord.height[0] || 0;
  const bottom = coord.height[1] || 0;
  return {
    left,
    top,
    width: right - left,
    height: bottom - top,
  };
};

const getAudioTimestamps = (
  pageNo: number,
  topicIndex: number,
): AiBookAudioTimestamp[] => {
  const info = audioInfo[pageNo as unknown as keyof typeof audioInfo];
  if (!info) return [];
  const parsed: AiBookAudioTimestamp[] = JSON.parse(info.items);
  const texts: string[] = JSON.parse(info.lyricText);
  const currentItem = parsed[topicIndex];
  const delta = currentItem?.startTime || 0;
  const timestamps =
    currentItem?.smallQuestions.map((val) => {
      return {
        startTime: (val.startTime - delta) / 1000,
        stopTime: (val.stopTime - delta) / 1000,
        title: val.title,
        smallQuestions: [],
        lrcs: [],
      };
    }) || [];

  const textsWithTimestamp = texts.map((text) => {
    const reg = /(\[.*?\])(.*)/g;
    const components = text.split(reg).filter(Boolean);
    const [time = '', content] = components;
    const [minute = 0, second = 0, millisecond = 0] =
      time
        .replaceAll(/[[\]]/g, '')
        .split(':')
        .map((s) => +s) || [];
    const timestamp = minute * 60 + second + millisecond / 100;
    return {
      timestamp: timestamp - delta / 1000,
      text: content || '',
      originalText: text,
      id: uuidv4(),
    };
  });
  return timestamps.map((timestamp, i) => {
    const { startTime, stopTime } = timestamp;
    const lrcs = textsWithTimestamp.filter(
      (lrc) => lrc.timestamp >= startTime && lrc.timestamp < stopTime,
    );
    return {
      ...timestamp,
      lrcs,
    };
  });
};

const getMockType = (
  pageNo: number,
  topic: AiBookTopicItem,
  topicIndex: number,
) => {
  if (pageNo === 1) {
    if (topicIndex === 0) {
      return AiTopicType.Word;
    } else {
      return AiTopicType.Knowledge;
    }
  } else if (pageNo === 6) {
    return AiTopicType.Knowledge;
  } else if (pageNo === 11 || pageNo === 12 || pageNo === 13) {
    return AiTopicType.Read;
  } else if (pageNo === 14) {
    return AiTopicType.Write;
  } else if (pageNo === 15 || pageNo === 10) {
    return AiTopicType.Listen;
  }
  return undefined;
};

const getMockLevel = (
  pageNo: number,
  topic: AiBookTopicItem,
  topicIndex: number,
) => {
  const allLevels = [AiTopicLevel.Easy, AiTopicLevel.Medium, AiTopicLevel.Hard];
  const index = topicIndex % allLevels.length;
  return allLevels[index];
};

export const useFetchAiBookData = () => {
  const [book, setAiBook] = useAtom(atomMap.aiBook);
  const setAiBookInfo = useSetAtom(atomMap.aiBookInfo);
  const { answerResult } = useTakePhotoResult({
    type: 'book',
    autoFetch: true,
  });

  const [aiBookTakePhotoResult, setAiBookTakePhotoResult] = useAtom(
    atomMap.aiBookTakePhotoResult,
  );
  const setAiBookMockData = useSetAtom(atomMap.aiBookMockData);

  const preProcessBook = useCallback(
    async (jsonData: AiBookDto) => {
      const { bookId, bookItems = [], bookName } = jsonData;
      const pages: AiBookPage[] = bookItems.map((item) => {
        const { catalogName, pageNo = 0, pageId, pageUrl, items = [] } = item;
        const topics: AiBookTopicItem[] = items.map((topic, topicIndex) => {
          const { itemId, itemUrl, video_info = [], coord, words = [] } = topic;
          const newWords: AiBookWordItem[] = words.map((word, index) => {
            const { text } = word;
            return {
              text,
              id: `${pageId}-${itemId}-${index}`,
              ...mapCoordToRect(word.coord),
            };
          });
          const baseTopicData = {
            itemId,
            pageId,
            itemUrl,
            videoUrl: video_info[0]?.url,
            type: getMockType(pageNo, topic, topicIndex),
            audioUrl: topic.audioUrl,
            words: newWords,
            title: getSectionTitle(pageNo || 0, topicIndex),
            audioTimestamps: getAudioTimestamps(pageNo, topicIndex),
            level: getMockLevel(pageNo, topic, topicIndex),
            pageNo,
            time:
              new Date('2024-12-01').getTime() +
              Math.floor(
                Math.random() *
                  (new Date('2025-06-01').getTime() -
                    new Date('2024-12-01').getTime()),
              ),
            ...mapCoordToRect(coord),
          };

          if (topic.answer_analysis) {
            const answer_analysis = topic.answer_analysis;
            return {
              ...baseTopicData,
              answerAnalysis: {
                markdown: answer_analysis.markdown,
                ttsUrls: answer_analysis.tts_urls,
                srtUrls: answer_analysis.srt_urls,
              },
            };
          } else {
            return baseTopicData;
          }
        });
        return {
          bookId,
          catalogName,
          pageId,
          pageNo,
          pageUrl,
          topics,
        };
      });
      const result: AiBook = {
        bookId,
        bookName,
        pages,
      };
      setAiBook(result);

      setAiBookInfo((prev) => {
        if (prev.bookId) {
          return prev;
        } else {
          return {
            bookId: result?.bookId,
            pageId: result?.pages?.[0]?.pageId,
            topicId: undefined,
          };
        }
      });
    },
    [setAiBook, setAiBookInfo],
  );

  const [isLoading, setIsLoading] = useState(false);
  const fetchData = useCallback(async () => {
    setIsLoading(true);

    const resp = await container.net().fetch({
      url: '/appConfig/getAppConf.do',
      data: {
        confCode: 'listening_practice_file_url',
      },
    });
    const { data: respData } = resp;
    if (respData) {
      const response = await fetch(respData as string);
      const jsonData = await response.json();
      preProcessBook(jsonData);
    } else {
      preProcessBook(mockJson);
    }

    setIsLoading(false);
  }, [preProcessBook]);

  useEffect(() => {
    if (!book) {
      fetchData();
    }
  }, [fetchData, book]);

  useEffect(() => {
    let _rightCount = 0;
    let _wrongCount = 0;
    answerResult.forEach((item) => {
      _rightCount += item.rightNum || 0;
      _wrongCount += item.wrongNum || 0;
    });

    const results = answerResult.filter((item) => (item.wrongNum || 0) > 0);
    setAiBookTakePhotoResult((prev) => {
      return {
        ...prev,
        answerResults: answerResult,
        wrongResults: results,
        totalCount: _rightCount + _wrongCount,
        rightCount: _rightCount,
        wrongCount: _wrongCount,
      };
    });
  }, [answerResult, book, setAiBookMockData, setAiBookTakePhotoResult]);

  useEffect(() => {
    if (!book) return;
    const mockCollectionTopics = book?.pages
      ?.flatMap((page) => {
        return page.topics;
      })
      .filter((topic, index) => index % 3 === 1) as AiBookTopicItem[];
    const topics = book?.pages
      ?.flatMap((page) => {
        return page.topics;
      })
      .filter((topic) => {
        return (
          topic?.type !== AiTopicType.Knowledge &&
          topic?.type !== AiTopicType.Word
        );
      }) as AiBookTopicItem[];

    const wrongMap = new Map<number | undefined, TakePhotoAnswerResult>();
    aiBookTakePhotoResult.wrongResults.forEach((item) =>
      wrongMap.set(item.itemId, item),
    );
    const wrongTopics = book?.pages
      ?.flatMap((item) => item.topics || [])
      .filter((topic) => {
        return wrongMap.has(topic.itemId);
      })
      .map((item) => {
        const result = wrongMap.get(item.itemId);
        return {
          ...item,
          takePhotoWrongResult: result,
        };
      });

    setAiBookMockData((prev) => {
      return {
        ...prev,
        topics,
        wrongTopics: wrongTopics || [],
        collectionTopics: mockCollectionTopics,
      };
    });
  }, [aiBookTakePhotoResult.wrongResults, book, setAiBookMockData]);

  return {
    isLoading,
    book,
  };
};
