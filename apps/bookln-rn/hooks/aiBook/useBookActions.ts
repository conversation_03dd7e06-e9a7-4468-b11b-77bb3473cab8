import { PermissionEnum, PermissionHooks, PermissionPurposeScene } from '@bookln/permission';
import { useBizRouter } from '@jgl/biz-func';
import { router } from '@jgl/utils';
import { useAtomValue } from 'jotai';
import { useCallback } from 'react';
import { routerMap } from '../../utils/routerMap';
import { navigateToWebBook } from '../../utils/WebViewHelper';
import { atomMap } from '../../atom';

export const useBookActions = () => {
  const { checkAndRequestPermission } = PermissionHooks.usePermission();
  const aiBookInfo = useAtomValue(atomMap.aiBookInfo);
  const { bookId } = aiBookInfo;

  const takePhotoAction = useCallback(async () => {
    const result = await checkAndRequestPermission({
      permission: PermissionEnum.Camera,
      scene: PermissionPurposeScene.AiQA,
    });
    if (result) {
      router.push(routerMap.AiBookTakePhoto);
    }
  }, [checkAndRequestPermission]);

  const bookResourceAction = useCallback(async () => {
    navigateToWebBook({ bookId: bookId?.toString() || '' });
  }, [bookId]);

  const bizRouter = useBizRouter();

  const wordEliminateAction = useCallback(async () => {
    bizRouter.push(routerMap.WordEliminate);
  }, [bizRouter]);

  return {
    takePhotoAction,
    bookResourceAction,
    wordEliminateAction,
  };
};
