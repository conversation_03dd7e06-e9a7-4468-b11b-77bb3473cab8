import { useBookAISendMessage } from '@jgl/ai-qa-v2';
import { router } from '@jgl/utils';
import { useAtom, useAtomValue } from 'jotai';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type {
  FlatList,
  NativeScrollEvent,
  NativeSyntheticEvent,
} from 'react-native';
import { Dimensions } from 'react-native';
import { atomMap } from '../../atom';
import type { AiBookWordItem } from './bookData.type';

export const useAiBookTopic = () => {
  const [aiBookInfo, setAiBookInfo] = useAtom(atomMap.aiBookInfo);
  const { pageId, topicId, bookId } = aiBookInfo;
  const aiBook = useAtomValue(atomMap.aiBook);

  const allTopics = useMemo(() => {
    return aiBook?.pages?.flatMap((page) => page.topics || []) || [];
  }, [aiBook?.pages]);

  const cellWidth = useMemo(() => Dimensions.get('window').width, []);
  const [showWordRecognition, setShowWordRecognition] = useState(false);
  const [currentWord, setCurrentWord] = useState<AiBookWordItem>();
  const { sendMessage } = useBookAISendMessage();

  const [topicTableScrollEnabled, setTopicTableScrollEnabled] =
    useState<boolean>(true);

  const topicTableRef = useRef<FlatList>(null);
  const tableViewIndexRef = useRef(-1);

  const currentTopicIndex = useMemo(() => {
    return allTopics.findIndex((item) => item.itemId === topicId);
  }, [allTopics, topicId]);

  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const offsetX = event.nativeEvent.contentOffset.x;
      const index = Math.round(offsetX / cellWidth);
      const topic = allTopics[index];

      if (index !== tableViewIndexRef.current) {
        tableViewIndexRef.current = index;

        setAiBookInfo({
          pageId: topic?.pageId,
          topicId: topic?.itemId,
          bookId,
        });
      }
    },
    [allTopics, bookId, cellWidth, setAiBookInfo],
  );

  useEffect(() => {
    if (
      topicTableRef.current &&
      tableViewIndexRef.current !== currentTopicIndex
    ) {
      tableViewIndexRef.current = currentTopicIndex ?? 0;
      if (currentTopicIndex !== -1) {
        topicTableRef.current.scrollToIndex({
          index: currentTopicIndex ?? 0,
          animated: true,
        });
      }
    }
  }, [currentTopicIndex]);

  const handleBack = useCallback(() => {
    router.back();
  }, []);

  const handleHint = () => {
    sendMessage({
      textContent: '来一点提示',
      bizData: JSON.stringify({
        bookId: bookId,
        pageId: pageId,
        itemId: topicId,
        rewriteUserMsg: true,
        bizType: 'book',
      }),
    });
  };

  const handleWordRecognition = () => {
    setShowWordRecognition(!showWordRecognition);
  };

  const handlePressWord = useCallback((word: AiBookWordItem) => {
    setCurrentWord(word);
  }, []);

  const handleScrollToNextItem = useCallback(() => {
    const total = allTopics.length || 0;
    const current = currentTopicIndex || 0;
    if (current + 1 < total) {
      setTopicTableScrollEnabled(true);
      topicTableRef.current?.scrollToIndex({
        index: current + 1,
        animated: true,
      });
      return true;
    }
    return false;
  }, [allTopics.length, currentTopicIndex]);

  const handleScrollToPrevItem = useCallback(() => {
    const current = currentTopicIndex || 0;
    if (current - 1 >= 0) {
      setTopicTableScrollEnabled(true);
      topicTableRef.current?.scrollToIndex({
        index: current - 1,
        animated: true,
      });
      return true;
    }
    return false;
  }, [currentTopicIndex]);

  return {
    allTopics,
    showWordRecognition,
    currentWord,
    cellWidth,
    handleScroll,
    currentTopicIndex,
    topicTableRef,
    handleBack,
    // handleHint,
    // handleWordRecognition,
    handlePressWord,
    setCurrentWord,
    handleScrollToNextItem,
    handleScrollToPrevItem,
    topicTableScrollEnabled,
    setTopicTableScrollEnabled,
  };
};
