import { container } from '@jgl/container';
import {
  UsersApiUpdateUserInfoParams,
  usersApiUpdateUserInfo,
  usersApiUserInfo,
} from '@yunti-private/api-xingdeng-boot';
import { useAtom } from 'jotai';
import { useCallback } from 'react';
import { atomMap } from '../atom';

/**
 * <AUTHOR>
 * @date 2024/01/17
 * @description 用户资料 hook
 */
export const useProfile = () => {
  const [profile, setProfile] = useAtom(atomMap.profileAtom);

  const loadProfile = useCallback(async () => {
    const request = usersApiUserInfo();
    const response = await container.net().fetch(request);
    if (response.success && response.data) {
      setProfile(response.data);
    }
  }, [setProfile]);

  const updateProfile = useCallback(
    async (param: UsersApiUpdateUserInfoParams) => {
      const request = usersApiUpdateUserInfo(param);
      const response = await container.net().fetch(request);
      if (response.success && response.data?.result === 'true') {
        await loadProfile();
        return true;
      } else {
        return false;
      }
    },
    [loadProfile],
  );

  return {
    profile,
    loadProfile,
    updateProfile,
  };
};
