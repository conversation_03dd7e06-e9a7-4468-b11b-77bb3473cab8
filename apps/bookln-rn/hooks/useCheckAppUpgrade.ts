import { agreementStateAtom } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { envVars, showToast, storage } from '@jgl/utils';
import { useAtomValue, useSetAtom } from 'jotai';
import { useCallback, useEffect } from 'react';
import { NativeModules, Platform } from 'react-native';
import { miniappversionV2 } from '../api/SystemApi';
import { atomMap } from '../atom';
import type { AppVersionDTO, MarketDTO } from '../dtos/AppVersion';
import { StrategyType } from '../upgrade/Const';
import { getUpgradeTipKey } from '../upgrade/UpgradeUtils';

type CheckVersionParams = { sysCheck?: boolean; tempVersion?: string };

export const useCheckAppUpgrade = (params: { isActive: boolean }) => {
  const { isActive } = params;

  const updateAppVersion = useSetAtom(atomMap.checkedAppVersionResultAtom);

  const agreementState = useAtomValue(agreementStateAtom);

  const checkAndroid = useCallback(
    async (sysCheck: boolean, appVersionDTO: AppVersionDTO, market?: MarketDTO) => {
      let downloadStatus = null;
      try {
        downloadStatus =
          await NativeModules.BooklnNativeHelper.queryApkDownloadStatusAndReportProgress();
      } catch (e) {}
      const { name = '', strategy } = appVersionDTO;
      switch (strategy) {
        case StrategyType.ForceUpgrade: // 强制更新
          updateAppVersion({
            appVersionDTO,
            downloadStatus,
            sysCheck,
            market,
            isShowUpgradeModal: true,
          });
          break;
        case StrategyType.SilentUpgrade: // 静默更新
          if (sysCheck === false) {
            updateAppVersion({
              appVersionDTO,
              downloadStatus,
              sysCheck,
              market,
              isShowUpgradeModal: true,
            });
          } else {
            // 红点提示
          }
          break;
        default:
          if (sysCheck) {
            const tipOrNot = await storage.getItem(getUpgradeTipKey(name));
            if (tipOrNot === 'false') {
              return;
            }
          }
          updateAppVersion({
            appVersionDTO,
            downloadStatus,
            sysCheck,
            market,
            isShowUpgradeModal: true,
          });
          break;
      }
    },
    [updateAppVersion],
  );

  const fetchData = useCallback(
    async (checkVersionParams?: CheckVersionParams) => {
      const { sysCheck = false, tempVersion = null } = checkVersionParams ?? {};

      const version = envVars.appVersion();

      const info = miniappversionV2({
        market: '',
        originVersion: tempVersion || version || '',
      });
      const resp = await container.net().fetch(info);
      if (resp.success) {
        const { data } = resp;
        const { data: appVersionDTO } = data || {};
        // 没有新版本
        if (data == null || !appVersionDTO) {
          if (!sysCheck) {
            showToast({ title: '当前已经是最新版本' });
          }
          return;
        }
        checkAndroid(sysCheck, appVersionDTO);
      }
    },
    [checkAndroid],
  );

  const startCheckVersion = useCallback(
    (checkVersionParams?: CheckVersionParams) => {
      fetchData(checkVersionParams);
    },
    [fetchData],
  );

  const sysCheckVersion = useCallback(() => {
    startCheckVersion({ sysCheck: true });
  }, [startCheckVersion]);

  useEffect(() => {
    if (isActive && Platform.OS === 'android' && agreementState === 'agreed') {
      sysCheckVersion();
    }
  }, [isActive, agreementState, sysCheckVersion]);

  const checkVersion = useCallback(() => {
    startCheckVersion({ sysCheck: false });
  }, [startCheckVersion]);

  return { checkVersion };
};
