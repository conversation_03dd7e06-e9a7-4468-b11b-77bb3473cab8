import { useCallback, useState } from 'react';
import { View } from 'tamagui';
import ImageView from 'react-native-image-viewing';

/**
 * 图片预览
 */
export const useImagePreview = () => {
  const [visible, setVisible] = useState(false);
  const [images, setImages] = useState<Record<string, string>[]>([]);

  const show = useCallback((imageUrls: string[]) => {
    setImages(imageUrls.map((url) => ({ uri: url })));
    setVisible(true);
  }, []);

  const hide = useCallback(() => {
    setVisible(false);
    setImages([]);
  }, []);

  const PreviewComponent = useCallback(
    () => (
      <View>
        <ImageView
          images={images}
          imageIndex={0}
          visible={visible}
          onRequestClose={hide}
        />
      </View>
    ),
    [visible, images, hide],
  );

  return {
    showImagePreview: show,
    ImagePreviewerComponent: PreviewComponent,
  };
};
