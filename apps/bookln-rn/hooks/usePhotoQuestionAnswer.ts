import type { AnalysisImageParams } from '@jgl/biz-components';
import { learnSceneApiCutQuestion, type PhotoCutRectDTO, PhotoProgress } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { router } from '@jgl/utils';
import { useCallback, useState } from 'react';
import { v4 } from 'uuid';

export const usePhotoQuestionAnswer = () => {
  const [progress, setProgress] = useState<PhotoProgress>(PhotoProgress.Photo);
  const [imageUrl, setImageUrl] = useState<string | undefined>();
  const [loading, setLoading] = useState(false);
  // 切题失败
  const [error, setError] = useState<string | undefined>();

  /**
   * 切题坐标数组
   */
  const [boxList, setBoxList] = useState<Array<PhotoCutRectDTO> | undefined>();

  const cutExam = useCallback(async (url?: string) => {
    if (url) {
      setLoading(true);
      const res = await container
        .net()
        .fetch(learnSceneApiCutQuestion({ image: url }))
        .finally(() => {
          setLoading(false);
        });
      console.log('🚀 ~ async ~ res:', JSON.stringify(res));
      // @ts-ignore 类型未定义，接口已返回
      if (res.success && res.data?.boxs) {
        // @ts-ignore
        let newBoxList = res.data?.boxs.map((item) => ({
          uuid: v4(),
          rect: item,
        })) as Array<PhotoCutRectDTO>;
        // res.data?.boxs 按照y轴排序
        newBoxList = newBoxList.sort((a, b) => a.rect[1] - b.rect[1]);
        setBoxList(newBoxList);
        // @ts-ignore
        setImageUrl(res?.data?.correctionDirectionImg);
      } else {
        setError(res.msg || '暂未识别出题目');
      }
    }
  }, []);

  /**
   * 解析图片
   * @param {string} imageUrl - 图片的网络链接
   */
  const onAnalysisImage = useCallback(
    async (args: AnalysisImageParams) => {
      /**
       * 切题传参，可传base64或者网络链接，不能传临时路径
       */
      const cutExamUrl = args?.base64 || args?.imageUrl;

      /**
       * 图片展示，可使用临时路径或者网络链接，不能使用base64
       */
      const showUrl = args?.tempFilePath || args?.imageUrl;
      setError(undefined);
      setProgress(PhotoProgress.Result);
      setImageUrl(showUrl);
      cutExam(cutExamUrl);
    },
    [cutExam],
  );

  /**
   * 更新当前步骤
   * @param {PhotoProgress} args - 需要更新的步骤
   */
  const updateProgress = useCallback(
    (args: PhotoProgress) => {
      if (args === PhotoProgress.Photo) {
        setError(undefined);
        setBoxList(undefined);
        setLoading(false);
        setImageUrl(undefined);
      }
      if (progress !== args) {
        setProgress(args);
      }
    },
    [progress],
  );

  /**
   * 点击拍照按钮
   */
  const onPressTakePhoto = useCallback(() => {
    setProgress(PhotoProgress.Photo);
    setError(undefined);
  }, []);

  /**
   * 点击返回按钮
   */
  const onPressBack = useCallback(() => {
    router.back();
  }, []);

  return {
    imageUrl,
    progress,
    boxList,
    loading,
    error,
    onAnalysisImage,
    updateProgress,
    onPressTakePhoto,
    onPressBack,
  };
};
