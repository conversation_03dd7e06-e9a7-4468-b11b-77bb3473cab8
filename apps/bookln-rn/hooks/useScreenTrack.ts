import { useGlobalSearchParams, usePathname } from 'expo-router';
import { useEffect } from 'react';

export const useScreenTrack = () => {
  const pathname = usePathname();
  const params = useGlobalSearchParams();

  useEffect(() => {
    // analytics.track({ pathname, params });

    if (__DEV__) {
      console.log('路由跳转');
      console.log('pathname: ', pathname);
      console.log('params: ', JSON.stringify(params));
    }
  }, [pathname, params]);
};
