import {
  isPlatform,
  routerMap,
  useBizRouter,
  useIsUuidUser,
} from '@jgl/biz-func';

export const useNeedLoginInAndroid = () => {
  const isUuidUser = useIsUuidUser();
  const isAndroid = isPlatform({
    os: 'android',
    runtime: 'rn',
  });
  const bizRouter = useBizRouter();

  /* 安卓用户未登录时点击加锁的场景需要提示登录 */
  const goToLogin = async () => {
    if (isUuidUser && isAndroid) {
      await bizRouter.push(routerMap.logInModal);
      return false;
    }
    return true;
  };

  return { goToLogin };
};
