import { agreementStateAtom } from '@jgl/biz-func';
import { router } from 'expo-router';
import { useAtom } from 'jotai';
import { useCallback } from 'react';
import { Alert } from 'react-native';
import { useFetchConfig } from './useFetchConfig';
import { useLogIn } from './useLogIn';

export const useSetAgreementState = () => {
  // TODO: aini - iOS track弹窗？因为有驰声SDK？
  // TODO: aini - 隐私增加驰声SDK

  const [agreementState, setAgreementState] = useAtom(agreementStateAtom);
  const { logInUuid } = useLogIn();

  const { fetchConfig } = useFetchConfig();

  const agree = useCallback(async () => {
    await setAgreementState('isAgreeing');

    const { logInSuccess } = await logInUuid();
    const fetchConfigSuccess = await fetchConfig();
    if (logInSuccess && fetchConfigSuccess) {
      // 登录成功且获取配置成功，进行下一步
      setAgreementState('agreed');
    } else {
      setAgreementState('undetermined');
    }
  }, [fetchConfig, logInUuid, setAgreementState]);

  const disagree = useCallback(async () => {
    if (agreementState === 'disagreed') {
      router.canGoBack() && router.back();
    } else {
      Alert.alert(
        '',
        '未同意协议仅可使用浏览模式',
        [
          {
            text: '取消',
            style: 'cancel',
          },
          {
            text: '进入浏览模式 ',
            onPress: async () => {
              setAgreementState('isDisagreeing');

              const { logInSuccess } = await logInUuid();
              if (logInSuccess) {
                setAgreementState('disagreed');
              } else {
                setAgreementState('undetermined');
              }
            },
            style: 'default',
          },
        ],
        { cancelable: false },
      );
    }
  }, [agreementState, logInUuid, setAgreementState]);

  return { agree, disagree };
};
