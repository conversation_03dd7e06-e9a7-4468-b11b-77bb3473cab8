import { MemoryLogger } from '@yunti-private/rn-memory-logger';
import { useStartNetworkLogIfNeeded } from '../utils/networkLogUtil';
import { useIgnoreLogBox } from './useIgnoreLogBox';
import { useLoadFonts } from './useLoadFonts';
import { useSplashScreen } from './useSplashScreen';

/**
 * 在渲染UI之前的一些初始化工作
 */
export const useInitBeforeRender = () => {
  // 忽略日志
  useIgnoreLogBox();
  // 开启网络请求log
  useStartNetworkLogIfNeeded();
  // 加载字体
  useLoadFonts();
  // 处理开屏页的隐藏逻辑
  useSplashScreen();

  // TODO: leejunhui - 为了定位浏览模式下的报错 (2025_05_14)
  MemoryLogger.enable(true);
};
