import { tamaguiThemeNameAtom } from '@jgl/biz-func';
import {
  DefaultTheme,
  Theme as NavigationTheme,
} from '@react-navigation/native';
import { useAtomValue } from 'jotai';
import { useTheme as useOriginalTamaguiTheme } from 'tamagui';

export const useNavigationTheme = (): NavigationTheme => {
  const tamaguiThemeName = useAtomValue(tamaguiThemeNameAtom);
  const tamaguiTheme = useOriginalTamaguiTheme();

  if (tamaguiThemeName) {
    const navigationTheme: NavigationTheme = {
      dark: false,
      colors: {
        // TODO: aini - 颜色不一定合适，待调整
        primary: tamaguiTheme.color9.val,
        background: tamaguiTheme.backgroundStrong.val,
        card: tamaguiTheme.backgroundStrong.val,
        text: tamaguiTheme.color12.val,
        border: tamaguiTheme.borderColor.val,
        notification: tamaguiTheme.red10.val,
      },
    };
    return navigationTheme;
  } else {
    return DefaultTheme;
  }
};

/**
 * tamagui useTheme 的替代。
 *
 * 没有选择tamagui主题的时候使用默认值，避免tamagui默认值生效导致UI异常。
 */
export const useTamaguiTheme = ():
  | ReturnType<typeof useOriginalTamaguiTheme>
  | undefined => {
  const tamaguiThemeName = useAtomValue(tamaguiThemeNameAtom);
  const tamaguiTheme = useOriginalTamaguiTheme();
  if (tamaguiThemeName) {
    return tamaguiTheme;
  } else {
    return undefined;
  }
};
