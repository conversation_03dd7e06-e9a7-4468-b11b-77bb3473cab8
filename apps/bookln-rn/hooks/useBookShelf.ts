import { withLogin } from '@bookln/bookln-biz';
import { PermissionEnum, PermissionHooks, PermissionPurposeScene } from '@bookln/permission';
import { envVars, router } from '@jgl/utils';
import { useAtomValue } from 'jotai';
import { useCallback } from 'react';
import { bookShelfAtom } from '../atom';
import { ScanType } from '../types';
import { routerMap } from '../utils/routerMap';

/**
 * 书架
 */
export const useBookShelf = () => {
  const bookShelfData = useAtomValue(bookShelfAtom);

  const { checkAndRequestPermission } = PermissionHooks.usePermission();

  /**
   * 扫码
   */
  const onPressScan = useCallback(() => {
    checkAndRequestPermission({
      permission: PermissionEnum.Camera,
      scene: PermissionPurposeScene.ScanCode,
    }).then((result) => {
      if (result) {
        router.push(routerMap.ScanSearch, { scanType: ScanType.ScanParse });
      }
    });
  }, [checkAndRequestPermission]);

  return {
    onPressScan: envVars.loginLogicVersion() === 'new' ? onPressScan : withLogin(onPressScan),
    bookShelfData,
  };
};
