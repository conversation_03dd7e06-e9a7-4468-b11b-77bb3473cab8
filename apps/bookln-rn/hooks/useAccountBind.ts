import {
  getWeChatAccessInfo,
  useAppSelector,
  useShowWeChatFeatures,
} from '@jgl/biz-func';
import { container } from '@jgl/container';
import { envVars } from '@jgl/utils';
import { usersApiBindWeChat } from '@yunti-private/api-xingdeng-boot';

import { useCallback, useMemo } from 'react';
import Toast from 'react-native-toast-hybrid';
import { useRefreshUserInfo } from './useRefreshUserInfo';

/**
 * 账号绑定
 */
export const useAccountBind = () => {
  const unionId = useAppSelector((s) => s.userInfo.unionId);

  /** 是否绑定微信 */
  const isWeChatBound = useMemo(() => unionId != null, [unionId]);

  const phone = useAppSelector((s) => s.userInfo.phone);
  /** 是否绑定手机号 */
  const isPhoneNumberBound = useMemo(() => phone != null, [phone]);

  const { refreshUserInfo } = useRefreshUserInfo();

  /** 是否显示微信功能 */
  const showWeChatFeatures = useShowWeChatFeatures();

  const bindWeChat = useCallback(async (): Promise<{
    success: boolean;
    msg?: string;
  }> => {
    let toast: Toast | undefined;

    const weChatAccessInfo = await getWeChatAccessInfo({
      onLoadingStart: () => {
        toast = Toast.loading();
      },
    });

    const { unionid: logInUnionId } = weChatAccessInfo ?? {};
    if (logInUnionId) {
      const request = usersApiBindWeChat({
        appId: envVars.appId(),
        unionId: logInUnionId,
      });
      const response = await container.net().fetch(request);
      toast?.hide();
      const { success, msg } = response;
      if (success) {
        await refreshUserInfo();
        return { success: true };
      } else {
        return { success: false, msg };
      }
    }
    toast?.hide();
    return { success: false, msg: '获取微信信息失败' };
  }, [refreshUserInfo]);

  return {
    isWeChatBound,
    isPhoneNumberBound,
    showWeChatFeatures,
    bindWeChat,
  };
};
