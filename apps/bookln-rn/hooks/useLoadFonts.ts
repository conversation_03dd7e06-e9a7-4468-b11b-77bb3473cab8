import { useFonts } from "expo-font";
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useEffect } from "react";
import { useSetAtom } from "jotai";
import { atomMap } from "../atom";

/**
 * 加载字体
 */
export const useLoadFonts = () => {
    const [loaded, error] = useFonts({
      SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
      pinyin: require('../assets/fonts/pinyin.ttf'),
      ...FontAwesome.font,
    });
    const setFontLoadResult = useSetAtom(atomMap.fontLoadResultAtom);
    useEffect(() => {
      if (error) {
        throw error;
      }
      setFontLoadResult({
        success: loaded,
        error: error,
      });
    }, [error, loaded, setFontLoadResult]);
}
