import { SplashScreen } from "expo-router";
import { useAtomValue, useSetAtom } from "jotai";
import { useEffect } from "react";
import { atomMap } from "../atom";

/**
 * 防止开屏页自动隐藏
 */
export const useSplashScreen = () => {
    const { success } = useAtomValue(atomMap.fontLoadResultAtom)
    
    const setSplashScreenHidden = useSetAtom(atomMap.splashScreenHiddenAtom)
    
    useEffect(()=>{
        if(success){
            SplashScreen.hideAsync();
            setSplashScreenHidden(true)
        }
    },[success, setSplashScreenHidden])
}