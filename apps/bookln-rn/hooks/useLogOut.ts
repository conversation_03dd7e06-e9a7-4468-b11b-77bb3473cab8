import { isLoggingOutAtom } from '@jgl/biz-func';
import { useAtom } from 'jotai';
import { useCallback } from 'react';
import { replaceToHome } from '../utils/routerHelper';
import { useLogInByUuidInternal } from './useLogIn';

export const useLogOut = () => {
  const [isLoggingOut] = useAtom(isLoggingOutAtom);
  const { logInByUuidInternal: logInByUuid } = useLogInByUuidInternal();

  const logOut = useCallback(
    async (p?: { backToRoot?: boolean }) => {
      const { backToRoot = true } = p ?? {};

      await logInByUuid({ from: 'logOut' });

      if (backToRoot) {
        replaceToHome();
      }
    },
    [logInByUuid],
  );

  return { isLoggingOut, logOut };
};
