import { agreementStateAtom, logInUuidAtom } from '@jgl/biz-func';
import { useAtomValue } from 'jotai';
import {
  bookShelfAtom,
  currentChannelAtom,
  messageNotificationSwitchAtom,
  personalizedSwitchAtom,
} from '../atom';

/**
 * 初始化存储原子
 * 如果不初始化在第一次使用的时候拿到可能不是storage中储存最新的
 */
export const useInitStorageAtom = () => {
  useAtomValue(currentChannelAtom);
  // 启动时读取是否同意过协议
  useAtomValue(agreementStateAtom);

  // 启动时读取登录uuid
  useAtomValue(logInUuidAtom);

  // 启动时读取书架
  useAtomValue(bookShelfAtom);

  // 启动时读取个性化开关状态
  useAtomValue(personalizedSwitchAtom);

  // 启动时读取消息通知开关状态
  useAtomValue(messageNotificationSwitchAtom);
};
