import { useGetImResultObject, type PhotoCutRectDTO } from '@jgl/biz-func';
import { JglXStack } from '@jgl/ui-v4';
import { useWindowDimensions } from '@yunti-private/jgl-ui';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { View, type FlatList } from 'react-native';
import Modal from 'react-native-modal';
import Animated, {
  type SharedValue,
  useSharedValue,
} from 'react-native-reanimated';
import Carousel, {
  type ICarouselInstance,
} from 'react-native-reanimated-carousel';
import { PhotoQuestionAnswerBoxIndexItem } from './PhotoQuestionAnswerBoxIndexItem';
import { PhotoQuestionAnswerResultItem } from './PhotoQuestionAnswerResultItem';
import type { CacheResultObject } from './hooks/PhotoQuestionAnswerResult.type';

type PhotoQuestionAnswerResultPopupProps = {
  height: number | string;
  closeAndUpdateIndex: (index: number) => void;
  aiLoading?: boolean;
  boxList: PhotoCutRectDTO[];
  resetAiLoading: () => void;
  onPressBack?: () => void;
  onPressTakePhoto?: () => void;
  getCurrentRectImgData: (
    rect: [number, number, number, number],
  ) => Promise<{ data: string; width: number; height: number } | undefined>;
};

export type PhotoQuestionAnswerResultPopupRef = {
  handleShow: (defaultIndex: number) => void;
  handleClose: () => void;
};

const indexItemSize = 34;

/**
 * 拍照答疑结果弹窗
 */
export const PhotoQuestionAnswerResultPopup = forwardRef<
  PhotoQuestionAnswerResultPopupRef,
  PhotoQuestionAnswerResultPopupProps
>((props, ref) => {
  const {
    closeAndUpdateIndex,
    onPressBack,
    onPressTakePhoto,
    boxList,
    getCurrentRectImgData,
  } = props;
  const [currentIndex, setCurrentIndex] = useState<number | undefined>();
  const [isShow, setIsShow] = useState(false);
  const progress = useSharedValue<number>(0);
  const { width } = useWindowDimensions();
  const flatListRef = useRef<FlatList>(null);
  const carouselRef = useRef<ICarouselInstance>(null);

  /**
   * 缓存图片识别结果
   * key: resultId
   * value: UseImResult
   */
  const cacheBoxResultMapRef = useRef<Map<number, CacheResultObject>>(
    new Map(),
  );

  /**
   * 缓存题目区域与resultId的映射
   * key: boxItem.rect生成的唯一key
   * value: resultId
   */
  const cacheBoxResultIdMapRef = useRef<Map<string, number>>(new Map());

  const { getLastResultObject, closeIm } = useGetImResultObject();
  useEffect(() => {
    return () => {
      closeIm();
    };
  }, [closeIm]);

  const willGoBack = useCallback(() => {
    onPressBack?.();
  }, [onPressBack]);

  const willTakePhoto = useCallback(() => {
    onPressTakePhoto?.();
  }, [onPressTakePhoto]);

  const handleShow = useCallback((defaultIndex: number) => {
    setCurrentIndex(defaultIndex);
    setIsShow(true);
    setTimeout(() => {
      flatListRef.current?.scrollToIndex({
        index: defaultIndex,
        animated: true,
        viewOffset: -indexItemSize / 2,
        viewPosition: 0.5,
      });
    }, 300);
  }, []);

  const handleClose = useCallback(() => {
    setCurrentIndex(undefined);
    setIsShow(false);
  }, []);

  useImperativeHandle(ref, () => ({
    handleShow,
    handleClose,
  }));

  const handleIndexPress = useCallback(
    (index: number) => {
      carouselRef.current?.scrollTo({
        count: index - progress.value,
        animated: true,
      });
    },
    [progress.value],
  );

  /**
   * 缓存结果
   */
  const handleCacheResultObject = useCallback((item: CacheResultObject) => {
    const { resultId, boxItem } = item;
    // 缓存结果
    cacheBoxResultMapRef.current.set(resultId, item);
    // 缓存题目区域与resultId的映射
    cacheBoxResultIdMapRef.current.set(boxItem.rect.join(','), resultId);
  }, []);

  /**
   * 渲染题目坐标
   */
  const renderItem = useCallback(
    ({ item, index }: { item: PhotoCutRectDTO; index: number }) => {
      return (
        <PhotoQuestionAnswerBoxIndexItem
          index={index}
          progress={progress}
          item={item}
          indexItemSize={indexItemSize}
          handleIndexPress={handleIndexPress}
        />
      );
    },
    [handleIndexPress, progress],
  );

  /**
   * 获取缓存结果
   */
  const getCacheResultObject = useCallback(
    (key: string) => {
      const cacheResultId = cacheBoxResultIdMapRef.current.get(key);
      if (cacheResultId) {
        const cacheResultObject =
          cacheBoxResultMapRef.current.get(cacheResultId);
        if (cacheResultObject) {
          return { ...cacheResultObject, resultObject: getLastResultObject() };
        }
      }
      return undefined;
    },
    [getLastResultObject],
  );

  const renderCarouselItem = useCallback(
    ({
      item,
      index,
      animationValue,
    }: {
      item: PhotoCutRectDTO;
      index: number;
      animationValue: SharedValue<number>;
    }) => {
      return (
        <PhotoQuestionAnswerResultItem
          getCurrentRectImgData={getCurrentRectImgData}
          willGoBack={willGoBack}
          willTakePhoto={willTakePhoto}
          getCacheResultObject={getCacheResultObject}
          handleCacheResultObject={handleCacheResultObject}
          index={index}
          activeIndex={currentIndex}
          boxRect={item}
        />
      );
    },
    [
      getCurrentRectImgData,
      willGoBack,
      willTakePhoto,
      getCacheResultObject,
      currentIndex,
      handleCacheResultObject,
    ],
  );

  const handleScrollEnd = useCallback((index: number) => {
    // LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    flatListRef.current?.scrollToIndex({
      index,
      animated: true,
      viewOffset: -indexItemSize / 2,
      viewPosition: 0.5,
    });
    setCurrentIndex(index);
  }, []);

  const renderMultipleResult = useCallback(() => {
    return (
      <Carousel
        autoPlay={false}
        data={boxList}
        loop={false}
        ref={carouselRef}
        pagingEnabled={true}
        snapEnabled={true}
        enabled={boxList.length > 1}
        defaultIndex={currentIndex}
        width={width}
        mode='parallax'
        modeConfig={{
          parallaxScrollingScale: 0.9,
          parallaxScrollingOffset: 44,
        }}
        onConfigurePanGesture={(gestureChain) =>
          gestureChain.activeOffsetX([-40, 40])
        }
        onProgressChange={progress}
        onScrollEnd={handleScrollEnd}
        renderItem={renderCarouselItem}
      />
    );
  }, [
    boxList,
    currentIndex,
    handleScrollEnd,
    progress,
    renderCarouselItem,
    width,
  ]);

  const onBackdropPress = useCallback(() => {
    closeAndUpdateIndex(currentIndex ?? 0);
  }, [closeAndUpdateIndex, currentIndex]);

  const getItemLayout = useCallback(
    (_data: ArrayLike<PhotoCutRectDTO> | null | undefined, index: number) => ({
      length: indexItemSize,
      offset: indexItemSize * index + 10 * index,
      index,
    }),
    [],
  );

  return (
    <Modal
      isVisible={isShow}
      hardwareAccelerated
      backdropOpacity={0.6}
      className='!m-0'
      useNativeDriver
      onBackdropPress={onBackdropPress}
      // 防止 iOS 上 Modal 消失时会闪一下的问题
      // https://github.com/react-native-modal/react-native-modal/issues/268
      hideModalContentWhileAnimating
    >
      <View className='h-[85%] max-h-[600px]'>
        {boxList.length > 1 && (
          <JglXStack zIndex={100} mb={-20}>
            <Animated.FlatList
              horizontal
              ref={flatListRef}
              renderItem={renderItem}
              getItemLayout={getItemLayout}
              showsHorizontalScrollIndicator={false}
              ItemSeparatorComponent={() => <View style={{ width: 10 }} />}
              data={boxList}
              contentContainerStyle={{
                paddingHorizontal: 22,
                flexGrow: 1,
                justifyContent: 'center',
              }}
            />
          </JglXStack>
        )}
        <View className='flex flex-1'>{renderMultipleResult()}</View>
      </View>
    </Modal>
  );
});
