import { AcIcon } from '@jgl/icon/src';
import { JglTouchable, JglView } from '@jgl/ui-v4';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type { GestureResponderEvent } from 'react-native';
import { ClipPath, Defs, Rect, Svg, Image as SvgImage } from 'react-native-svg';
import { Image, View } from 'tamagui';

type DragRectViewProps = {
  rect: [number, number, number, number] | undefined;
  getBoundRect: () => {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  height: number;
  width: number;
  rectIndex?: number;
  onPress: () => void;
  imageUrl: string;
  // handleUpdateCurrentRect: (rect: [number, number, number, number]) => void;
  onDragEnd: (currentRect: [number, number, number, number]) => void;
};

export type DragCornerPosition =
  | 'topLeft'
  | 'topRight'
  | 'bottomRight'
  | 'bottomLeft';

interface DragCornerProps {
  position: DragCornerPosition;
  onDrag: (
    event: GestureResponderEvent,
    position: DragCornerPosition,
    dx: number,
    dy: number,
  ) => void;
  rotation: number;

  onTouchStart: () => void;
  onTouchEnd: () => void;
}

/**
 * 拖拽角
 */
const DragCorner: React.FC<DragCornerProps> = ({
  position,
  onDrag,
  rotation,
  onTouchStart,
  onTouchEnd,
}) => {
  const rects = useRef({ x: 0, y: 0 });

  const handleTouchMove = useCallback(
    (event: GestureResponderEvent) => {
      event.stopPropagation();
      const { touches } = event.nativeEvent;
      const yy = touches[0]?.pageY ?? 0;
      const xx = touches[0]?.pageX ?? 0;
      const dx = xx - rects.current.x;
      const dy = yy - rects.current.y;
      onDrag(event, position, dx, dy);
    },
    [onDrag, position],
  );

  const handleTouchStart = useCallback(
    (event: GestureResponderEvent) => {
      event.stopPropagation();
      const { touches } = event.nativeEvent;
      rects.current.x = touches[0]?.pageX ?? 0;
      rects.current.y = touches[0]?.pageY ?? 0;
      onTouchStart();
    },
    [onTouchStart],
  );

  const handleTouchEnd = useCallback(
    (event: GestureResponderEvent) => {
      event.stopPropagation();
      onTouchEnd();
    },
    [onTouchEnd],
  );

  return (
    <JglView
      pos='absolute'
      h={22}
      w={22}
      top={position.includes('top') ? -4 : undefined}
      left={position.includes('Left') ? -7 : undefined}
      bottom={position.includes('bottom') ? -10 : undefined}
      right={position.includes('Right') ? -7 : undefined}
    >
      <View
        className={'flex-center'}
        onTouchMove={handleTouchMove}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
      >
        <Image
          source={{ uri: AcIcon.IcRectangle }}
          w={14}
          h={14}
          style={{ transform: `rotate(${rotation}deg)` }}
        />
      </View>
    </JglView>
  );
};

const offset = 32; // 拖拽偏移量

const defaultRect: [number, number, number, number] = [0, 0, 0, 0];

/**
 * 拖拽矩形
 */
export const DragRectView = (props: DragRectViewProps) => {
  const {
    rect,
    getBoundRect,
    onPress,
    onDragEnd,
    rectIndex,
    imageUrl,
    width: coverImgWidth,
    height: coverImgHeight,
  } = props;

  const boundRectRef = useRef({ top: 0, bottom: 0, left: 0, right: 0 });

  const [currentRect, setCurrentRect] =
    useState<[number, number, number, number]>(defaultRect);

  /**
   * 是否为空矩形
   */
  const isEmptyRect = useMemo(
    () =>
      currentRect[0] === 0 &&
      currentRect[1] === 0 &&
      currentRect[2] === 0 &&
      currentRect[3] === 0,
    [currentRect],
  );

  useEffect(() => {
    // 使用深度比较或检查数组的每个元素，而不是直接比较数组引用
    setCurrentRect(rect ?? defaultRect);
  }, [rect]);

  const [x1 = 0, y1 = 0, x2 = 0, y2 = 0] = currentRect;

  const currentRectRef = useRef({ x1, y1, x2, y2 });

  const handleCornerDrag = useCallback(
    (
      event: GestureResponderEvent,
      position: string,
      dx: number,
      dy: number,
    ) => {
      const { top, bottom, left, right } = boundRectRef.current;
      const { x1: sx1, y1: sy1, x2: sx2, y2: sy2 } = currentRectRef.current;
      // 根据不同角更新尺寸和位置
      switch (position) {
        case 'topLeft': {
          // 更新x1和y1，x1必须大于left 小于x2-32，y1必须大于top 小于y2-32
          const cx1 = Math.min(Math.max(left, sx1 + dx), sx2 - offset);
          const cy1 = Math.min(Math.max(top, sy1 + dy), sy2 - offset);
          setCurrentRect([cx1, cy1, sx2, sy2]);
          // handleUpdateCurrentRect([cx1, cy1, sx2, sy2]);
          break;
        }
        case 'topRight': {
          // 更新x2和y1，x2必须大于x1+32 小于right，y1必须大于top 小于y2-32
          const cx2 = Math.min(Math.max(sx1 + offset, sx2 + dx), right);
          const cy1 = Math.min(Math.max(top, sy1 + dy), sy2 - offset);
          setCurrentRect([sx1, cy1, cx2, sy2]);
          // handleUpdateCurrentRect([sx1, cy1, cx2, sy2]);
          break;
        }
        case 'bottomRight': {
          // 更新x2和y2，x2必须大于x1+32 小于right，y2必须大于y1+32 小于bottom
          const cx2 = Math.min(Math.max(sx1 + offset, sx2 + dx), right);
          const cy2 = Math.min(Math.max(sy1 + offset, sy2 + dy), bottom);

          setCurrentRect([sx1, sy1, cx2, cy2]);
          // handleUpdateCurrentRect([sx1, sy1, cx2, cy2]);
          break;
        }
        case 'bottomLeft': {
          // 更新x1和y2，x1必须大于left 小于x2-32，y2必须大于y1+32 小于bottom
          const cx1 = Math.min(Math.max(left, sx1 + dx), sx2 - offset);
          const cy2 = Math.min(Math.max(sy1 + offset, sy2 + dy), bottom);
          setCurrentRect([cx1, sy1, sx2, cy2]);
          // handleUpdateCurrentRect([cx1, sy1, sx2, cy2]);
          break;
        }
      }
    },
    [],
  );

  const handleTouchStart = useCallback(() => {
    boundRectRef.current = getBoundRect();
    currentRectRef.current = { x1, y1, x2, y2 };
  }, [getBoundRect, x1, x2, y1, y2]);

  const handleTouchEnd = useCallback(() => {
    onDragEnd(currentRect);
  }, [onDragEnd, currentRect]);

  const cornerConfig: Record<DragCornerPosition, { rotation: number }> =
    useMemo(() => {
      return {
        topLeft: {
          rotation: 0,
        },
        topRight: {
          rotation: 90,
        },
        bottomRight: {
          rotation: 180,
        },
        bottomLeft: {
          rotation: 270,
        },
      };
    }, []);

  /**
   * 计算矩形的位置和尺寸
   */
  const left = useMemo(() => {
    return x1 - 1;
  }, [x1]);

  /**
   * 计算矩形的位置和尺寸
   */
  const top = useMemo(() => {
    return y1 - 1;
  }, [y1]);

  /**
   * 计算矩形的位置和尺寸
   */
  const width = useMemo(() => {
    return x2 - x1 + 2;
  }, [x1, x2]);

  /**
   * 计算矩形的位置和尺寸
   */
  const height = useMemo(() => {
    return y2 - y1 + 2;
  }, [y1, y2]);

  const renderSelectedRect = useMemo(() => {
    if (isEmptyRect) {
      return null;
    }
    const [rx1 = 0, ry1 = 0, rx2 = 0, ry2 = 0] = currentRect || [];
    const rectWidth = rx2 - rx1;
    const rectHeight = ry2 - ry1;

    return (
      <View
        pointerEvents='none'
        style={{ position: 'absolute', left: 0, top: 0, right: 0, bottom: 0 }}
      >
        <Svg
          key={`selected-rect-${currentRect.toString()}`}
          width={coverImgWidth}
          height={coverImgHeight}
        >
          <Defs>
            <ClipPath id='clip'>
              {/* 中间镂空区域 */}
              <Rect
                x={x1}
                y={y1}
                width={rectWidth}
                height={rectHeight}
                rx={4}
                ry={4}
              />
            </ClipPath>
          </Defs>
          <SvgImage
            href={{ uri: imageUrl }}
            width={coverImgWidth}
            height={coverImgHeight}
            pointerEvents='none'
            preserveAspectRatio='xMidYMid slice'
            clipPath='url(#clip)'
          />
        </Svg>
      </View>
    );
  }, [
    coverImgHeight,
    isEmptyRect,
    currentRect,
    x1,
    y1,
    imageUrl,
    coverImgWidth,
  ]);

  if (!rect) {
    return null;
  }

  return (
    <>
      {renderSelectedRect}
      <JglView
        jglClassName='absolute border-solid'
        overflow='visible'
        borderColor='white'
        borderRadius={4}
        borderWidth={1}
        zIndex={1000}
        style={{
          left,
          top,
          width,
          height,
          overflow: 'visible',
        }}
      >
        <JglTouchable
          pos='absolute'
          left={0}
          top={0}
          right={0}
          bottom={0}
          minH={0}
          minW={0}
          onPress={onPress}
        />
        {Object.entries(cornerConfig).map(([position, config]) => (
          <DragCorner
            key={position}
            position={position as DragCornerPosition}
            onDrag={handleCornerDrag}
            rotation={config.rotation}
            onTouchStart={handleTouchStart}
            onTouchEnd={handleTouchEnd}
          />
        ))}
      </JglView>
    </>
  );
};
