import { AppConfigCode, getBooklnServiceConfig } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { atom, useAtom } from 'jotai';
import { useEffect, useMemo } from 'react';

const appConfigAtom = atom<{
  [k in keyof typeof AppConfigCode]?: string;
}>({});

/**
 * 获取应用配置
 * @param configCode 配置码
 * @returns 配置值
 */
export const useAppConfig = (configCode: keyof typeof AppConfigCode) => {
  const [config, setConfig] = useAtom(appConfigAtom);

  const targetConfigResult = useMemo(() => {
    if (config) {
      return config[configCode] ?? '{}';
    }
    return '{}';
  }, [config, configCode]);

  useEffect(() => {
    if (!config?.[configCode]) {
      container
        .net()
        .fetch(getBooklnServiceConfig(AppConfigCode[configCode]))
        .then((res) => {
          const { data, success } = res;
          if (success && data?.result) {
            setConfig({
              ...config,
              [configCode]: data.result,
            });
          }
        });
      setConfig(config);
    }
  }, [config, configCode, setConfig]);

  return targetConfigResult;
};
