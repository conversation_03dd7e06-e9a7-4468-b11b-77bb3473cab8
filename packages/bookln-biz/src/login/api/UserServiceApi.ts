import { AppLoginType } from '@jgl/biz-func';
import type { YTRequest } from '@yunti-private/net';
import type { QueryWxUserInfoDTO } from '../dto/QueryWxUserInfoDTO';
import type { UsersApiLoginDTO } from '../dto/UsersApiLoginDTO';
import type { LoginAndBindMobileParams, UsersApiLoginParams } from '../type/ApiFetcherParams.type';

/**
 * 退出登录
 */
export const logout = (): YTRequest<{ result: 'true' | 'false' }> => ({
  url: '/userservice/logout.do',
});

/**
 * 用户注销
 */
export const userLogout = (): YTRequest<boolean> => ({
  url: '/userservice/userLogout.do',
});

/**
 * 登录
 */
export const login = (data: UsersApiLoginParams): YTRequest<UsersApiLoginDTO> => ({
  url: '/userservice/login.do',
  data,
});

/**
 * 登录并绑定手机号
 */
export const loginAndBindMobile = (
  data: LoginAndBindMobileParams,
): YTRequest<UsersApiLoginDTO> => ({
  url: '/userservice/loginAndBindMobile.do',
  data: {
    ...data,
    loginType: AppLoginType.WeChat,
  },
});

// 绑定手机号
export const bindPhone = (data: {
  mobile: string;
  validCode: string;
}): YTRequest<{ result: 'true' | 'false' }> => ({
  url: '/userservice/bindmobileonlycode.do',
  data,
});

/**
 * 根据微信登录key查询用户信息
 * @param data
 * @returns
 */
export const queryByWxLoginKey = (data: {
  loginKey: string;
}): YTRequest<QueryWxUserInfoDTO> => ({
  url: '/userservice/queryByWxLoginKey.do',
  data,
});
