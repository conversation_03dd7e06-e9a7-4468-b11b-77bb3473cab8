import { Stack } from 'expo-router';
import { useBindMobile } from '../hook/useBindMobile';
import { PhoneAndSmsCodeView } from './components/PhoneAndSmsCodeView';

/**
 * 绑定手机号内容
 */
export const BindMobileContent = () => {
  const { headerRight, ...bindPhoneProps } = useBindMobile();
  return (
    <>
      <Stack.Screen options={{ headerTitle: '手机号绑定', headerRight }} />
      <PhoneAndSmsCodeView {...bindPhoneProps} />
    </>
  );
};
