import { type AppLoginParams, store, updateUserInfo, type UserInfo } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { setStorage, USERINFO } from '@jgl/utils';
import { login } from '../api/UserServiceApi';

const key = USERINFO;

/**
 * 发送登录请求
 * @param args
 * @returns
 */
export const sendLogInRequest = async (args: AppLoginParams): Promise<UserInfo | undefined> => {
  const request = login(args);
  const response = await container.net().fetch(request);
  const { success, data } = response;
  if (success && data) {
    const user = data;

    await setStorage(key, user);
    store.dispatch(updateUserInfo(user));
    return user;
  }

  return undefined;
};
