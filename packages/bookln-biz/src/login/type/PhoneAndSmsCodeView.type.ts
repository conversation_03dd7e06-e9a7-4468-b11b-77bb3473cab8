import type { RefObject } from 'react';
import type { TextInput } from 'react-native';

export type PhoneAndSmsCodeViewBizProps = {
  showAgreementView: boolean;
  confirmButtonTitle: string;
  isConfirmButtonLoading: boolean;
  onPressConfirm: (args: {
    phoneNumber: string | undefined;
    smsCode: string | undefined;
  }) => void;
  headerRight?: () => React.ReactNode;
  headerLeft?: () => React.ReactNode;
};

export type PhoneAndSmsCodeViewCommonProps = {
  smsCodeInputRef: RefObject<TextInput>;
  phoneNumberInputRef: RefObject<TextInput>;
  phoneNumber: string | undefined;
  onChangePhoneNumber: (text: string) => void;
  smsCode: string | undefined;
  smsCodeButtonTitle: string;
  isSmsCodeButtonLoading: boolean;
  isSmsCodeButtonDisabled: boolean;
  isChecked: boolean;
  onCheckedChange: (checked?: boolean) => void;
  onPressGetSmsCode: () => void;
  onChangeSmsCode: (text: string) => void;
};
