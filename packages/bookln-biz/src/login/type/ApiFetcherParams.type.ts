export type UsersApiLoginParams = {
  /** 数据类型:java.lang.Long */
  id?: number;
  /** 数据类型:java.lang.Integer */
  status?: number;
  /** 数据类型:java.lang.Integer */
  isDelete?: number;
  /** 数据类型:java.lang.Long */
  userId?: number;
  /** 数据类型:java.lang.String */
  name?: string;
  /** 数据类型:java.lang.String */
  realName?: string;
  /** 数据类型:java.lang.String */
  pwd?: string;
  /** 数据类型:java.lang.String */
  nick?: string;
  /** 数据类型:java.lang.Integer */
  sex?: number;
  /** 数据类型:java.lang.String */
  email?: string;
  /** 数据类型:java.lang.String */
  mobile?: string;
  /** 数据类型:java.lang.Integer */
  appType?: number;
  /** 数据类型:java.lang.Integer */
  loginType?: number;
  /** 数据类型:java.lang.String */
  idCard?: string;
  /** 数据类型:java.lang.String */
  sessionId?: string;
  /** 数据类型:java.lang.String */
  smallPhoto?: string;
  /** 数据类型:java.lang.Integer */
  type?: number;
  /** 数据类型:java.lang.Long */
  inviteUid?: number;
  /** 数据类型:java.lang.String */
  inviteCode?: string;
  /** 数据类型:java.lang.Long */
  appId?: number;
  /** 数据类型:java.lang.String */
  openId?: string;
  /** 数据类型:java.lang.String */
  unionId?: string;
  /** 数据类型:java.lang.Integer */
  thirdType?: number;
  /** 数据类型:java.lang.String */
  wxCode?: string;
  /** 数据类型:java.lang.String */
  utm?: string;
  /** 数据类型:java.lang.String */
  validCode?: string;
  /** 数据类型:java.lang.String */
  ttpId?: string;
  /** 数据类型:java.lang.String */
  registerIp?: string;
  /** 数据类型:java.lang.String */
  lastLoginIp?: string;
  /** 数据类型:java.time.LocalDateTime */
  lastLoginTime?: string;
  /** 扩展字段 数据类型:java.lang.String */
  extend?: string;
  /** 出身年 数据类型:java.lang.Integer */
  birthYear?: number;
};

/**
 * 登录并绑定手机号
 */
export type LoginAndBindMobileParams = {
  ttpId: string;
  mobile: string;
  validCode: string;
  smallPhoto?: string;
  sex?: number;
  nick?: string;
};
