import { useLogin } from './hook/useLogin';
import { useLogout } from './hook/useLogout';
import { useSupportSkipBind } from './hook/useSupportSkipBind';
import { useWechat } from './hook/useWechat';
import { BindMobileContent } from './pageContent/BindMobileContent';
import { LoginByPhoneContent } from './pageContent/LoginByPhoneContent';
import { LoginContent } from './pageContent/LoginContent';

/**
 * 书链APP登录相关组件
 */
export const BooklnLoginComponents = {
  LoginContent,
  LoginByPhoneContent,
  BindMobileContent,
};

/**
 * 书链APP登录相关 hooks
 */
export const BooklnLoginHooks = {
  useSupportSkipBind,
  useLogin,
  useLogout,
  useWechat,
};
