import { useChannel } from '@jgl/biz-func';
import { envVars } from '@jgl/utils/src/envVars.native';
import { useMemo } from 'react';
import { useAppConfig } from '../../config/useAppConfig';

type BindMobil = {
  [key: string]: {
    vid: number;
    supportSkip: boolean;
  };
};

const defaultResult = true; // 默认支持跳过绑定

/**
 * 是否支持跳过绑定
 * 默认支持跳过绑定
 */
export const useSupportSkipBind = () => {
  const channel = useChannel();

  const loginConfig = useAppConfig('BooklnLoginConfig');

  // const mockData = {"result": {"bindMobil": { "huawei": { "vid": 307, "supportSkip": false } } } };

  const supportSkipBind = useMemo(() => {
    const bindMobil = JSON.parse(loginConfig) as BindMobil;
    // bindMobile中的key是否存在channel
    if (!bindMobil[channel]) {
      return defaultResult;
    }
    const { vid, supportSkip } = bindMobil[channel];
    const appVersionId = envVars.appVersionId();
    if (vid === Number(appVersionId)) {
      return supportSkip;
    }
    return defaultResult;
  }, [channel, loginConfig]);

  return supportSkipBind;
};
