import { getWeChatAccessInfo, isWeChatInitdAtom } from '@jgl/biz-func';
import { envVars } from '@jgl/utils';
import { useSetAtom } from 'jotai';
import { registerApp } from 'native-wechat';
import { useCallback } from 'react';

export type WeChatInfo = {
  ttpId?: string;
  nick?: string;
  sex?: number;
  smallPhoto?: string;
};

/**
 * 初始化微信
 */
export const useWechat = () => {
  const setIsWeChatInitd = useSetAtom(isWeChatInitdAtom);

  const initWechat = useCallback(() => {
    // https://wxappsdk.bookln.cn/.well-known/apple-app-site-association
    // https://wxappsdk.bookln.cn/appid105
    const universalLink = `https://wxappsdk.bookln.cn/appid${envVars.appId()}`;
    registerApp({
      appid: envVars.weChatAppId(),
      universalLink: universalLink,
      log: __DEV__,
    });
    setIsWeChatInitd(true);
  }, [setIsWeChatInitd]);

  /**
   * 获取微信信息
   */
  const getWeChatInfo = useCallback(async (): Promise<WeChatInfo | undefined> => {
    const weChatAccessInfo = await getWeChatAccessInfo();

    if (weChatAccessInfo) {
      const { ttpId, nickname: nick, sex, headimgurl: smallPhoto } = weChatAccessInfo;
      return { ttpId, nick, sex, smallPhoto };
    }
    return undefined;
  }, []);

  return { initWechat, getWeChatInfo };
};
