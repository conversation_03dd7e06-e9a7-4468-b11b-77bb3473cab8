import { useIMLogout } from '@jgl/ai-qa-v2';
import {
  agreementStateAtom,
  isLoggingOutAtom,
  updateUserInfo,
  useAppDispatch,
} from '@jgl/biz-func';
import { container } from '@jgl/container';
import { storage, USERINFO } from '@jgl/utils';
import { showToast } from '@yunti-private/jgl-ui';
import { useSetAtom } from 'jotai';
import { useCallback } from 'react';
import { logout } from '../api/UserServiceApi';

/**
 * 退出登录
 * @returns
 */
export const useLogout = () => {
  const setIsLoggingOut = useSetAtom(isLoggingOutAtom);

  const setAgreementState = useSetAtom(agreementStateAtom);

  const dispatch = useAppDispatch();

  const { imLogout } = useIMLogout();

  /**
   * 退出登录
   * 先退出登录再使用 uuid 登录
   */
  const handleLogout = useCallback(async () => {
    setIsLoggingOut(true);

    const { success, msg, data } = await container
      .net()
      .fetch(logout())
      .finally(() => {
        setIsLoggingOut(false);
      });
    if (success && data?.result === 'true') {
      // 退出登录 IM
      await imLogout();
      // 清除tid
      dispatch(updateUserInfo({}));
      storage.removeItem(USERINFO);
      setAgreementState('undetermined');
    } else {
      showToast({ title: msg ?? '请求数据失败' });
    }
  }, [dispatch, imLogout, setAgreementState, setIsLoggingOut]);

  return {
    logout: handleLogout,
  };
};
