import { systemApiSmsSend, useCheckUtil } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { showToast } from '@yunti-private/jgl-ui';
import { useCountDown } from 'ahooks';
import { useFocusEffect } from 'expo-router';
import { useCallback, useRef, useState } from 'react';
import type { TextInput } from 'react-native';
import type { PhoneAndSmsCodeViewCommonProps } from '../type/PhoneAndSmsCodeView.type';

export const usePhoneAndSmsCodeView = (): PhoneAndSmsCodeViewCommonProps => {
  const [phoneNumber, setPhoneNumber] = useState<string | undefined>(undefined);
  const [smsCode, setSmsCode] = useState<string | undefined>(undefined);
  const [isRequestingCode, setIsRequestingCode] = useState<boolean>(false);
  const didRequestCode = useRef<boolean>(false);
  const [isChecked, setIsChecked] = useState<boolean>(false);
  const [targetDate, setTargetDate] = useState<number | undefined>(undefined);
  const [requestCodeCountdown] = useCountDown({ targetDate });
  const { checkPhoneNumber } = useCheckUtil();

  // 奇奇怪怪的写法，本来应该是 tamagui 的 Input
  const codeInputRef = useRef<TextInput>(null);
  const phoneNumberInputRef = useRef<TextInput>(null);

  const getCode = useCallback(async () => {
    const checkedPhoneNumber = checkPhoneNumber(phoneNumber);
    if (checkedPhoneNumber) {
      setIsRequestingCode(true);

      const request = systemApiSmsSend({ mobile: checkedPhoneNumber });
      const response = await container.net().fetch(request);
      if (response.success) {
        didRequestCode.current = true;

        showToast({ title: '已发送验证码' });
        codeInputRef.current?.focus();

        setTargetDate(Date.now() + 60 * 1000);
      }

      setIsRequestingCode(false);
    }
  }, [checkPhoneNumber, phoneNumber]);

  useFocusEffect(
    useCallback(() => {
      // 延迟1秒后聚焦
      setTimeout(() => {
        phoneNumberInputRef.current?.focus();
      }, 500);
    }, []),
  );

  const handleChangePhoneNumber = useCallback((text: string) => {
    setPhoneNumber(text);
  }, []);

  const handleChangeCode = useCallback((text: string) => {
    setSmsCode(text);
  }, []);

  const isRequestCodeButtonDisabled = requestCodeCountdown > 0;

  let requestCodeButtonTitle = '获取验证码';
  if (isRequestCodeButtonDisabled) {
    requestCodeButtonTitle = `${Math.floor(requestCodeCountdown / 1000)}s`;
  } else if (didRequestCode.current) {
    requestCodeButtonTitle = '重新获取';
  }

  const handleCheckedChange = useCallback((checked?: boolean) => {
    if (checked === undefined) {
      setIsChecked((preChecked) => !preChecked);
    } else {
      setIsChecked(checked);
    }
  }, []);

  return {
    isSmsCodeButtonDisabled: isRequestCodeButtonDisabled,
    isSmsCodeButtonLoading: isRequestingCode,
    onChangePhoneNumber: handleChangePhoneNumber,
    onChangeSmsCode: handleChangeCode,
    onPressGetSmsCode: getCode,
    phoneNumber,
    smsCode,
    smsCodeButtonTitle: requestCodeButtonTitle,
    smsCodeInputRef: codeInputRef,
    phoneNumberInputRef: phoneNumberInputRef,
    onCheckedChange: handleCheckedChange,
    isChecked,
  };
};
