import { useCallback } from 'react';
import type { PhoneAndSmsCodeViewBizProps } from '../type/PhoneAndSmsCodeView.type';
import { useLogin } from './useLogin';

/**
 * 手机号登录
 * @returns 手机号登录的props
 */
export const useLogInByPhone = (): PhoneAndSmsCodeViewBizProps => {
  const { isLoggingIn: isLoggingInBySmsCode, handleLoginByPhone } = useLogin();
  const handlePressLogIn = useCallback(
    async (args: {
      phoneNumber: string | undefined;
      smsCode: string | undefined;
    }) => {
      handleLoginByPhone(args);
    },
    [handleLoginByPhone],
  );

  return {
    confirmButtonTitle: '登录',
    isConfirmButtonLoading: isLoggingInBySmsCode,
    onPressConfirm: handlePressLogIn,
    showAgreementView: true,
  };
};
