import { AppLoginType } from '@jgl/biz-func';
import { JglText, JglTouchable } from '@jgl/ui-v4';
import { useRouterParams } from '@jgl/utils';
import { useCallback } from 'react';
import { ActivityIndicator } from 'react-native';
import type { PhoneAndSmsCodeViewBizProps } from '../type/PhoneAndSmsCodeView.type';
import { useLogin } from './useLogin';
import { useSupportSkipBind } from './useSupportSkipBind';
import type { WeChatInfo } from './useWechat';

export const useBindMobile = (): PhoneAndSmsCodeViewBizProps => {
  const {
    handleBindMobile,
    isBinding,
    isSkippingBind,
    handleSkipBindAndLoginByWeChat,
  } = useLogin();
  const { weChatInfoStr } = useRouterParams();
  const weChatInfo = weChatInfoStr
    ? (JSON.parse(weChatInfoStr) as WeChatInfo)
    : null;
  const supportSkipBind = useSupportSkipBind();

  const handlePressBind = useCallback(
    async (args: {
      phoneNumber: string | undefined;
      smsCode: string | undefined;
    }) => {
      handleBindMobile({
        mobile: args.phoneNumber ?? '',
        validCode: args.smsCode ?? '',
        ttpId: weChatInfo?.ttpId ?? '',
        nick: weChatInfo?.nick,
        sex: weChatInfo?.sex,
      });
    },
    [handleBindMobile, weChatInfo?.nick, weChatInfo?.sex, weChatInfo?.ttpId],
  );

  /**
   * 跳过绑定手机号，直接微信登录
   */
  const handleSkipBind = useCallback(() => {
    handleSkipBindAndLoginByWeChat({
      loginType: AppLoginType.WeChat,
      ttpId: weChatInfo?.ttpId ?? '',
      nick: weChatInfo?.nick,
      sex: weChatInfo?.sex,
      smallPhoto: weChatInfo?.smallPhoto,
    });
  }, [
    handleSkipBindAndLoginByWeChat,
    weChatInfo?.nick,
    weChatInfo?.sex,
    weChatInfo?.smallPhoto,
    weChatInfo?.ttpId,
  ]);

  const headerRight = useCallback(() => {
    if (supportSkipBind) {
      return (
        <JglTouchable
          jglClassName='flex-center'
          onPress={handleSkipBind}
          disabled={isSkippingBind}
        >
          {isSkippingBind ? (
            <ActivityIndicator size='small' color={'#A0A0A0'} />
          ) : (
            <JglText fontSize={14} color={'#A0A0A0'}>
              跳过
            </JglText>
          )}
        </JglTouchable>
      );
    }
    return null;
  }, [handleSkipBind, isSkippingBind, supportSkipBind]);

  return {
    confirmButtonTitle: '绑定手机号',
    isConfirmButtonLoading: isBinding,
    onPressConfirm: handlePressBind,
    showAgreementView: false,
    headerRight,
  };
};
