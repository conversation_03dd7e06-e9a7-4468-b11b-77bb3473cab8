import { store } from '@jgl/biz-func';
import { envVars } from '@jgl/utils';
import type { YTRequest } from '@yunti-private/net';
import type { IMAgentConfDTO } from '../dto/IMAgentConfDTO';
import { getHeaderAppToken } from '../utils/getHeaderAppToken';

export const getByName = (data: {
  name: string;
}): YTRequest<IMAgentConfDTO> => {
  const sessionId = store.getState().userInfo.sessionId;
  const appId = envVars.appId().toString();
  return {
    url: '/aiProduct/getByName.do',
    data,
    project: 'booklnboot',
    headers: {
      'app-id': appId,
      'app-p-token': getHeaderAppToken(sessionId) || '',
    },
  };
};
