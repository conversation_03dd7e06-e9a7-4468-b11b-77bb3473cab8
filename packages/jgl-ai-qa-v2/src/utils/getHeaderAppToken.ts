import { envVars } from '@jgl/utils';
import CryptoJS, { MD5 } from 'crypto-js';

/**
 * 获取 App Token
 */
export const getHeaderAppToken = (
  sessionId: string | undefined | null,
): string | undefined | null => {
  if (sessionId) {
    const appId = envVars.appId();
    const keyPart1 = MD5(`e0cdc${appId}`).toString().substring(0, 4);
    const keyPart2 = MD5(`0cdc2${appId}`).toString().substring(0, 4);
    const key = `${keyPart1}${envVars.appToken()}${keyPart2}`;

    const encryptedSessionId = encrypt(key, sessionId);
    return encryptedSessionId;
  }
  return null;
};

/**
 * 加密
 */
const encrypt = (key: string, plainText: string): string => {
  const ciphertext = CryptoJS.AES.encrypt(
    CryptoJS.enc.Utf8.parse(plainText),
    CryptoJS.enc.Utf8.parse(key),
    { mode: CryptoJS.mode.ECB },
  ).toString();
  return ciphertext;
};
