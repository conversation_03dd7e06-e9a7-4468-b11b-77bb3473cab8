import { container } from '@jgl/container';
import { sessionUtil } from '@yunti-private/basic-im';

export const sendGuideFollowUpMsg = async (param: {
  sessionId: string;
  robotUserId: string;
  lastMsgId: number;
}) => {
  const { sessionId, robotUserId, lastMsgId } = param;
  const bizId = sessionUtil.bizIdFromSessionId(sessionId);
  const response = await container.im().sendRequest<{ followAskMessages: string }>({
    path: '/aiProductMessage/sendGuideFollowAskMsg.do',
    bizData: {
      appName: 'app',
      bizId,
      robotUserId,
      lastMsgId,
    },
  });
  return response.data;
};
