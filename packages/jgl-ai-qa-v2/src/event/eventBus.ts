import mitt from 'mitt';
import type { IMMessage } from '../store/IMMessage';

export type IMInternalEvents = {
  /**
   * 当前会话获取最新消息成功
   */
  currentSessionFetchLatestMessagesSuccess: {
    sessionId: string;
    messages: IMMessage[];
  };
  /**
   * 消息需要追问
   */
  messageNeedFollowAsk: {
    sessionId: string;
    message: IMMessage;
  };

  /**
   * 开场白消息需要追问
   */
  guideMessageNeedFollowAsk: {
    sessionId: string;
    message: IMMessage;
  };

  /**
   * 追问消息点击事件
   */
  onFollowAskMessagePress: {
    sessionId: string;
    message: IMMessage;
  };

  /**
   * 消息列表项点击编辑按钮
   */
  onOutMessageContextMenuEditButtonPress: {
    messageTextContent: string;
  };

  /**
   * 删除追问消息
   */
  onDeleteFollowAskMessage: {
    sessionId: string;
  };
};

export const eventBus = mitt<IMInternalEvents>();
