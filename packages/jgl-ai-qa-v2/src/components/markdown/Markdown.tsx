import React, {
  memo,
  useCallback,
  type ReactElement,
  type ReactNode,
} from 'react';
import { FlatList, useColorScheme } from 'react-native';
import type { MarkdownProps } from 'react-native-marked';
import { useMarkdown } from './useMarkdown';
// import { ChatStatus } from '../../../types/Chat.ts';

type ChatMarkdownProps = MarkdownProps;

export const Markdown = memo(
  ({
    value,
    flatListProps,
    theme,
    baseUrl,
    renderer,
    styles,
    tokenizer,
  }: ChatMarkdownProps) => {
    const colorScheme = useColorScheme();

    const rnElements = useMarkdown(value, {
      theme,
      baseUrl,
      renderer,
      colorScheme,
      styles,
      tokenizer,
    });

    const renderItem = useCallback(({ item }: { item: ReactNode }) => {
      return item as ReactElement;
    }, []);

    const keyExtractor = useCallback(
      (_: ReactNode, index: number) => index.toString(),
      [],
    );

    return (
      <FlatList
        removeClippedSubviews={false}
        keyExtractor={keyExtractor}
        initialNumToRender={rnElements.length}
        style={{
          backgroundColor: colorScheme === 'light' ? '#ffffff' : '#000000',
        }}
        {...flatListProps}
        data={rnElements}
        renderItem={renderItem}
      />
    );
  },
);
