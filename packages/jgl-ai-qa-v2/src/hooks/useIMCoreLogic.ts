/* eslint-disable @typescript-eslint/no-shadow */
import { useAppSelector, YuntimCommonApi } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { envVars, featureToggles } from '@jgl/utils';
import { appConfigApiGetAppConf } from '@yunti-private/api-xingdeng-boot';
import {
  type BizResponse,
  type CustomMessageBody,
  EndType,
  type ICustomPayload,
  type IImagePayload,
  type ImageMessageBody,
  type ImageTextMessageBody,
  type IMessage,
  type IMEventValue,
  type IMMessageDTO,
  type IStreamAudioPayload,
  type ITextPayload,
  MessageContentType,
  MessageFlow,
  SessionType,
  sessionUtil,
  type TextMessageBody,
} from '@yunti-private/basic-im';
import { ConnectionEvent } from '@yunti-private/connection';
import { Env } from '@yunti-private/env';
import { useAtomValue, useSetAtom } from 'jotai';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { getUniqueId } from 'react-native-device-info';
import { getByName } from '../api/AiProductApi';
import { genTmpAudioClips as genTmpAudioClipsRequest } from '../api/AiServiceApi';
import {
  imAgentConfAtom,
  imAudioConfAtom,
  imIntentRecognizeRobotConfAtom,
  imLoginStatusAtom,
  imRobotConfAtom,
} from '../atoms/IIMAtoms';
import type { IMAudioConfDTO } from '../dto/IMAudioConfDTO';
import { eventBus } from '../event/eventBus';
import type { IMMessage } from '../store/IMMessage';
import { useIMMessageStore } from '../store/IMMessageStore';
import { useIMSessionStore } from '../store/IMSessionStore';
import {
  booklnAIRobotBizId,
  type BooklnAIRobotBizIdValueType,
  JglAiQAChatMessagePageSize,
} from '../utils/constants';
import { sendGuideFollowUpMsg } from '../utils/sendGuideFollowUpMsg';
import { sendFollowAskMsg } from '../utils/sendFollowAskMsg';
import { MemoryLogger } from '@yunti-private/rn-memory-logger';

/**
 * IM 事件监听 Hook
 */
export const useIMEvents = () => {
  const imLoginStatus = useAtomValue(imLoginStatusAtom);

  // 获取 store 中的方法
  const { updateSessions } = useIMSessionStore();
  const { appendMessage, markAllNotFinishedInMessageFailed } = useIMMessageStore();
  const { robotConf } = useIMRobotConf();
  useInitIMSessions({ robotBizId: booklnAIRobotBizId.AI_QA, imLoginStatus });
  const userInfo = useAppSelector((state) => state.userInfo);
  const { genTmpAudioClips } = useIMGenTmpAudioClips();

  const fullMessageTextMapRef = useRef<Map<string, string>>(new Map());

  /**
   * 会话事件处理
   * 处理会话的添加、删除、最后消息变更等事件
   * @param resp - 会话事件数据
   */
  const onSessionEvents = useCallback(
    (resp: IMEventValue<'SessionEvents'>) => {
      // 只处理 SessionAdd, SessionRemove, SessionLastMsgChanged
      const { action } = resp;
      console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - onSessionEvents ~ action:', action);
      if (action !== 'SessionRank') {
        const { data } = resp;
        console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - onSessionEvents ~ data:', data);
        updateSessions([...data]);
      }
    },
    [updateSessions],
  );

  /**
   * 连接事件处理
   * 处理 IM 连接成功事件，更新会话列表
   * @param resp - 连接事件数据
   */
  const onConnectEvents = useCallback(
    (resp: IMEventValue<'ConnectEvents'>) => {
      const { action } = resp;
      console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - onConnectEvents ~ action:', action);
      if (action === 'Connect') {
        console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - onConnectEvents ~ Connect ~ resp.data:', resp.data);
        updateSessions([...resp.data]);
      } else if (action === 'Disconnect') {
        console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - onConnectEvents ~ Disconnect');
        // 更新 in 并且 end 为 0 的消息状态为失败
        markAllNotFinishedInMessageFailed();
      }
    },
    [updateSessions, markAllNotFinishedInMessageFailed],
  );

  const handleSyntheticAudio = useCallback(
    async (param: { order: number; txt: string; bizId: string }) => {
      const { order, txt, bizId } = param;
      // 检查文本是否包含数字、大小写英文字母或汉字
      const hasValidChars = /[0-9a-zA-Z\u4e00-\u9fa5]/.test(txt);
      if (!hasValidChars || txt.length === 0) {
        console.log('文本不包含有效字符，跳过语音合成', txt);
        return;
      }
      const response = await genTmpAudioClips({
        txt,
        order,
        userId: userInfo?.userId ?? 0,
        bizId,
      });
      console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ AIChatFloatBall ~ response:', response);
    },
    [genTmpAudioClips, userInfo?.userId],
  );

  const handleInMessage = useCallback(
    async (data: IMMessage) => {
      console.log('handleInMessage - 🎯 收到新消息:', {
        id: data.id,
        contentType: data.contentType,
        sessionId: data.sessionId,
        msgId: data.msgId,
        end: (data.payload as ITextPayload).end,
        streamType: (data.payload as ITextPayload).streamType,
      });

      if (data.contentType === MessageContentType.Text) {
        const { text: currentTotalText, end, streamType } = data.payload as ITextPayload;
        console.log('handleInMessage - 📝 当前文本内容:', {
          currentTotalText,
          end,
          streamType,
          textLength: currentTotalText.length,
        });

        if (streamType && currentTotalText.length > 0) {
          const sessionIdStr = String(data.sessionId ?? '').trim();
          const msgIdStr = String(data.msgId ?? '').trim();
          const bizId = `${sessionIdStr}-${msgIdStr}`;

          // 获取已合成的文本内容
          const oldMessageContent = fullMessageTextMapRef.current.get(data.id) || '';
          console.log('handleInMessage - 📚 已合成文本:', {
            oldMessageContent,
            oldLength: oldMessageContent.length,
          });

          // 获取新到达的文本
          const newlyArrivedText = currentTotalText.substring(oldMessageContent.length);
          console.log('handleInMessage - 🆕 新到达文本:', {
            newlyArrivedText,
            newLength: newlyArrivedText.length,
          });

          // 更新当前文本内容
          // fullMessageTextMapRef.current.set(data.id, currentTotalText);

          // 如果新文本为空，直接返回
          if (!newlyArrivedText) {
            console.log('handleInMessage - ⚠️ 新文本为空，跳过处理');
            return;
          }

          // 判断是否需要合成音频
          const shouldSynthesize = (() => {
            // 如果是最后一条消息，需要合成
            if (end) {
              console.log('handleInMessage - 🎬 最后一条消息，需要合成');
              return true;
            }

            // 检查新文本中是否包含停顿点（包括中文逗号）
            const hasPausePoint = /[。！？.!?，]/.test(newlyArrivedText);
            console.log('handleInMessage - 🔍 停顿点检查:', {
              hasPausePoint,
              textLength: newlyArrivedText.length,
            });

            // 如果新文本长度超过阈值且包含停顿点，需要合成
            if (newlyArrivedText.length >= 10 && hasPausePoint) {
              console.log('handleInMessage - ✅ 满足合成条件: 长度>=50且包含停顿点');
              return true;
            }

            console.log('handleInMessage - ❌ 不满足合成条件');
            return false;
          })();

          if (shouldSynthesize) {
            // 找到所有停顿点（包括中文逗号）
            const pausePoints = newlyArrivedText.match(/[。！？.!?，]/g);
            console.log('handleInMessage - 📍 停顿点:', {
              pausePoints,
              count: pausePoints?.length,
            });

            let textToSynthesize = newlyArrivedText;
            let synthesizedLength = 0;

            if (pausePoints && pausePoints.length > 0) {
              // 找到最后一个标点符号的位置
              const lastPausePoint = pausePoints[pausePoints.length - 1] || '';
              const lastIndex = newlyArrivedText.lastIndexOf(lastPausePoint);

              console.log('handleInMessage - 🎯 最后一个停顿点:', {
                lastPausePoint,
                lastIndex,
                remainingText: newlyArrivedText.substring(lastIndex + 1).trim(),
              });

              // 确保标点符号后没有其他内容（除了空白字符）
              const remainingText = newlyArrivedText.substring(lastIndex + 1).trim();

              if (remainingText.length === 0) {
                // 如果标点符号后没有其他内容，合成到标点符号
                textToSynthesize = newlyArrivedText.substring(0, lastIndex + 1);
                synthesizedLength = textToSynthesize.length;
                console.log('handleInMessage - 📌 情况1: 标点后无内容，合成到标点');
              } else if (remainingText.length >= 10) {
                // 如果剩余文本较长，也合成到标点符号
                textToSynthesize = newlyArrivedText.substring(0, lastIndex + 1);
                synthesizedLength = textToSynthesize.length;
                console.log('handleInMessage - 📌 情况2: 剩余文本较长，合成到标点');
              } else if (lastPausePoint === '，') {
                // 如果是逗号，且剩余文本较短，也合成到逗号
                textToSynthesize = newlyArrivedText.substring(0, lastIndex + 1);
                synthesizedLength = textToSynthesize.length;
                console.log('handleInMessage - 📌 情况3: 是逗号且剩余文本较短，合成到逗号');
              } else {
                // 如果是其他标点，且剩余文本较短，合成整个新文本
                textToSynthesize = newlyArrivedText;
                synthesizedLength = textToSynthesize.length;
                console.log('handleInMessage - 📌 情况4: 其他标点且剩余文本较短，合成整个新文本');
              }
            }

            console.log('handleInMessage - 🎵 准备合成音频:', {
              textToSynthesize,
              length: textToSynthesize.length,
            });

            // 更新已合成的文本内容
            const newSynthesizedContent = oldMessageContent + textToSynthesize;

            handleSyntheticAudio({
              order: newSynthesizedContent.length - 1,
              txt: textToSynthesize,
              bizId,
            });

            fullMessageTextMapRef.current.set(data.id, newSynthesizedContent);
          }
        }
      }
    },
    [handleSyntheticAudio],
  );

  const onConnectionMessage = useCallback(
    (response: {
      code: number;
      data: { data: IMMessageDTO };
    }) => {
      const { code, data } = response;
      console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - onConnectionMessage ~ data:', data.data);
      if (data.data.contentType === MessageContentType.StreamAudio) {
        try {
          console.log(
            'leejunhui - 🔥🔥🔥🔥🔥🔥 - onConnectionMessage ~ StreamAudio data:',
            data.data.payload,
          );
          const payload = JSON.parse(data.data.payload) as IStreamAudioPayload;
          const { uid, order, txt, mp3, bizId } = payload;
          // console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - onConnectionMessage ~ StreamAudio data:', payload);
        } catch (error) {
          console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ onConnectionMessage ~ error:', error);
        }
      }
    },
    [],
  );

  /**
   * 消息事件处理
   * 处理消息发送事件，将新消息添加到对应会话
   * @param resp - 消息事件数据
   */
  const onMessageEvents = useCallback(
    async (resp: IMEventValue<'MessageEvents'>) => {
      console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - onMessageEvents ~ resp:', resp);
      if (resp.data.contentType === MessageContentType.Text) {
        appendMessage({
          sessionId: resp.data.sessionId,
          message: { ...resp.data },
          robotConf,
        });
        console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - onMessageEvents ~ message appended:', {
          sessionId: resp.data.sessionId,
          message: resp.data,
        });
      }

      // if (resp.data.flow === MessageFlow.In) {
      //   handleInMessage(resp.data);
      // }
    },
    [appendMessage, robotConf],
  );

  const addIMEventListeners = useCallback(() => {
    // IM 事件监听
    container.im().on<'ConnectEvents'>('ConnectEvents', onConnectEvents);
    container.im().on<'SessionEvents'>('SessionEvents', onSessionEvents);
    container.im().on<'MessageEvents'>('MessageEvents', onMessageEvents);

    container.im().connection.on(ConnectionEvent.Message, onConnectionMessage);
  }, [onConnectEvents, onConnectionMessage, onMessageEvents, onSessionEvents]);

  const removeIMEventListeners = useCallback(() => {
    container.im().off<'ConnectEvents'>('ConnectEvents', onConnectEvents);
    container.im().off<'SessionEvents'>('SessionEvents', onSessionEvents);
    container.im().off<'MessageEvents'>('MessageEvents', onMessageEvents);

    container.im().connection.off(ConnectionEvent.Message, onConnectionMessage);
  }, [onConnectEvents, onConnectionMessage, onMessageEvents, onSessionEvents]);

  /**
   * 设置 IM 事件监听
   * 监听连接事件、会话事件和消息事件
   * 在组件卸载时自动清理事件监听
   */
  useEffect(() => {
    // 如果已经登录，则添加事件监听
    if (imLoginStatus === 'loggedIn') {
      removeIMEventListeners();
      addIMEventListeners();
    } else if (imLoginStatus === 'logout') {
      removeIMEventListeners();
    }

    // 清理函数：移除所有事件监听
    return () => {
      removeIMEventListeners();
    };
  }, [imLoginStatus, addIMEventListeners, removeIMEventListeners]);
};

export const useIMFetchAgentConf = () => {
  // 获取 IM 登录状态
  const imHasLoggedIn = useAtomValue(imLoginStatusAtom);
  // 设置智能体配置的方法
  const setImAgentConf = useSetAtom(imAgentConfAtom);

  const fetchAgentConf = useCallback(async () => {
    const request = getByName({ name: 'app' });
    // 发送请求获取机器人配置
    const response = await container.net().fetch(request);
    if (response.success && response.data) {
      setImAgentConf(response.data);
    }
  }, [setImAgentConf]);

  useEffect(() => {
    if (imHasLoggedIn === 'loggedIn') {
      fetchAgentConf();
    }
  }, [fetchAgentConf, imHasLoggedIn]);
};

/**
 * 获取机器人配置的 Hook
 * 用于获取和设置 IM 机器人的配置信息
 */
export const useIMFetchRobotConf = () => {
  // 获取 IM 登录状态
  const imHasLoggedIn = useAtomValue(imLoginStatusAtom);
  // 设置机器人配置的方法
  const setImRobotConf = useSetAtom(imRobotConfAtom);

  /**
   * 获取 LLM 模型机器人 ID
   * 根据不同环境调用不同的 API 地址获取机器人配置
   */
  const fetchLLMModelRobotId = useCallback(async () => {
    // 创建获取机器人配置的请求
    const request = YuntimCommonApi.getRobotConf({
      bizCode: envVars.imRobotBizCode(),
    });

    // 根据当前环境设置不同的 API 基础地址
    const currentEnv = container.env().env();
    if (currentEnv === Env.Daily || currentEnv === Env.Local || currentEnv === Env.Dev) {
      request.baseHost = 'https://www-daily.yuntim.com';
    } else if (currentEnv === Env.Prepub) {
      request.baseHost = 'https://www-prepub.yuntim.com';
    } else if (currentEnv === Env.Production) {
      request.baseHost = 'https://www.yuntim.com';
    }
    request.project = 'www';

    // 发送请求获取机器人配置
    const response = await container.net().fetch(request);
    if (response.success && response.data) {
      console.log('response.data', response.data);
      setImRobotConf(response.data);
    }
  }, [setImRobotConf]);

  // 当 IM 登录成功后，自动获取机器人配置
  useEffect(() => {
    if (imHasLoggedIn === 'loggedIn') {
      fetchLLMModelRobotId();
    }
  }, [fetchLLMModelRobotId, imHasLoggedIn]);
};

/**
 * 获取意图识别机器人配置的 Hook
 * 用于获取和设置 IM 意图识别机器人的配置信息
 */
export const useIMFetchIntentRecognizeRobotConf = () => {
  // 获取 IM 登录状态
  const imHasLoggedIn = useAtomValue(imLoginStatusAtom);
  // 设置机器人配置的方法
  const setImIntentRecognizeRobotConf = useSetAtom(imIntentRecognizeRobotConfAtom);

  /**
   * 获取 LLM 模型机器人 ID
   * 根据不同环境调用不同的 API 地址获取机器人配置
   */
  const fetchLLMModelRobotId = useCallback(async () => {
    // 创建获取机器人配置的请求
    const request = YuntimCommonApi.getRobotConf({
      bizCode: envVars.imIntentRecognizeRobotBizCode(),
    });

    // 根据当前环境设置不同的 API 基础地址
    const currentEnv = container.env().env();
    if (currentEnv === Env.Daily || currentEnv === Env.Local || currentEnv === Env.Dev) {
      request.baseHost = 'https://www-daily.yuntim.com';
    } else if (currentEnv === Env.Prepub) {
      request.baseHost = 'https://www-prepub.yuntim.com';
    } else if (currentEnv === Env.Production) {
      request.baseHost = 'https://www.yuntim.com';
    }
    request.project = 'www';

    // 发送请求获取机器人配置
    const response = await container.net().fetch(request);
    if (response.success && response.data) {
      console.log('response.data', response.data);
      setImIntentRecognizeRobotConf(response.data);
    }
  }, [setImIntentRecognizeRobotConf]);

  // 当 IM 登录成功后，自动获取机器人配置
  useEffect(() => {
    if (imHasLoggedIn === 'loggedIn') {
      fetchLLMModelRobotId();
    }
  }, [fetchLLMModelRobotId, imHasLoggedIn]);
};

export const useIMFetchAudioConf = () => {
  // 获取 IM 登录状态
  const imHasLoggedIn = useAtomValue(imLoginStatusAtom);

  // 设置机器人配置的方法
  const setImAudioConf = useSetAtom(imAudioConfAtom);

  const fetchAudioConf = useCallback(async () => {
    const request = appConfigApiGetAppConf({
      confCode: envVars.imAudioConfCode(),
    });
    request.project = 'app';
    const response = await container.net().fetch(request);
    if (response.success && response.data) {
      if (typeof response.data === 'string') {
        const audioConf = JSON.parse(response.data) as IMAudioConfDTO;
        console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ fetchAudioConf ~ audioConf:', audioConf);
        setImAudioConf(audioConf);
      }
    }
  }, [setImAudioConf]);

  // 当 IM 登录成功后，自动获取音频配置
  useEffect(() => {
    if (imHasLoggedIn === 'loggedIn') {
      fetchAudioConf();
    }
  }, [fetchAudioConf, imHasLoggedIn]);
};

/**
 * IM 登录的 Hook
 * 提供 IM 登录功能
 */
export const useIMLogin = () => {
  // 设置 IM 登录状态的方法
  const setImHasLoggedIn = useSetAtom(imLoginStatusAtom);

  /**
   * IM 登录方法
   * @param userId - 用户 ID
   * @returns Promise<void>
   */
  const imLogin = useCallback(
    async (userId: string) => {
      MemoryLogger.log(`🚀🚀🚀🚀🚀🚀 - leejunhui ~ imLogin ~ userId: ${userId}`);
      const response = await container.im().login(userId);
      MemoryLogger.log(
        `🚀🚀🚀🚀🚀🚀 - leejunhui ~ imLogin ~ response: ${JSON.stringify(response)}`,
      );
      if (response.success && response.data) {
        console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ imLogin ~ 登录成功');
        setImHasLoggedIn('loggedIn');
      }
    },
    [setImHasLoggedIn],
  );

  return { imLogin };
};

/**
 * IM 登出的 Hook
 * 提供 IM 登出功能
 */
export const useIMLogout = () => {
  // 设置 IM 登录状态的方法
  const setImHasLoggedIn = useSetAtom(imLoginStatusAtom);

  /**
   * IM 登出方法
   * @returns Promise<void>
   */
  const imLogout = useCallback(async () => {
    const response = await container.im().logout();
    if (response.success) {
      setImHasLoggedIn('logout');
    }
  }, [setImHasLoggedIn]);

  return { imLogout };
};

/**
 * 获取智能体配置的 Hook
 * 用于获取和设置 IM 智能体配置信息
 */
export const useIMAgentConf = () => {
  const agentConf = useAtomValue(imAgentConfAtom);

  return { agentConf };
};

/**
 * 获取机器人配置的 Hook
 * 用于获取当前机器人配置信息
 * @returns {Object} 包含机器人配置的对象
 */
export const useIMRobotConf = () => {
  // 获取机器人配置
  const robotConf = useAtomValue(imRobotConfAtom);

  return { robotConf };
};

/**
 * 获取意图识别机器人配置的 Hook
 * 用于获取和设置 IM 意图识别机器人的配置信息
 */
export const useIMIntentRecognizeRobotConf = () => {
  const robotConf = useAtomValue(imIntentRecognizeRobotConfAtom);

  return { robotConf };
};

/**
 * 获取 IM 音频配置的 Hook
 * 用于获取和设置 IM 音频配置信息
 */
export const useIMAudioConf = () => {
  const audioConf = useAtomValue(imAudioConfAtom);

  return { audioConf };
};

/**
 * 获取 IM 会话列表的 Hook
 * @returns {Object} 包含获取会话列表方法的对象
 * @property {Function} fetchIMSessions - 获取会话列表的方法
 */
export const useFetchIMSessions = (param: {
  robotBizId?: BooklnAIRobotBizIdValueType;
  onlyOneSessionScene?: boolean;
}) => {
  const { robotBizId = booklnAIRobotBizId.AI_QA, onlyOneSessionScene = false } = param;

  const { fetchSessionListApiState } = useIMSessionStore();
  const { robotConf } = useIMRobotConf();

  /**
   * 获取 IM 会话列表
   * 该方法会:
   * 1. 更新获取会话列表的 API 状态
   * 2. 调用 IM SDK 获取会话列表
   * 3. 对获取的会话列表按最后消息时间排序
   * 4. 更新会话列表到 store 中
   */
  const fetchIMSessions = useCallback(() => {
    // 从 store 中获取更新状态和会话的方法
    const { setFetchSessionListApiState, updateSessions } = useIMSessionStore.getState();

    /**
     * 实际获取会话列表的异步函数
     * 处理获取会话列表的具体逻辑和错误处理
     */
    const fetchSessions = async () => {
      try {
        // 设置加载状态
        setFetchSessionListApiState('loading');

        console.log(
          'leejunhui - 🔥🔥🔥🔥🔥🔥 - fetchSessions ~ robotConf?.robotId:',
          robotConf?.robotId,
          robotBizId,
        );
        // 调用 IM SDK 获取会话列表
        const response = await container.im().getRobotBizList({
          robotUserId: robotConf?.robotId ?? +featureToggles.imRobotId(),
          bizId: robotBizId,
          updateTime: onlyOneSessionScene ? 0 : undefined,
        });

        if (response.success && response.data) {
          console.log(
            'leejunhui - 🔥🔥🔥🔥🔥🔥 - fetchSessions ~ response:',
            response.data.sessions,
          );

          const filteredData = response.data.sessions.filter((session) => {
            const bizId = session.bizId;
            if (bizId) {
              const bizIdSplitResult = bizId.split('_');
              if (bizIdSplitResult.length === 5) {
                const sessionRobotBizId = Number(
                  bizIdSplitResult[bizIdSplitResult.length - 2] || '',
                );
                return sessionRobotBizId === robotBizId;
              } else {
                return false;
              }
            } else {
              return false;
            }
          });
          console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - fetchSessions ~ filteredData:', filteredData);

          // 按最后消息时间降序排序会话列表
          const sortedSessions = filteredData.sort((a, b) => {
            return b.lastMessage.time - a.lastMessage.time;
          });

          // 更新会话列表到 store
          updateSessions(sortedSessions);
          // 设置成功状态
          setFetchSessionListApiState('success');

          return sortedSessions;
        } else {
          // 如果响应不成功,设置错误状态
          setFetchSessionListApiState('error');
          return [];
        }
      } catch (error) {
        // 捕获并记录错误
        console.error('Failed to fetch session list:', error);
        // 设置错误状态
        setFetchSessionListApiState('error');
        return [];
      }
    };

    // 执行获取会话列表
    return fetchSessions();
  }, [onlyOneSessionScene, robotBizId, robotConf?.robotId]);

  return {
    fetchSessionListApiState,
    fetchIMSessions,
  };
};

export const useIMSetCurrentSessionId = () => {
  const { setCurrentSessionId } = useIMSessionStore();

  return { setCurrentSessionId };
};

/**
 * 初始化 IM 会话列表的 Hook
 *
 * 该 Hook 主要用于监听 IM 登录状态的变化，在登录成功后自动获取会话列表
 *
 * 工作流程:
 * 1. 监听 IM 登录状态
 * 2. 当状态变为 'loggedIn' 时，触发获取会话列表
 * 3. 通过 fetchIMSessions 获取并更新会话列表到 store 中
 */
export const useInitIMSessions = (param: {
  robotBizId?: BooklnAIRobotBizIdValueType;
  imLoginStatus: 'idle' | 'loggedIn' | 'logout';
}) => {
  const { robotBizId = booklnAIRobotBizId.AI_QA, imLoginStatus } = param;
  // 获取会话列表的方法
  const { fetchIMSessions } = useFetchIMSessions({ robotBizId });
  const { createIMSession } = useIMCreateSession({ robotBizId });
  const { setCurrentSessionId } = useIMSetCurrentSessionId();
  const isFetchingSessionsRef = useRef(false);
  const { robotConf } = useIMRobotConf();
  // 获取 IM 登录状态
  // const imLoginStatus = useAtomValue(imLoginStatusAtom);

  // 监听登录状态变化，在登录成功后获取会话列表
  useEffect(() => {
    const fetchSessionsIfNeeded = imLoginStatus === 'loggedIn' && robotConf?.robotId;
    if (fetchSessionsIfNeeded) {
      if (isFetchingSessionsRef.current) {
        console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - useInitIMSessions - 正在获取会话呢');
        return;
      }
      isFetchingSessionsRef.current = true;
      console.log(
        'leejunhui - 🔥🔥🔥🔥🔥🔥 - useInitIMSessions ~ imLoginStatus === loggedIn, robotConf?.robotId:',
        robotConf?.robotId,
      );
      fetchIMSessions().then((sessions) => {
        console.log(
          'leejunhui - 🔥🔥🔥🔥🔥🔥 - useInitIMSessions ~ fetchIMSessions ~ sessions:',
          sessions,
          robotBizId,
        );
        if (sessions.length === 0) {
          // 如果会话列表为空，则创建一个新会话
          console.log(
            'leejunhui - 🔥🔥🔥🔥🔥🔥 - useInitIMSessions ~ sessions.length === 0, creating new session',
            robotBizId,
          );
          createIMSession().then((session) => {
            console.log(
              'leejunhui - 🔥🔥🔥🔥🔥🔥 - useInitIMSessions ~ createIMSession ~ session:',
              session,
              robotBizId,
            );
            setCurrentSessionId(session.sessionId);
          });
        } else {
          // 取第一条，也就是最新的一条会话
          const lastSession = sessions[0];
          console.log(
            'leejunhui - 🔥🔥🔥🔥🔥🔥 - useInitIMSessions ~ sessions.length > 0, lastSession:',
            lastSession,
            robotBizId,
          );
          if (robotBizId === booklnAIRobotBizId.AI_QA) {
            if (lastSession?.sessionId) {
              // 获取唯一会话上的消息记录，如果消息为空，则设置为当前会话，如果不为空，则新建会话
              container
                .im()
                .getLatestMessageList({
                  sessionId: lastSession?.sessionId,
                  pageSize: JglAiQAChatMessagePageSize,
                })
                .then((resp) => {
                  console.log(
                    'leejunhui - 🔥🔥🔥🔥🔥🔥 - useInitIMSessions ~ getLatestMessageList ~ resp:',
                    resp,
                    robotBizId,
                  );
                  if ((resp.data?.length ?? 0) > 1) {
                    console.log(
                      'leejunhui - 🔥🔥🔥🔥🔥🔥 - useInitIMSessions ~ messages.length > 0, creating new session',
                    );
                    createIMSession().then((session) => {
                      console.log(
                        'leejunhui - 🔥🔥🔥🔥🔥🔥 - useInitIMSessions ~ createIMSession ~ session:',
                        session,
                      );
                      setCurrentSessionId(session.sessionId);
                    });
                  } else {
                    console.log(
                      'leejunhui - 🔥🔥🔥🔥🔥🔥 - useInitIMSessions - messages.length === 0, setting current session to lastSession',
                    );
                    setCurrentSessionId(lastSession.sessionId);
                  }
                });
            }
          } else if (robotBizId === booklnAIRobotBizId.AI_BOOK) {
            if (lastSession?.sessionId) {
              setCurrentSessionId(lastSession.sessionId);
            }
          }
        }

        isFetchingSessionsRef.current = false;
      });
    }
  }, [
    imLoginStatus,
    fetchIMSessions,
    createIMSession,
    setCurrentSessionId,
    robotConf?.robotId,
    robotBizId,
  ]);
};

/**
 * 获取 IM 会话列表的 Hook
 * @returns {Object} 包含获取会话列表方法的对象
 * @property {Function} fetchIMSessions - 获取会话列表的方法
 */
export const useIMSessions = () => {
  const { sessions } = useIMSessionStore();

  return { sessions };
};

/**
 * 创建 IM 会话的 Hook
 *
 * 该 Hook 提供创建新的 IM 会话的功能
 *
 * @returns {Object} 包含创建会话方法的对象
 * @returns {Function} createIMSession - 创建新会话的异步方法
 */
export const useIMCreateSession = (param: {
  robotBizId?: BooklnAIRobotBizIdValueType;
}) => {
  const { robotConf } = useIMRobotConf();
  const { robotBizId = booklnAIRobotBizId.AI_QA } = param;

  /**
   * 创建一个新的 IM 会话
   *
   * @returns {Promise<Session>} 返回新创建的会话对象
   */
  const createIMSession = useCallback(async () => {
    console.log('createIMSession', robotConf);
    const newlyCreatedSession = await container.im().createRobotBizSession({
      sessionType: SessionType.RobotMultiSession,
      to: robotConf?.robotId?.toString() ?? featureToggles.imRobotId(), // 机器人ID
      nick: robotConf?.robotName ?? featureToggles.imRobotNick(), // 机器人昵称
      avatar: robotConf?.robotAvatar ?? featureToggles.imRobotAvatar(), // 机器人头像
      robotBizId,
    });

    return newlyCreatedSession;
  }, [robotConf, robotBizId]);

  return { createIMSession };
};

/**
 * 创建 IM 消息的 Hook
 *
 * 该 Hook 提供创建文本和图片消息的功能
 *
 * @returns {Object} 包含创建消息方法的对象
 * @returns {Function} createIMTextMessage - 创建文本消息的方法
 * @returns {Function} createIMImageMessage - 创建图片消息的方法
 */
export const useIMCreateMessage = (param: {
  robotBizId?: BooklnAIRobotBizIdValueType;
}) => {
  const { robotConf } = useIMRobotConf();
  const { robotBizId = booklnAIRobotBizId.AI_QA } = param;

  /**
   * 创建文本消息
   *
   * @param {string} text - 消息文本内容
   * @returns {Message<TextMessageBody>} 返回客户端消息对象
   */
  const createIMTextMessage = useCallback(
    async (param: {
      text: string;
      sessionId: string;
      bizData?: string;
      skipAIAnswer?: boolean;
    }) => {
      const { text, sessionId, bizData, skipAIAnswer } = param;
      const clientSideMessage = await container.im().createRobotBizMessage<TextMessageBody>({
        contentType: MessageContentType.Text,
        sessionType: SessionType.RobotMultiSession,
        payload: { text: text, skipResponse: skipAIAnswer ? 1 : 0 },
        to: robotConf?.robotId?.toString() ?? featureToggles.imRobotId(), // 发送给机器人
        robotBizId,
        sessionId,
        bizData,
      });

      return clientSideMessage;
    },
    [robotBizId, robotConf?.robotId],
  );

  /**
   * 创建图片消息
   *
   * @param {Object} param - 参数对象
   * @param {IImagePayload} param.imagePayload - 图片载荷对象,包含图片的url、size、width、height等信息
   * @returns {Promise<Message<ImageMessageBody>>} 返回客户端图片消息对象
   */
  const createIMImageMessage = useCallback(
    async (param: {
      imagePayload: IImagePayload;
      sessionId: string;
      bizData?: string;
      skipAIAnswer?: boolean;
    }) => {
      const { imagePayload, sessionId, bizData, skipAIAnswer } = param;
      const clientSideMessage = await container.im().createRobotBizMessage<ImageMessageBody>({
        contentType: MessageContentType.Image,
        sessionType: SessionType.RobotMultiSession,
        payload: { ...imagePayload, skipResponse: skipAIAnswer ? 1 : 0 },
        to: robotConf?.robotId?.toString() ?? featureToggles.imRobotId(), // 发送给机器人
        robotBizId,
        sessionId,
        bizData,
      });

      return clientSideMessage;
    },
    [robotBizId, robotConf?.robotId],
  );

  /**
   * 创建图片文本消息
   *
   * @param {Object} param - 参数对象
   * @param {string} param.text - 文本内容
   * @param {IImagePayload} param.imagePayload - 图片载荷对象,包含图片的url、size、width、height等信息
   * @returns {Promise<Message<ImageTextMessageBody>>} 返回客户端图文消息对象
   */
  const createIMImageAndTextMessage = useCallback(
    async (param: {
      text: string;
      imagePayload: IImagePayload;
      sessionId: string;
      bizData?: string;
      skipAIAnswer?: boolean;
    }) => {
      const { text, imagePayload, sessionId, bizData, skipAIAnswer } = param;
      const clientSideMessage = await container.im().createRobotBizMessage<ImageTextMessageBody>({
        contentType: MessageContentType.ImageText,
        sessionType: SessionType.RobotMultiSession,
        payload: { text, images: [imagePayload], skipResponse: skipAIAnswer ? 1 : 0 },
        to: robotConf?.robotId?.toString() ?? featureToggles.imRobotId(), // 发送给机器人
        robotBizId,
        sessionId,
        bizData,
      });

      return clientSideMessage;
    },
    [robotBizId, robotConf?.robotId],
  );

  /**
   * 创建自定义消息
   *
   * @param {Object} param - 参数对象
   * @param {ICustomPayload} param.payload - 自定义消息载荷对象
   * @param {string} param.sessionId - 会话ID
   * @returns {Promise<Message<CustomMessageBody>>} 返回客户端自定义消息对象
   */
  const createIMCustomMessage = useCallback(
    async (param: {
      payload: ICustomPayload;
      sessionId: string;
    }) => {
      const { payload, sessionId } = param;
      const clientSideMessage = await container.im().createRobotBizMessage<CustomMessageBody>({
        contentType: MessageContentType.Custom,
        sessionType: SessionType.RobotMultiSession,
        payload: payload,
        to: robotConf?.robotId?.toString() ?? featureToggles.imRobotId(), // 发送给机器人
        robotBizId,
        sessionId,
      });

      return clientSideMessage;
    },
    [robotBizId, robotConf?.robotId],
  );

  return {
    createIMTextMessage,
    createIMImageMessage,
    createIMImageAndTextMessage,
    createIMCustomMessage,
  };
};

/**
 * 发送IM消息的Hook
 * 提供发送消息的功能,发送成功后会将消息追加到消息列表中
 * @returns {{sendMessage: (message: IMessage) => Promise<void>}} 返回发送消息的方法
 */
export const useIMSendMessage = () => {
  const { appendMessage } = useIMMessageStore();
  const { robotConf } = useIMRobotConf();

  const sendMessage = useCallback(
    async (message: IMessage) => {
      const response = await container
        .im()
        .sendMessage({ message }, (resp: BizResponse<IMessage>) => {
          if (resp.success && resp.data) {
            appendMessage({
              sessionId: resp.data.sessionId,
              message: resp.data,
              robotConf,
            });
          }
        });
      console.log('leejunhui - 🔥🔥🔥🔥🔥🔥 - useIMSendMessage ~ response:', response);
      if (response.success && response.data) {
        appendMessage({
          sessionId: response.data.sessionId,
          message: response.data,
          robotConf,
        });
      }
    },
    [appendMessage, robotConf],
  );

  return { sendMessage };
};

export const useIMRegenerateMessage = () => {
  const regenerateMessage = useCallback(
    async (param: {
      questionMessage: IMessage;
      currentAnswerMsgId: number;
    }) => {
      const response = await container.im().regenerateMessage(param);
      return response;
    },
    [],
  );

  return { regenerateMessage };
};
/**
 * 追加IM消息的Hook
 * 提供将消息追加到消息列表的功能
 * @returns {{appendMessage: (param: {sessionId: string, message: IMessage}) => void}} 返回追加消息的方法
 */
export const useIMAppendMessage = () => {
  const { appendMessage } = useIMMessageStore();

  return { appendMessage };
};

/**
 * 批量追加消息的Hook
 * 提供将消息批量追加到消息列表的功能
 * @returns {{appendMessages: (param: {sessionId: string, messages: IMessage[]}) => void}} 返回批量追加消息的方法
 */
export const useIMAppendMessages = () => {
  const { appendMessages } = useIMMessageStore();

  return { appendMessages };
};

/**
 * 标记所有接收消息的音频播放状态为已完成的Hook
 * @returns {{markAllReceivedMessagesAudioPlayingStatusFinished: () => void}} 返回标记方法
 */
export const useIMMarkAllReceivedMessagesAudioPlayingStatusFinished = () => {
  const { markAllReceivedMessagesAudioPlayingStatusFinished } = useIMMessageStore();

  return { markAllReceivedMessagesAudioPlayingStatusFinished };
};

export const useIMMarkReceivedMessageAudioPlayingStatusPlayingWithAudioSrc = () => {
  const { markReceivedMessageAudioPlayingStatusPlayingWithAudioSrc } = useIMMessageStore();

  return { markReceivedMessageAudioPlayingStatusPlayingWithAudioSrc };
};

export const useIMMarkTargetMessageAudioStartLoading = () => {
  const { markTargetMessageAudioStartLoading } = useIMMessageStore();

  return { markTargetMessageAudioStartLoading };
};

export const useIMMarkTargetMessageAudioFinishLoading = () => {
  const { markTargetMessageAudioFinishLoading } = useIMMessageStore();

  return { markTargetMessageAudioFinishLoading };
};

/**
 * 获取当前会话的Hook
 * 根据当前会话ID从会话列表中查找并返回当前会话信息
 * @returns {{currentSession: Session | undefined}} 返回当前会话对象
 */
export const useCurrentSession = () => {
  const { sessions, currentSessionId } = useIMSessionStore();

  const currentSession = useMemo(() => {
    return sessions.find((session) => session.sessionId === currentSessionId);
  }, [sessions, currentSessionId]);

  return { currentSession };
};

export const useIMTargetSession = (param: {
  sessionId?: string;
}) => {
  const { sessions } = useIMSessionStore();
  const { sessionId } = param;

  const targetSession = useMemo(() => {
    return sessions.find((session) => session.sessionId === sessionId);
  }, [sessions, sessionId]);

  return { targetSession };
};

/**
 * 获取IM消息列表的Hook
 * 根据当前会话ID获取对应的消息列表
 * @returns {IMessage[]} 返回当前会话的消息列表
 */
export const useIMMessages = (param: { sessionId?: string }) => {
  const { messages } = useIMMessageStore();

  if (param.sessionId) {
    return messages.get(param.sessionId) ?? [];
  }

  return [];
};

/**
 * 生成消息音频的 Hook
 * 用于将文本消息转换为语音
 */
export const useIMGenMessageAudio = () => {
  /**
   * 生成音频的回调函数
   * @param message - IM消息对象
   * @returns 返回音频生成的响应结果
   */
  const genMessageAudio = useCallback(async (message: IMMessage) => {
    const response = await container.im().genAudio({ message });
    return response;
  }, []);

  return { genMessageAudio };
};

/**
 * 检查指定会话是否正在输出消息的 Hook
 * 用于判断机器人是否正在生成回复
 * @param param.sessionId - 会话ID
 */
export const useIMTargetSessionOutputting = (param: {
  sessionId?: string;
}) => {
  // 获取会话的消息列表
  const messages = useIMMessages(param);

  /**
   * 查找是否存在正在输出的消息
   * 条件:
   * 1. 消息方向为输入(In)
   * 2. 消息类型为文本
   * 3. 消息未结束(end=false)
   * 4. 消息为流式消息
   */
  const targetSessionOutputting = useMemo(() => {
    const streamInTextMessage = messages.filter(
      (msg) =>
        msg.flow === MessageFlow.In &&
        msg.contentType === MessageContentType.Text &&
        (msg.payload as ITextPayload).streamType,
    );
    if (streamInTextMessage.length > 0) {
      return !!streamInTextMessage.find((msg) => {
        return (msg.payload as ITextPayload).end === EndType.NotEnd;
      });
    } else {
      return false;
    }

    // return !!messages.find(
    //   (msg) =>
    //     msg.flow === MessageFlow.In &&
    //     msg.contentType === MessageContentType.Text &&
    //     !(msg.payload as ITextPayload).end &&
    //     (msg.payload as ITextPayload).streamType,
    // );
  }, [messages]);

  return { targetSessionOutputting };
};

/**
 * 设置消息临时扩展数据的 Hook
 * 用于为消息添加临时的额外信息
 */
export const useIMSetTmpExtra = () => {
  /**
   * 设置临时扩展数据的回调函数
   * @param param.message - IM消息对象
   * @param param.tmpData - 临时数据
   * @param param.days - 数据保存天数
   * @returns 设置结果的响应
   */
  const setIMSetTmpExtra = useCallback(
    async (param: {
      message: IMessage;
      tmpData: string;
      days: number;
    }) => {
      const response = await container.im().setMessageTempExtra(param);
      return response;
    },
    [],
  );

  return { setIMSetTmpExtra };
};

/**
 * 加载更多历史消息的 Hook
 * 用于分页加载会话的历史消息记录
 */
export const useIMLoadMoreOldMsgs = () => {
  // 获取消息列表状态管理相关方法
  const { setFetchMoreOldMessageListApiState, fetchMoreOldMessageListSuccess } =
    useIMMessageStore();

  /**
   * 加载更多历史消息的回调函数
   * @param param.sessionId - 会话ID
   * @param param.lastMsgId - 最后一条消息ID
   * @param param.pageSize - 每页消息数量
   * @param param.dbCallBack - 数据库缓存回调函数
   */
  const loadMoreOldMsgs = useCallback(
    async (param: {
      sessionId: string;
      lastMsgId: number;
      pageSize?: number;
      dbCallBack?: (msgLength: number) => void;
    }) => {
      const { sessionId, lastMsgId, pageSize = JglAiQAChatMessagePageSize, dbCallBack } = param;
      // 设置加载状态
      setFetchMoreOldMessageListApiState('loading');

      console.log('useIMCoreLogic - loadMoreOldMsgs', param);
      // 调用IM SDK获取消息列表
      const response = await container.im().getMessageList(
        {
          sessionId,
          msgId: lastMsgId,
          pageSize,
          loadType: 'prev',
        },
        // 处理缓存数据的回调
        (resp: BizResponse<IMessage[]>) => {
          if (resp.success && resp.data) {
            console.log(
              'useIMCoreLogic - loadMoreOldMsgs - 拉取更多老的历史消息 - 缓存',
              resp.data,
            );
            dbCallBack?.(resp.data?.length ?? 0);
            fetchMoreOldMessageListSuccess({
              sessionId,
              messages: resp.data,
            });
          }
        },
      );

      // 处理接口返回数据
      if (response.success && response.data) {
        console.log(
          'useIMCoreLogic - loadMoreOldMsgs - 拉取更多老的历史消息 - 接口',
          response.data,
        );
        fetchMoreOldMessageListSuccess({
          sessionId,
          messages: response.data,
        });

        // 设置加载完成状态
        setFetchMoreOldMessageListApiState('success');
        return response.data?.length ?? 0;
      } else {
        // 设置加载完成状态
        setFetchMoreOldMessageListApiState('error');
        return 0;
      }
    },
    [fetchMoreOldMessageListSuccess, setFetchMoreOldMessageListApiState],
  );

  return { loadMoreOldMsgs };
};

export const useIMGenTmpAudioClips = () => {
  const genTmpAudioClips = useCallback(
    async (param: {
      // 待转音频文本
      txt: string;
      // 音频顺序
      order: number;
      // 用户 ID
      userId: number;
      // 业务 ID
      bizId: string;
    }) => {
      const { txt, order, userId, bizId } = param;
      const deviceId = await getUniqueId();
      const request = genTmpAudioClipsRequest({
        appId: envVars.appId(),
        deviceId,
        userId,
        txt,
        order,
        bizId,
      });
      const response = await container.net().fetch(request);

      return response;
    },
    [],
  );

  return { genTmpAudioClips };
};

export const useIMIntentRecognize = () => {
  const { robotConf: intentRecognizeRobotConf } = useIMIntentRecognizeRobotConf();
  const { robotConf } = useIMRobotConf();

  const intentRecognize = useCallback(
    async (param: {
      sessionId: string;
    }): Promise<'default' | 'command'> => {
      const { sessionId } = param;
      const bizId = sessionUtil.bizIdFromSessionId(sessionId);
      const response = await container.im().sendRequest<{ intent: 'default' | 'command' }>({
        path: '/message/intentRecognize.do',
        bizData: {
          bizId,
          msgRobotUserId: robotConf?.robotId?.toString(),
          robotUserId: intentRecognizeRobotConf?.robotId?.toString(),
          bizCode: 'intentRecognize',
        },
      });
      if (response.success && response.data?.intent) {
        return response.data.intent;
      } else {
        return 'default';
      }
    },
    [robotConf?.robotId, intentRecognizeRobotConf?.robotId],
  );

  const commandIntentRecognize = useCallback(
    async (param: {
      sessionId: string;
    }): Promise<string | undefined> => {
      const { sessionId } = param;
      const bizId = sessionUtil.bizIdFromSessionId(sessionId);
      const response = await container.im().sendRequest<{ intent: string }>({
        path: '/message/intentRecognize.do',
        bizData: {
          bizId,
          robotUserId: robotConf?.robotId?.toString(),
          bizCode: 'commandIntentRecognize',
        },
      });
      if (response.success && response.data?.intent) {
        return response.data.intent;
      } else {
        return undefined;
      }
    },
    [robotConf?.robotId],
  );

  return { intentRecognize, commandIntentRecognize };
};

export const useIMUpdateTargetMessageAudioPlayingStatus = () => {
  const { updateTargetMessageAudioPlayingStatus } = useIMMessageStore();

  return { updateTargetMessageAudioPlayingStatus };
};

export const useIMDeleteTargetMessage = () => {
  const { deleteTargetMessage } = useIMMessageStore();

  return { deleteTargetMessage };
};

export const useIMSendGuideMsg = () => {
  const sendGuideMsg = useCallback(
    async (param: {
      sessionId: string;
      robotUserId: string;
    }) => {
      const { sessionId, robotUserId } = param;
      const bizId = sessionUtil.bizIdFromSessionId(sessionId);
      const response = await container.im().sendRequest<boolean>({
        path: '/aiProductMessage/sendGuideMsg.do',
        bizData: {
          appName: 'app',
          bizId,
          robotUserId,
        },
      });
      return response.data;
    },
    [],
  );

  return {
    sendGuideMsg,
  };
};

export const useIMSendGuideFollowUpMsg = () => {
  return {
    sendGuideFollowUpMsg,
  };
};

export const useIMInitEventBus = () => {
  useIMListenToCurrentSessionFetchLatestMessagesSuccess();
  useIMListenToMessageNeedFollowAsk();
  useIMListenToGuideMessageNeedFollowAsk();
  useIMListenToOnFollowAskMessagePress();
  useIMListenToOnDeleteFollowAskMessage();
};

export const useIMListenToCurrentSessionFetchLatestMessagesSuccess = () => {
  const imLoginStatus = useAtomValue(imLoginStatusAtom);
  const { agentConf } = useIMAgentConf();
  const { sendGuideMsg } = useIMSendGuideMsg();
  const { robotConf } = useIMRobotConf();

  const handler = useCallback(
    async (param: { sessionId: string; messages: IMMessage[] }) => {
      console.log(
        'useIMListenToCurrentSessionFetchLatestMessagesSuccess - 开始检查是否需要发送开场白消息',
        {
          messageLength: param.messages.length,
          guideSwitch: agentConf?.guideSwitch,
          sessionId: param.sessionId,
          robotId: robotConf?.robotId,
        },
      );
      if (param.messages.length === 0) {
        console.log(
          'useIMListenToCurrentSessionFetchLatestMessagesSuccess - 消息列表为空,准备发送开场白',
        );
        if (agentConf?.guideSwitch) {
          console.log(
            'useIMListenToCurrentSessionFetchLatestMessagesSuccess - 开场白开关已开启,开始发送',
          );
          const sendGuideMsgResult = await sendGuideMsg({
            sessionId: param.sessionId,
            robotUserId: robotConf?.robotId?.toString() ?? '',
          });
          if (sendGuideMsgResult) {
            console.log(
              'useIMListenToCurrentSessionFetchLatestMessagesSuccess - 发送开场白消息成功',
              param,
            );
          } else {
            console.log(
              'useIMListenToCurrentSessionFetchLatestMessagesSuccess - 发送开场白消息失败',
            );
          }
        } else {
          console.log(
            'useIMListenToCurrentSessionFetchLatestMessagesSuccess - 开场白开关未开启,跳过发送',
          );
        }
      } else {
        console.log(
          'useIMListenToCurrentSessionFetchLatestMessagesSuccess - 消息列表不为空,跳过发送开场白',
        );
        if (param.messages.length === 1) {
          console.log(
            'useIMListenToCurrentSessionFetchLatestMessagesSuccess - 检查是否需要发送追问消息',
            {
              messages: param.messages,
            },
          );
          const targetMessage = param.messages[0];
          if (
            targetMessage &&
            targetMessage.flow === MessageFlow.In &&
            !targetMessage?.followAskMessages
          ) {
            console.log(
              'useIMListenToCurrentSessionFetchLatestMessagesSuccess - 开始发送追问消息',
              {
                sessionId: targetMessage.sessionId,
                message: targetMessage,
              },
            );
            eventBus.emit('guideMessageNeedFollowAsk', {
              sessionId: targetMessage.sessionId,
              message: { ...targetMessage },
            });
          } else {
            console.log(
              'useIMListenToCurrentSessionFetchLatestMessagesSuccess - 跳过发送追问消息',
              {
                hasFollowAskMessages: !!targetMessage?.followAskMessages,
              },
            );
          }
        }
      }
    },
    [agentConf?.guideSwitch, robotConf?.robotId, sendGuideMsg],
  );

  useEffect(() => {
    if (imLoginStatus === 'loggedIn') {
      eventBus.off('currentSessionFetchLatestMessagesSuccess', handler);
      eventBus.on('currentSessionFetchLatestMessagesSuccess', handler);
    }
    return () => {
      eventBus.off('currentSessionFetchLatestMessagesSuccess', handler);
    };
  }, [handler, imLoginStatus]);
};

export const useIMListenToMessageNeedFollowAsk = () => {
  const imLoginStatus = useAtomValue(imLoginStatusAtom);
  const { agentConf } = useIMAgentConf();
  const { robotConf } = useIMRobotConf();
  const { appendMessage } = useIMAppendMessage();

  const handler = useCallback(
    async (param: { sessionId: string; message: IMMessage }) => {
      const { sessionId, message } = param;
      // 从 imAgentConfAtom 中获取是否要发送开场白追问消息
      if (agentConf?.followAskSwitch) {
        const response = await sendFollowAskMsg({
          sessionId,
          robotUserId: robotConf?.robotId?.toString() ?? '',
          lastMsgId: message.msgId,
        });
        if (response?.followAskMessages) {
          const followAskMessages: string[] = JSON.parse(response.followAskMessages);
          message.followAskMessages = followAskMessages;
          appendMessage({
            sessionId,
            message: { ...message },
            robotConf,
          });
        }
      }
    },
    [agentConf?.followAskSwitch, appendMessage, robotConf],
  );

  useEffect(() => {
    if (imLoginStatus === 'loggedIn') {
      eventBus.off('messageNeedFollowAsk', handler);
      eventBus.on('messageNeedFollowAsk', handler);
    }
    return () => {
      eventBus.off('messageNeedFollowAsk', handler);
    };
  }, [handler, imLoginStatus]);
};

export const useIMListenToGuideMessageNeedFollowAsk = () => {
  const imLoginStatus = useAtomValue(imLoginStatusAtom);
  const { agentConf } = useIMAgentConf();
  const { robotConf } = useIMRobotConf();
  const { appendMessage } = useIMAppendMessage();

  const handler = useCallback(
    async (param: { sessionId: string; message: IMMessage }) => {
      const { sessionId, message } = param;
      console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ handler ~ param:', param);
      console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ handler ~ agentConf:', agentConf);
      // 从 imAgentConfAtom 中获取是否要发送开场白追问消息
      if (agentConf?.guideSwitch) {
        const sendGuideFollowUpMsgResponse = await sendGuideFollowUpMsg({
          sessionId,
          robotUserId: robotConf?.robotId?.toString() ?? '',
          lastMsgId: 1,
        });
        console.log(
          '🚀🚀🚀🚀🚀🚀 - leejunhui ~ handler ~ sendGuideFollowUpMsgResponse:',
          sendGuideFollowUpMsgResponse,
        );
        if (sendGuideFollowUpMsgResponse?.followAskMessages) {
          const followAskMessages: string[] = JSON.parse(
            sendGuideFollowUpMsgResponse.followAskMessages,
          );
          console.log('🚀🚀🚀🚀🚀🚀 - leejunhui ~ handler ~ followAskMessages:', followAskMessages);
          message.followAskMessages = followAskMessages;
          appendMessage({
            sessionId,
            message: { ...message },
            robotConf,
          });
        }
      }
    },
    [agentConf, appendMessage, robotConf],
  );

  useEffect(() => {
    if (imLoginStatus === 'loggedIn') {
      eventBus.off('guideMessageNeedFollowAsk', handler);
      eventBus.on('guideMessageNeedFollowAsk', handler);
    }
    return () => {
      eventBus.off('guideMessageNeedFollowAsk', handler);
    };
  }, [handler, imLoginStatus]);
};

export const useIMListenToOnFollowAskMessagePress = () => {
  const imLoginStatus = useAtomValue(imLoginStatusAtom);
  const { robotConf } = useIMRobotConf();
  const { appendMessage } = useIMAppendMessage();

  const handler = useCallback(
    async (param: { sessionId: string; message: IMMessage }) => {
      const { sessionId, message } = param;
      appendMessage({
        sessionId,
        message: { ...message, followAskMessages: [] },
        robotConf,
      });
    },
    [appendMessage, robotConf],
  );

  useEffect(() => {
    if (imLoginStatus === 'loggedIn') {
      eventBus.off('onFollowAskMessagePress', handler);
      eventBus.on('onFollowAskMessagePress', handler);
    }
    return () => {
      eventBus.off('onFollowAskMessagePress', handler);
    };
  }, [handler, imLoginStatus]);
};

export const useIMListenToOnDeleteFollowAskMessage = () => {
  const imLoginStatus = useAtomValue(imLoginStatusAtom);
  const { deleteTargetSessionAllFollowAskMessages } = useIMMessageStore();

  const handler = useCallback(
    async (param: { sessionId: string }) => {
      const { sessionId } = param;
      deleteTargetSessionAllFollowAskMessages({
        sessionId,
      });
    },
    [deleteTargetSessionAllFollowAskMessages],
  );

  useEffect(() => {
    if (imLoginStatus === 'loggedIn') {
      eventBus.off('onDeleteFollowAskMessage', handler);
      eventBus.on('onDeleteFollowAskMessage', handler);
    }
    return () => {
      eventBus.off('onDeleteFollowAskMessage', handler);
    };
  }, [handler, imLoginStatus]);
};

export const useIMDeleteMessage = () => {
  const { robotConf } = useIMRobotConf();
  const { deleteTargetMessage } = useIMMessageStore();
  const deleteIMMessage = useCallback(
    async (param: {
      sessionId: string;
      msgId: number;
      id: string;
    }) => {
      const { sessionId, msgId, id } = param;
      const response = await container.im().deleteMessage({
        sessionId,
        msgId,
        id,
        robotUserId: robotConf?.robotId,
        sync: true,
      });
      if (response.success) {
        deleteTargetMessage({
          sessionId,
          id,
        });
      }
      return response.data;
    },
    [deleteTargetMessage, robotConf?.robotId],
  );

  return {
    deleteIMMessage,
  };
};

export const useIMClearSession = () => {
  const { clearTargetSessionMessages } = useIMMessageStore();
  const clearSession = useCallback(
    async (param: {
      sessionId: string;
    }) => {
      const { sessionId } = param;
      const response = await container.im().clearSession({
        sessionId,
      });
      clearTargetSessionMessages({
        sessionId,
      });
      return response.data;
    },
    [clearTargetSessionMessages],
  );

  return {
    clearSession,
  };
};
