import { routerMap } from '@jgl/biz-func';
import { router } from '@jgl/utils';
import { useCallback, useRef } from 'react';
import { TouchableWithoutFeedback } from 'react-native';
import { Text, View } from 'tamagui';

/**
 * 调试选项入口
 */
export const DebugSettingsButton = () => {
  const { handlePressDebugSettings } = usePressDebugSettings();
  return (
    <TouchableWithoutFeedback onPress={handlePressDebugSettings}>
      <View className={'h-10 w-10 items-center justify-center'}>
        {__DEV__ ? <Text>{'调试\n信息'}</Text> : null}
      </View>
    </TouchableWithoutFeedback>
  );
};

const usePressDebugSettings = () => {
  const requiredCount = 5;
  const requiredDuration = 2000;

  const presses = useRef<number[]>([]);
  const handlePressDebugSettings = useCallback(() => {
    if (__DEV__) {
      router.push(routerMap.debug);
    } else {
      const timestamp = Date.now();
      presses.current.push(timestamp);

      if (presses.current.length >= requiredCount) {
        const lastPresses = presses.current.slice(-requiredCount);
        const first = lastPresses[0];
        const last = lastPresses[lastPresses.length - 1];
        if (first && last) {
          const duration = last - first;
          const isFastEnough = duration <= requiredDuration;
          if (isFastEnough) {
            router.push(routerMap.debug);
          }
        }
      }
    }
  }, []);

  return { handlePressDebugSettings };
};
