import { container } from '@jgl/container';
import { im, type ITextPayload, JGL_COMMAND, type Message, MessageEnd } from '@jgl/im';
import type { ConnectionResponse } from '@jgl/im/src/types/types';
import { getAppVid, parsePayload } from '@jgl/utils';
import { useCallback, useEffect, useRef } from 'react';

type UseImResult = Record<
  number,
  {
    /**
     * 输出内容
     */
    content: string;

    /**
     *  内容输出状态
     */
    outputStatus: 'outputting' | 'end' | 'fail';
  }
>;

/**
 * 获取im的输出结果
 */
export const useGetImResultObject = () => {
  const markdownResultRef = useRef<string | undefined>(undefined);

  const isEndRef = useRef<boolean | undefined>(undefined);

  const resultObjectRef = useRef<UseImResult>({});

  const messageMapRef = useRef<Map<number, string>>(new Map());

  /**
   * 长连接连接成功回调
   */
  const handleIMOpen = useCallback(async () => {
    isEndRef.current = false;
  }, []);

  /**
   * 长连接关闭回调
   */
  const handleIMClose = useCallback(() => {
    isEndRef.current = true;
  }, []);

  /**
   * 关闭监听
   */
  const closeIm = useCallback(() => {
    im.off('Open', handleIMOpen);
    im.off('Close', handleIMClose);
  }, [handleIMClose, handleIMOpen]);

  /**
   * 处理长连接返回的消息
   */
  const handleIMMessage = useCallback(
    async (pushData: ConnectionResponse<unknown>) => {
      if (pushData.cmd !== JGL_COMMAND) {
        return;
      }
      const message = pushData.data.data as Message;
      const messagePayload = parsePayload(message.payload);
      const messageId = message.id;
      if (messageId) {
        const oldMessageContent = messageMapRef.current.get(messageId) ?? '';
        const messageContent = (messagePayload as ITextPayload).text;

        messageMapRef.current.set(messageId, oldMessageContent + messageContent);
        const totalContent = messageMapRef.current.get(messageId) ?? '';
        markdownResultRef.current = totalContent;
        const resultObject = resultObjectRef.current;
        if (!resultObject[messageId] || resultObject[messageId]?.outputStatus === 'outputting') {
          resultObjectRef.current = {
            ...resultObject,
            [messageId]: {
              content: totalContent,
              outputStatus: 'outputting',
            },
          };
        }

        if (message.end === MessageEnd.IsEnd) {
          resultObjectRef.current = {
            ...resultObject,
            [messageId]: {
              content: totalContent,
              outputStatus: 'end',
            },
          };
          isEndRef.current = true;
          closeIm();
        } else if (message.end === MessageEnd.IsError) {
          resultObjectRef.current = {
            ...resultObject,
            [messageId]: {
              content: totalContent,
              outputStatus: 'fail',
            },
          };
        }
      }
    },
    [closeIm],
  );

  /**
   * 清除结果
   */
  const clearResult = useCallback(() => {
    markdownResultRef.current = undefined;
  }, []);

  /**
   * 打开Im的监听
   */
  const openIm = useCallback(() => {
    isEndRef.current = false;
    im.on('Open', handleIMOpen);
    im.on('Close', handleIMClose);
    im.on('Message', handleIMMessage);
  }, [handleIMClose, handleIMMessage, handleIMOpen]);

  const handleConnect = useCallback(() => {
    im.reconnectIfNeeded();
  }, []);

  useEffect(() => {
    try {
      im.init({ vid: Number(getAppVid()), net: container.net() });
    } catch (error) {
      console.log('error: - im - ', error);
    }
  }, []);

  useEffect(() => {
    openIm();
  }, [openIm]);

  const getLastResultObject = useCallback(() => {
    return resultObjectRef.current;
  }, []);

  const getIsEnd = useCallback(() => {
    return isEndRef.current;
  }, []);

  const getMarkdownResult = useCallback(() => {
    return markdownResultRef.current;
  }, []);

  return {
    getMarkdownResult,
    getIsEnd,
    messageMapRef,
    getLastResultObject,
    handleConnect,
    closeIm,
    clearResult,
    openIm,
  };
};
