import { showToast } from '@jgl/utils';
import { mobileRegexp } from '@yunti-private/utils-universal';

/**
 * 检查手机号，如果不正确就toast提示
 */
const checkPhoneNumber = (phoneNumber: string | undefined): string | undefined => {
  const trimmed = phoneNumber?.trim();
  if (trimmed && trimmed.length > 0) {
    if (trimmed.match(mobileRegexp)) {
      return trimmed;
    } else {
      showToast({ title: '请检查手机号格式' });
    }
  } else {
    showToast({ title: '请输入手机号' });
  }
  return undefined;
};

/**
 * 检查验证码，如果不正确就toast提示
 */
const checkSmsCode = (smsCode: string | undefined): string | undefined => {
  const trimmed = smsCode?.trim();
  if (trimmed && trimmed.length > 0) {
    return trimmed;
  } else {
    showToast({ title: '请输入验证码' });
  }
  return undefined;
};

export const useCheckUtil = () => {
  return { checkPhoneNumber, checkSmsCode };
};
